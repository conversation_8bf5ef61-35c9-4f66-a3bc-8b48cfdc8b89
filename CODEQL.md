# CodeQL Security Analysis for HeartSync v2

This document describes the CodeQL security analysis setup for HeartSync v2, including both local development and CI/CD integration.

## Overview

CodeQL is GitHub's semantic code analysis engine that helps identify security vulnerabilities and coding errors. Our setup includes:

- **Automated CI/CD Analysis**: Runs on every push and pull request
- **Local Development Analysis**: Run security scans during development
- **Custom Security Queries**: Tailored for Node.js/API security concerns
- **Comprehensive Reporting**: Both machine-readable (SARIF) and human-readable formats

## Quick Start

### Run CodeQL Analysis Locally

```bash
# Install and run CodeQL analysis
npm run security:codeql

# Clean up previous results
npm run security:codeql:cleanup
```

### View Results

Results are saved to `codeql-results/` directory:
- `security-results.sarif` - Standard security analysis (SARIF format)
- `custom-results.sarif` - Custom queries results (SARIF format)
- `security-report.csv` - Human-readable CSV report

## Features

### 1. GitHub Actions Integration

The CodeQL workflow (`.github/workflows/codeql.yml`) automatically runs:
- On push to `main` and `develop` branches
- On pull requests to `main`
- Weekly scheduled scans (Sundays at 2 AM UTC)

### 2. Custom Security Queries

We've created custom CodeQL queries specifically for Node.js applications:

#### API Key Exposure Detection
- **File**: `.github/codeql/custom-queries/api-key-exposure.ql`
- **Purpose**: Detects hardcoded API keys, secrets, and tokens
- **Severity**: Critical
- **CWE**: CWE-798 (Use of Hard-coded Credentials)

#### Unsafe HTTP Request Handling
- **File**: `.github/codeql/custom-queries/unsafe-http-handling.ql`
- **Purpose**: Identifies HTTP handlers that don't validate input
- **Severity**: Warning
- **CWE**: CWE-20 (Improper Input Validation)

### 3. Local Development Support

The local analysis script (`scripts/codeql-local.sh`) provides:
- Automatic CodeQL CLI installation
- Database creation and analysis
- Multiple output formats
- Easy cleanup

## Configuration

### CodeQL Configuration (`.github/codeql/codeql-config.yml`)

```yaml
# Security-focused query suites
queries:
  - security-extended
  - security-and-quality

# Analyzed paths
paths:
  - src
  - scripts
  - "*.js"
  - "*.ts"

# Ignored paths
paths-ignore:
  - node_modules
  - "**/*.test.js"
  - "**/*.backup"
```

### GitHub Actions Workflow

Two analysis jobs:
1. **Standard Analysis**: Uses GitHub's default security queries
2. **Custom Analysis**: Runs our custom security queries

Both jobs:
- Support Node.js 18+ (matching our project requirements)
- Install dependencies with `npm ci`
- Generate SARIF results for GitHub Security tab
- Run on multiple triggers (push, PR, schedule)

## Usage

### Local Development

1. **Quick Analysis**:
   ```bash
   npm run security:codeql
   ```

2. **View Results**:
   ```bash
   # Open CSV report
   open codeql-results/security-report.csv
   
   # View SARIF in VS Code (requires CodeQL extension)
   code codeql-results/security-results.sarif
   ```

3. **Cleanup**:
   ```bash
   npm run security:codeql:cleanup
   ```

### CI/CD Integration

CodeQL analysis runs automatically on:
- Every push to `main` or `develop`
- Every pull request to `main`
- Weekly security scans

Results are available in:
- GitHub Security tab
- Pull request checks
- Action run logs

## Interpreting Results

### Severity Levels

- **Critical/Error**: Security vulnerabilities that need immediate attention
- **Warning**: Potential security issues or code quality problems
- **Note**: Informational findings

### Common Findings

1. **API Key Exposure**: Hardcoded secrets in source code
2. **Input Validation**: Missing validation in HTTP handlers
3. **Path Traversal**: Unsafe file system operations
4. **SQL Injection**: Unsafe database queries
5. **XSS**: Cross-site scripting vulnerabilities

## Customization

### Adding Custom Queries

1. Create a new `.ql` file in `.github/codeql/custom-queries/`
2. Follow CodeQL query structure:
   ```ql
   /**
    * @name Query Name
    * @description Query description
    * @kind problem
    * @problem.severity error|warning|note
    * @security-severity 1.0-10.0
    * @precision high|medium|low
    * @id js/custom-query-id
    * @tags security external/cwe/cwe-XXX
    */
   
   import javascript
   
   // Query logic here
   ```

### Modifying Configuration

Edit `.github/codeql/codeql-config.yml` to:
- Add/remove query suites
- Change analyzed paths
- Modify ignored patterns
- Add custom query packs

## Integration with Existing Security Tools

CodeQL complements our existing security tools:

- **npm audit**: Dependency vulnerability scanning
- **Biome**: Code linting and formatting
- **Custom security scanner**: General security best practices

Combined security command:
```bash
npm run security:scan && npm run security:codeql
```

## Troubleshooting

### Common Issues

1. **Local setup fails**: Ensure curl, unzip, and git are installed
2. **Database creation fails**: Check Node.js version and npm dependencies
3. **Analysis timeout**: Large codebases may need increased timeout limits
4. **Missing results**: Verify query syntax and paths in configuration

### Debug Local Analysis

```bash
# Run with verbose output
./scripts/codeql-local.sh --help

# Check CodeQL installation
~/.codeql/codeql version

# Verify database
~/.codeql/codeql database info codeql-results/heartsync-db
```

## Performance Considerations

- **Local analysis**: 2-5 minutes depending on codebase size
- **CI/CD analysis**: 3-8 minutes in GitHub Actions
- **Database size**: ~50-100MB for typical Node.js projects
- **Memory usage**: 1-2GB during analysis

## Best Practices

1. **Regular Scans**: Run locally before committing sensitive changes
2. **Fix Critical Issues**: Address all critical/error findings immediately
3. **Review Warnings**: Evaluate and fix warning-level issues
4. **Custom Queries**: Add project-specific security queries as needed
5. **Documentation**: Keep security findings and fixes documented

## Resources

- [CodeQL Documentation](https://codeql.github.com/docs/)
- [CodeQL for JavaScript](https://codeql.github.com/docs/codeql-language-guides/codeql-for-javascript/)
- [Writing Custom Queries](https://codeql.github.com/docs/writing-codeql-queries/)
- [GitHub Security Features](https://docs.github.com/en/code-security)

## Support

For issues with CodeQL setup or analysis:
1. Check the troubleshooting section above
2. Review GitHub Actions logs
3. Consult CodeQL documentation
4. File an issue in the project repository
