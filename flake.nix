{
  description = "HeartSync v2 - AI-powered social connection platform";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
      in
      {
        devShells.default = pkgs.mkShell {
          buildInputs = with pkgs; [
            # Node.js 開發環境
            nodejs_20
            npm-check-updates
            
            # TypeScript 和 linting 工具
            nodePackages.typescript
            nodePackages.typescript-language-server
            
            # Git 和版本控制
            git
            gh
            
            # 開發工具
            jq
            curl
            wget
            
            # 系統工具
            coreutils
            findutils
            gnugrep
            gnused
            
            # 文檔工具
            pandoc
            
            # 環境變數管理
            direnv
          ];

          shellHook = ''
            echo "🚀 HeartSync v2 開發環境已啟動"
            echo "Node.js 版本: $(node --version)"
            echo "NPM 版本: $(npm --version)"
            echo "Nix 版本: $(nix --version)"
            echo ""
            echo "可用的工具:"
            echo "  - Node.js: $(which node)"
            echo "  - NPM: $(which npm)"
            echo "  - Wrangler: $(which wrangler)"
            echo "  - TypeScript: $(which tsc)"
            echo "  - Git: $(which git)"
            echo ""
            echo "💡 提示: 執行 'npm run dev' 來啟動開發服務器"
            echo "📖 文檔: https://nixos.org/manual/nix/stable/"
            
            # 確保環境變數正確設置
            export PATH="$PWD/node_modules/.bin:$PATH"
            
            # 設置 Node.js 環境
            export NODE_ENV=development
            
            # 如果 .env 文件存在，載入它
            if [ -f .env ]; then
              export $(cat .env | xargs)
            fi
            
            # 檢查重要的依賴
            if [ ! -d "node_modules" ]; then
              echo "⚠️  node_modules 不存在，建議執行 'npm install'"
            fi
          '';

          # 環境變數
          NIX_ENFORCE_PURITY = 0;
          
          # Node.js 相關環境變數
          NODE_OPTIONS = "--max-old-space-size=4096";
          
          # 開發環境特定設置
          ENVIRONMENT = "development";
        };

        # 提供一些有用的 Nix 腳本
        packages.update-deps = pkgs.writeShellScriptBin "update-deps" ''
          echo "🔄 更新 Node.js 依賴..."
          npm update
          echo "✅ 依賴更新完成"
        '';

        packages.clean-env = pkgs.writeShellScriptBin "clean-env" ''
          echo "🧹 清理開發環境..."
          rm -rf node_modules
          rm -f package-lock.json
          echo "✅ 環境清理完成，請執行 'npm install' 重新安裝依賴"
        '';

        packages.dev-status = pkgs.writeShellScriptBin "dev-status" ''
          echo "📊 HeartSync v2 開發環境狀態"
          echo "================================"
          echo "Node.js: $(node --version)"
          echo "NPM: $(npm --version)"
          echo "Nix: $(nix --version)"
          echo "Git: $(git --version)"
          echo ""
          echo "當前目錄: $(pwd)"
          echo "Git 狀態:"
          git status --porcelain | head -10
          echo ""
          echo "NPM 腳本："
          npm run | grep -E "^\s+(dev|start|test|build|deploy)" | head -10
        '';
      });
}
