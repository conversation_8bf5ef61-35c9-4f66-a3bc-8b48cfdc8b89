# 從本機 /heartsync-v2 復原正常檔案的腳本

param(
    [Parameter(Mandatory=$false)]
    [string]$LocalPath = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun = $false
)

Write-Host "🔄 HeartSync v2 檔案復原腳本" -ForegroundColor Cyan
Write-Host "目標: 從本機復原正常的路由檔案" -ForegroundColor Yellow

# 自動尋找本機 heartsync-v2 路徑
if ($LocalPath -eq "") {
    $PossiblePaths = @(
        "C:\heartsync-v2",
        "C:\Users\<USER>\heartsync-v2",
        "D:\heartsync-v2",
        "C:\Projects\heartsync-v2",
        "C:\Dev\heartsync-v2"
    )
    
    foreach ($path in $PossiblePaths) {
        if (Test-Path $path) {
            $LocalPath = $path
            break
        }
    }
}

if ($LocalPath -eq "" -or -not (Test-Path $LocalPath)) {
    Write-Host "❌ 找不到本機 heartsync-v2 目錄" -ForegroundColor Red
    Write-Host "請使用 -LocalPath 參數指定正確路徑" -ForegroundColor Yellow
    Write-Host "例如: .\restore-from-local.ps1 -LocalPath 'C:\your\path\heartsync-v2'" -ForegroundColor White
    exit 1
}

Write-Host "✅ 找到本機目錄: $LocalPath" -ForegroundColor Green

if ($DryRun) {
    Write-Host "⚠️  DRY RUN 模式 - 不會實際複製檔案" -ForegroundColor Magenta
}

# 需要復原的檔案列表
$FilesToRestore = @(
    @{
        Source = "src/index.js"
        Target = "src/index.js"
        Critical = $true
        Description = "主要路由定義檔案"
    },
    @{
        Source = "package.json"
        Target = "package.json"
        Critical = $false
        Description = "專案配置檔案"
    },
    @{
        Source = "package-lock.json"
        Target = "package-lock.json"
        Critical = $false
        Description = "依賴鎖定檔案"
    }
)

Write-Host "`n📋 檔案復原計劃:" -ForegroundColor Cyan

foreach ($file in $FilesToRestore) {
    $sourcePath = Join-Path $LocalPath $file.Source
    $targetPath = $file.Target
    
    Write-Host "`n📄 $($file.Description)" -ForegroundColor White
    Write-Host "   來源: $sourcePath" -ForegroundColor Gray
    Write-Host "   目標: $targetPath" -ForegroundColor Gray
    
    if (Test-Path $sourcePath) {
        Write-Host "   ✅ 來源檔案存在" -ForegroundColor Green
        
        if (Test-Path $targetPath) {
            $backupPath = "$targetPath.backup-$(Get-Date -Format 'yyyyMMdd-HHmm')"
            Write-Host "   📋 將備份現有檔案為: $backupPath" -ForegroundColor Yellow
            
            if (-not $DryRun) {
                Copy-Item $targetPath $backupPath
            }
        }
        
        if (-not $DryRun) {
            Copy-Item $sourcePath $targetPath -Force
            Write-Host "   ✅ 檔案已復原" -ForegroundColor Green
        } else {
            Write-Host "   🔄 (DRY RUN) 將會復原此檔案" -ForegroundColor Magenta
        }
    } else {
        if ($file.Critical) {
            Write-Host "   ❌ 關鍵檔案不存在!" -ForegroundColor Red
        } else {
            Write-Host "   ⚠️  檔案不存在 (非關鍵)" -ForegroundColor Yellow
        }
    }
}

# 檢查路由定義
Write-Host "`n🔍 檢查路由定義..." -ForegroundColor Cyan
$sourceIndexPath = Join-Path $LocalPath "src/index.js"

if (Test-Path $sourceIndexPath) {
    $content = Get-Content $sourceIndexPath -Raw
    
    $routesToCheck = @("database-test", "chat-alpine")
    foreach ($route in $routesToCheck) {
        if ($content -match $route) {
            Write-Host "   ✅ 找到 /$route 路由定義" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 未找到 /$route 路由定義" -ForegroundColor Red
        }
    }
} else {
    Write-Host "   ❌ 無法檢查來源檔案" -ForegroundColor Red
}

# 提供下一步指示
Write-Host "`n🎯 復原完成後的步驟:" -ForegroundColor Green
Write-Host "1. npm install (如果 package.json 有變更)" -ForegroundColor White
Write-Host "2. npm start" -ForegroundColor White
Write-Host "3. 測試路由:" -ForegroundColor White
Write-Host "   - http://localhost:3000/database-test" -ForegroundColor Gray
Write-Host "   - http://localhost:3000/chat-alpine" -ForegroundColor Gray
Write-Host "   - http://localhost:3000/CLI-preview" -ForegroundColor Gray
Write-Host "4. 確認所有功能正常" -ForegroundColor White

if ($DryRun) {
    Write-Host "`n⚠️  這是 DRY RUN - 沒有實際複製檔案" -ForegroundColor Magenta
    Write-Host "   移除 -DryRun 參數來執行實際復原" -ForegroundColor White
} else {
    Write-Host "`n✅ 檔案復原完成!" -ForegroundColor Green
    Write-Host "   請按照上述步驟進行測試" -ForegroundColor White
}
