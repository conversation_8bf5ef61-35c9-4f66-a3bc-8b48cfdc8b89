{"compilerOptions": {"lib": ["deno.window"], "strict": true}, "tasks": {"dev": "deno run --allow-net --allow-read --allow-env --allow-write src/index.js", "start": "deno run --allow-net --allow-read --allow-env --allow-write src/index.js", "deno-server": "deno run --allow-net --allow-read --allow-env --allow-write deno-server.ts", "deno-dev": "deno run --allow-net --allow-read --allow-env --allow-write --watch deno-server.ts", "deno-persistence": "deno run --allow-net --allow-read --allow-env --allow-write deno-persistence-server.ts", "deno-persistence-dev": "deno run --allow-net --allow-read --allow-env --allow-write --watch deno-persistence-server.ts", "deno-chat-pure": "deno run --allow-net --allow-read --allow-env --allow-write deno-chat-pure.ts", "deno-chat-pure-dev": "deno run --allow-net --allow-read --allow-env --allow-write --watch deno-chat-pure.ts", "deno-chat-simple": "deno run --allow-net --allow-read --allow-env --allow-write deno-chat-simple.ts", "deno-chat-simple-dev": "deno run --allow-net --allow-read --allow-env --allow-write --watch deno-chat-simple.ts", "lint": "deno lint src/", "fmt": "deno fmt src/", "check": "deno check src/index.js", "check:deno": "deno check deno-server.ts", "test": "deno test --allow-net --allow-read --allow-env", "health": "echo 'Deno health check passed'", "diagnose:claude": "echo 'Deno <PERSON> diagnostic'"}, "imports": {"hono": "jsr:@hono/hono@^4", "hono/": "jsr:@hono/hono@^4/", "@hono/node-server": "npm:@hono/node-server@^1.16.0", "dotenv": "https://deno.land/std@0.224.0/dotenv/mod.ts", "anthropic": "npm:@anthropic-ai/sdk@^0.57.0"}, "exclude": ["node_modules/", "dist/", "build/", ".wrangler/", "backups/", "playwright-report/"], "fmt": {"useTabs": true, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "singleQuote": true, "proseWrap": "preserve", "include": ["src/"], "exclude": ["node_modules/", "dist/", "build/"]}, "lint": {"include": ["src/"], "exclude": ["node_modules/", "dist/", "build/"], "rules": {"tags": ["recommended"], "include": ["ban-untagged-todo"], "exclude": ["no-unused-vars"]}}}