#!/bin/bash
# hsn-terminal-chat.sh
# 謎之CLI 終端聊天助理

echo "🤖 謎之CLI 終端助理已啟動"
echo "💡 輸入 'exit' 結束對話"
echo "📚 輸入 'help' 查看可用指令"
echo "─────────────────────────────"

# 檢查依賴
if ! command -v http &> /dev/null; then
    echo "❌ 錯誤：需要安裝 httpie"
    echo "💡 安裝：brew install httpie"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    echo "❌ 錯誤：需要安裝 jq"
    echo "💡 安裝：brew install jq"
    exit 1
fi

# HSN API 端點
HSN_API="http://localhost:8787/api/hsn/chat"
while true; do
    echo -n "你 > "
    read user_input
    
    case $user_input in
        "exit"|"quit"|"bye")
            echo "👋 再見！期待下次知識探索！"
            break
            ;;
        "help")
            echo "📚 可用指令："
            echo "  exit/quit/bye - 結束對話"
            echo "  help - 顯示此幫助"
            echo "  任何問題 - 開始知識探索"
            ;;
        "")
            # 空輸入，忽略
            continue
            ;;
        *)
            echo -n "🤖 思考中..."
            
            # 調用 HSN API
            response=$(http POST $HSN_API \
                message="$user_input" \
                conversation_id="terminal-session-$(date +%s)" \
                --json --timeout=30 2>/dev/null)
            
            if [ $? -eq 0 ]; then
                # 成功獲得回應
                ai_response=$(echo $response | jq -r '.content // .response // "抱歉，無法解析回應"')
                echo -e "\r🤖 > $ai_response"
            else
                # 錯誤處理
                echo -e "\r❌ 連接錯誤，請檢查："
                echo "   1. HSN 服務是否運行在 localhost:3000"
                echo "   2. 網路連接是否正常"
                echo "   3. API 端點是否正確"
            fi
            ;;
    esac
    echo
done


