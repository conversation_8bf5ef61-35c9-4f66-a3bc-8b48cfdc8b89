<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多 API 整合測試平台</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #0066cc;
            --secondary-green: #00aa44;
            --accent-purple: #8844cc;
            --warning-orange: #ff8800;
            --error-red: #cc3333;
            --success-green: #00cc66;
            --bg-dark: #0a0a0a;
            --bg-card: #1a1a1a;
            --bg-light: #2a2a2a;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --text-muted: #888888;
            --border-color: #333333;
        }

        body {
            font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--bg-dark) 0%, #1a1a2e 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, var(--primary-blue), var(--accent-purple));
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 102, 204, 0.3);
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            z-index: 1000;
        }

        .nav-link {
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 0.9rem;
            display: block;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.2s ease;
            margin-bottom: 5px;
        }

        .nav-link:hover {
            background: var(--primary-blue);
            color: white;
        }

        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .api-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .api-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--secondary-green));
        }

        .api-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 102, 204, 0.2);
            border-color: var(--primary-blue);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            background: var(--text-muted);
            transition: all 0.3s ease;
        }

        .status-indicator.loading {
            background: var(--warning-orange);
            animation: pulse 1.5s infinite;
        }

        .status-indicator.success {
            background: var(--success-green);
            box-shadow: 0 0 10px var(--success-green);
        }

        .status-indicator.error {
            background: var(--error-red);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary-blue);
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .input-field {
            width: 100%;
            padding: 12px;
            background: var(--bg-dark);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 0.95rem;
            transition: all 0.2s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
        }

        .textarea-field {
            min-height: 80px;
            resize: vertical;
        }

        .btn {
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-green));
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 102, 204, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn.loading {
            background: var(--warning-orange);
        }

        .result-container {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .result-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--secondary-green);
        }

        .result-content {
            font-size: 0.9rem;
            line-height: 1.6;
            white-space: pre-wrap;
        }

        .result-meta {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .error-message {
            background: rgba(204, 51, 51, 0.1);
            border: 1px solid var(--error-red);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            color: var(--error-red);
        }

        .success-message {
            background: rgba(0, 204, 102, 0.1);
            border: 1px solid var(--success-green);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            color: var(--success-green);
        }

        .combined-results {
            background: var(--bg-card);
            border: 2px solid var(--accent-purple);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .combined-title {
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 25px;
            color: var(--accent-purple);
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-blue);
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: var(--bg-dark);
            border-radius: 8px;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-blue);
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--text-muted);
            margin-top: 5px;
        }
    </style>
</head>
<body x-data="multiApiTestApp()">
    <!-- 導航 -->
    <div class="navigation">
        <a href="/" class="nav-link">← 回首頁</a>
        <a href="/finlight_test_page.html" class="nav-link">Finlight 測試</a>
        <a href="/cli-preview-c" class="nav-link">Cohere 測試</a>
    </div>

    <div class="container">
        <!-- 標題 -->
        <div class="header">
            <h1 class="title">🚀 多 API 整合測試平台</h1>
            <p class="subtitle">測試 Finlight、Claude、Tavily、OpenRouter 等多種 API 服務的整合應用</p>
        </div>

        <!-- API 測試區域 -->
        <div class="api-grid">
            <!-- Finlight 金融新聞 API -->
            <div class="api-card">
                <div class="card-header">
                    <div class="status-indicator" :class="finlightStatus"></div>
                    <h2 class="card-title">📊 Finlight 金融新聞</h2>
                </div>

                <div class="input-group">
                    <label class="input-label">搜索關鍵字</label>
                    <input type="text" 
                           class="input-field" 
                           x-model="finlightQuery"
                           placeholder="例如: NVIDIA, 台積電, Bitcoin"
                           @keydown.enter="testFinlight()">
                </div>

                <div class="input-group">
                    <label class="input-label">新聞數量</label>
                    <select class="input-field" x-model="finlightLimit">
                        <option value="5">5 篇</option>
                        <option value="10">10 篇</option>
                        <option value="15">15 篇</option>
                    </select>
                </div>

                <button class="btn" 
                        :class="{ 'loading': finlightLoading }"
                        :disabled="finlightLoading || !finlightQuery.trim()"
                        @click="testFinlight()">
                    <span x-show="!finlightLoading">🔍 搜索金融新聞</span>
                    <span x-show="finlightLoading">
                        <span class="loading-spinner"></span>搜索中...
                    </span>
                </button>

                <div x-show="finlightResult" class="result-container">
                    <div class="result-title">📰 新聞結果</div>
                    <div class="result-content" x-html="formatFinlightResult()"></div>
                    <div class="result-meta">
                        <span>響應時間: <span x-text="finlightResponseTime"></span>ms</span>
                        <span>新聞數量: <span x-text="finlightResult?.articles?.length || 0"></span></span>
                    </div>
                </div>

                <div x-show="finlightError" class="error-message">
                    <strong>❌ 錯誤:</strong> <span x-text="finlightError"></span>
                </div>
            </div>

            <!-- Claude AI 分析 -->
            <div class="api-card">
                <div class="card-header">
                    <div class="status-indicator" :class="claudeStatus"></div>
                    <h2 class="card-title">🤖 Claude AI 分析</h2>
                </div>

                <div class="input-group">
                    <label class="input-label">分析模式</label>
                    <select class="input-field" x-model="claudeMode">
                        <option value="investment">投資影響分析</option>
                        <option value="sentiment">情緒風險評估</option>
                        <option value="summary">新聞摘要整理</option>
                        <option value="prediction">市場預測分析</option>
                    </select>
                </div>

                <div class="input-group">
                    <label class="input-label">自定義提示 (可選)</label>
                    <textarea class="input-field textarea-field"
                              x-model="claudeCustomPrompt"
                              placeholder="輸入自定義分析要求..."></textarea>
                </div>

                <button class="btn"
                        :class="{ 'loading': claudeLoading }"
                        :disabled="claudeLoading || !finlightResult"
                        @click="analyzeWithClaude()">
                    <span x-show="!claudeLoading">🧠 開始 AI 分析</span>
                    <span x-show="claudeLoading">
                        <span class="loading-spinner"></span>分析中...
                    </span>
                </button>

                <div x-show="claudeResult" class="result-container">
                    <div class="result-title">🎯 AI 分析結果</div>
                    <div class="result-content" x-text="claudeResult"></div>
                    <div class="result-meta">
                        <span>分析時間: <span x-text="claudeResponseTime"></span>ms</span>
                        <span>Token 用量: <span x-text="claudeTokens || 'N/A'"></span></span>
                    </div>
                </div>

                <div x-show="claudeError" class="error-message">
                    <strong>❌ 錯誤:</strong> <span x-text="claudeError"></span>
                </div>
            </div>
        </div>

        <!-- 第二行 API -->
        <div class="api-grid">
            <!-- Tavily 網路搜索 -->
            <div class="api-card">
                <div class="card-header">
                    <div class="status-indicator" :class="tavilyStatus"></div>
                    <h2 class="card-title">🔍 Tavily 網路搜索</h2>
                </div>

                <div class="input-group">
                    <label class="input-label">搜索查詢</label>
                    <input type="text" 
                           class="input-field" 
                           x-model="tavilyQuery"
                           placeholder="例如: 最新科技趨勢, AI 發展"
                           @keydown.enter="testTavily()">
                </div>

                <div class="input-group">
                    <label class="input-label">結果數量</label>
                    <select class="input-field" x-model="tavilyMaxResults">
                        <option value="3">3 個結果</option>
                        <option value="5">5 個結果</option>
                        <option value="8">8 個結果</option>
                    </select>
                </div>

                <button class="btn"
                        :class="{ 'loading': tavilyLoading }"
                        :disabled="tavilyLoading || !tavilyQuery.trim()"
                        @click="testTavily()">
                    <span x-show="!tavilyLoading">🌐 開始網路搜索</span>
                    <span x-show="tavilyLoading">
                        <span class="loading-spinner"></span>搜索中...
                    </span>
                </button>

                <div x-show="tavilyResult" class="result-container">
                    <div class="result-title">🌐 搜索結果</div>
                    <div class="result-content" x-html="formatTavilyResult()"></div>
                    <div class="result-meta">
                        <span>響應時間: <span x-text="tavilyResponseTime"></span>ms</span>
                        <span>結果數量: <span x-text="tavilyResult?.results?.length || 0"></span></span>
                    </div>
                </div>

                <div x-show="tavilyError" class="error-message">
                    <strong>❌ 錯誤:</strong> <span x-text="tavilyError"></span>
                </div>
            </div>

            <!-- OpenRouter AI 對話 -->
            <div class="api-card">
                <div class="card-header">
                    <div class="status-indicator" :class="openrouterStatus"></div>
                    <h2 class="card-title">🎭 OpenRouter AI</h2>
                </div>

                <div class="input-group">
                    <label class="input-label">AI 模型</label>
                    <select class="input-field" x-model="openrouterModel">
                        <option value="gpt-4o-mini-fast">GPT-4o Mini (極速)</option>
                        <option value="valkyrie">Valkyrie 49B</option>
                        <option value="anubis">Anubis Pro 105B</option>
                        <option value="deepseek-v3">DeepSeek R1</option>
                        <option value="gpt-4o-mini">GPT-4o Mini</option>
                    </select>
                </div>

                <div class="input-group">
                    <label class="input-label">對話訊息</label>
                    <textarea class="input-field textarea-field"
                              x-model="openrouterMessage"
                              placeholder="輸入您想與 AI 討論的內容..."></textarea>
                </div>

                <button class="btn"
                        :class="{ 'loading': openrouterLoading }"
                        :disabled="openrouterLoading || !openrouterMessage.trim()"
                        @click="testOpenRouter()">
                    <span x-show="!openrouterLoading">💬 開始 AI 對話</span>
                    <span x-show="openrouterLoading">
                        <span class="loading-spinner"></span>對話中...
                    </span>
                </button>

                <div x-show="openrouterResult" class="result-container">
                    <div class="result-title">💬 AI 回應</div>
                    <div class="result-content" x-text="openrouterResult"></div>
                    <div class="result-meta">
                        <span>響應時間: <span x-text="openrouterResponseTime"></span>ms</span>
                        <span>使用模型: <span x-text="openrouterUsedModel"></span></span>
                    </div>
                </div>

                <div x-show="openrouterError" class="error-message">
                    <strong>❌ 錯誤:</strong> <span x-text="openrouterError"></span>
                </div>
            </div>
        </div>

        <!-- 組合測試結果 -->
        <div x-show="hasAnyResults()" class="combined-results">
            <h2 class="combined-title">🚀 多 API 整合測試結果</h2>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" x-text="getSuccessfulTests()"></div>
                    <div class="stat-label">成功測試</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" x-text="getTotalResponseTime()"></div>
                    <div class="stat-label">總響應時間 (ms)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" x-text="getDataPoints()"></div>
                    <div class="stat-label">數據點</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" x-text="getApiCoverage()"></div>
                    <div class="stat-label">API 覆蓋率</div>
                </div>
            </div>

            <div x-show="finlightResult && claudeResult" class="success-message" style="margin-top: 20px;">
                ✅ <strong>智能金融分析流程測試成功！</strong><br>
                Finlight 新聞數據 → Claude AI 深度分析 → 投資決策支援
            </div>

            <div x-show="tavilyResult && openrouterResult" class="success-message" style="margin-top: 20px;">
                ✅ <strong>智能搜索對話流程測試成功！</strong><br>
                Tavily 網路搜索 → OpenRouter AI 對話 → 知識整合應用
            </div>

            <div x-show="getAllApiResults().length >= 3" class="success-message" style="margin-top: 20px;">
                🎉 <strong>多 API 協同測試完成！</strong><br>
                已成功測試 <span x-text="getAllApiResults().length"></span> 個 API 服務，系統整合度良好
            </div>
        </div>
    </div>

    <script>
        function multiApiTestApp() {
            return {
                // Finlight 狀態
                finlightQuery: 'NVIDIA AI chips',
                finlightLimit: '10',
                finlightLoading: false,
                finlightResult: null,
                finlightError: null,
                finlightResponseTime: 0,
                finlightStatus: '',

                // Claude 狀態
                claudeMode: 'investment',
                claudeCustomPrompt: '',
                claudeLoading: false,
                claudeResult: null,
                claudeError: null,
                claudeResponseTime: 0,
                claudeTokens: null,
                claudeStatus: '',

                // Tavily 狀態
                tavilyQuery: 'AI technology trends 2025',
                tavilyMaxResults: '5',
                tavilyLoading: false,
                tavilyResult: null,
                tavilyError: null,
                tavilyResponseTime: 0,
                tavilyStatus: '',

                // OpenRouter 狀態
                openrouterModel: 'valkyrie',
                openrouterMessage: '請分析當前 AI 技術發展趨勢',
                openrouterLoading: false,
                openrouterResult: null,
                openrouterError: null,
                openrouterResponseTime: 0,
                openrouterUsedModel: '',
                openrouterStatus: '',

                // 測試 Finlight API
                async testFinlight() {
                    if (!this.finlightQuery.trim()) return;

                    this.finlightLoading = true;
                    this.finlightError = null;
                    this.finlightResult = null;
                    this.finlightStatus = 'loading';
                    const startTime = Date.now();

                    try {
                        const response = await fetch('/api/finlight/search', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                query: this.finlightQuery,
                                pageSize: parseInt(this.finlightLimit),
                                order: 'DESC'
                            })
                        });

                        this.finlightResponseTime = Date.now() - startTime;

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const data = await response.json();
                        this.finlightResult = data;
                        this.finlightStatus = 'success';

                        console.log('✅ Finlight API 測試成功:', data);

                    } catch (error) {
                        console.error('❌ Finlight API 錯誤:', error);
                        this.finlightError = error.message;
                        this.finlightStatus = 'error';
                    } finally {
                        this.finlightLoading = false;
                    }
                },

                // 測試 Claude 分析
                async analyzeWithClaude() {
                    if (!this.finlightResult) {
                        this.claudeError = '請先搜索金融新聞';
                        return;
                    }

                    this.claudeLoading = true;
                    this.claudeError = null;
                    this.claudeResult = null;
                    this.claudeStatus = 'loading';
                    const startTime = Date.now();

                    try {
                        // 準備新聞內容
                        const newsContent = this.finlightResult.articles
                            .slice(0, 5)
                            .map(article => `${article.title}\n${article.description || ''}`)
                            .join('\n\n---\n\n');

                        const analysisPrompts = {
                            investment: `作為專業投資分析師，請分析以下新聞對投資決策的影響：
1. 關鍵投資信號和風險點
2. 可能受影響的股票或行業
3. 建議的投資行動 (買入/賣出/觀望)
4. 風險等級評估 (1-10分)`,

                            sentiment: `請分析以下新聞的市場情緒和風險：
1. 整體市場情緒 (樂觀/中性/悲觀)
2. 主要風險因素識別
3. 情緒信心指數 (1-10分)
4. 短期和長期影響預測`,

                            summary: `請整理以下新聞的關鍵要點：
1. 3個最重要的市場信息
2. 核心事件時間線
3. 關鍵數據和指標
4. 後續關注要點`,

                            prediction: `基於以下新聞，請提供市場預測：
1. 短期市場趨勢 (1-4週)
2. 中期影響評估 (1-3個月)
3. 關鍵催化劑和時間點
4. 預測信心等級 (1-10分)`
                        };

                        const prompt = this.claudeCustomPrompt.trim() || analysisPrompts[this.claudeMode];

                        const response = await fetch('/api/claude/analyze', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                newsContent: newsContent,
                                analysisPrompt: prompt,
                                mode: this.claudeMode
                            })
                        });

                        this.claudeResponseTime = Date.now() - startTime;

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const data = await response.json();
                        this.claudeResult = data.analysis;
                        this.claudeTokens = data.tokens;
                        this.claudeStatus = 'success';

                        console.log('✅ Claude 分析成功:', data);

                    } catch (error) {
                        console.error('❌ Claude API 錯誤:', error);
                        this.claudeError = error.message;
                        this.claudeStatus = 'error';
                    } finally {
                        this.claudeLoading = false;
                    }
                },

                // 測試 Tavily 搜索 - 直接 API 調用
                async testTavily() {
                    if (!this.tavilyQuery.trim()) return;

                    this.tavilyLoading = true;
                    this.tavilyError = null;
                    this.tavilyResult = null;
                    this.tavilyStatus = 'loading';
                    const startTime = Date.now();

                    try {
                        // 直接調用 Tavily API（跳過後端代理）
                        const response = await fetch('https://api.tavily.com/search', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                api_key: '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', // 直接使用 API key
                                query: this.tavilyQuery,
                                search_depth: 'basic',
                                include_answer: true,
                                include_images: false,
                                include_raw_content: false,
                                max_results: parseInt(this.tavilyMaxResults)
                            })
                        });

                        this.tavilyResponseTime = Date.now() - startTime;

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const data = await response.json();
                        this.tavilyResult = data;
                        this.tavilyStatus = 'success';

                        console.log('✅ Tavily 搜索成功 (直接調用):', data);

                    } catch (error) {
                        console.error('❌ Tavily API 錯誤:', error);
                        this.tavilyError = error.message;
                        this.tavilyStatus = 'error';
                    } finally {
                        this.tavilyLoading = false;
                    }
                },

                // 測試 OpenRouter AI
                async testOpenRouter() {
                    if (!this.openrouterMessage.trim()) return;

                    this.openrouterLoading = true;
                    this.openrouterError = null;
                    this.openrouterResult = null;
                    this.openrouterStatus = 'loading';
                    const startTime = Date.now();

                    try {
                        const response = await fetch('/api/chat/send', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                message: this.openrouterMessage,
                                persona: 'tech-enthusiast',
                                modelKey: this.openrouterModel,
                                isAdvanced: true
                            })
                        });

                        this.openrouterResponseTime = Date.now() - startTime;

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const data = await response.json();
                        this.openrouterResult = data.response;
                        this.openrouterUsedModel = data.modelUsed || data.model || this.openrouterModel;
                        this.openrouterStatus = 'success';

                        console.log('✅ OpenRouter 對話成功:', data);

                    } catch (error) {
                        console.error('❌ OpenRouter API 錯誤:', error);
                        this.openrouterError = error.message;
                        this.openrouterStatus = 'error';
                    } finally {
                        this.openrouterLoading = false;
                    }
                },

                // 格式化結果顯示函數
                formatFinlightResult() {
                    if (!this.finlightResult || !this.finlightResult.articles) return '';

                    return this.finlightResult.articles.slice(0, 3).map(article => `
                        <div style="margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid var(--border-color);">
                            <strong style="color: var(--primary-blue);">${article.title}</strong><br>
                            <span style="color: var(--text-muted); font-size: 0.8rem;">
                                ${new Date(article.publishedAt).toLocaleString('zh-TW')}
                                ${article.source ? ` • ${article.source}` : ''}
                            </span><br>
                            <span style="color: var(--text-secondary); font-size: 0.9rem;">
                                ${article.description || ''}
                            </span>
                        </div>
                    `).join('') + (this.finlightResult.articles.length > 3 ?
                        `<div style="color: var(--warning-orange); text-align: center; margin-top: 10px;">
                            ...還有 ${this.finlightResult.articles.length - 3} 篇新聞
                        </div>` : '');
                },

                formatTavilyResult() {
                    if (!this.tavilyResult || !this.tavilyResult.results) return '';

                    let html = '';

                    if (this.tavilyResult.answer) {
                        html += `<div style="margin-bottom: 20px; padding: 15px; background: var(--bg-dark); border-radius: 8px; border-left: 4px solid var(--secondary-green);">
                            <strong style="color: var(--secondary-green);">📝 AI 摘要:</strong><br>
                            <span style="color: var(--text-primary);">${this.tavilyResult.answer}</span>
                        </div>`;
                    }

                    html += this.tavilyResult.results.slice(0, 3).map(result => `
                        <div style="margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid var(--border-color);">
                            <strong style="color: var(--primary-blue);">${result.title}</strong><br>
                            <span style="color: var(--text-secondary); font-size: 0.9rem; line-height: 1.4;">
                                ${result.content.substring(0, 200)}...
                            </span><br>
                            <a href="${result.url}" target="_blank" style="color: var(--secondary-green); font-size: 0.8rem;">
                                🔗 查看原文
                            </a>
                        </div>
                    `).join('');

                    return html;
                },

                // 統計函數
                hasAnyResults() {
                    return this.finlightResult || this.claudeResult || this.tavilyResult || this.openrouterResult;
                },

                getSuccessfulTests() {
                    let count = 0;
                    if (this.finlightResult) count++;
                    if (this.claudeResult) count++;
                    if (this.tavilyResult) count++;
                    if (this.openrouterResult) count++;
                    return count;
                },

                getTotalResponseTime() {
                    return this.finlightResponseTime + this.claudeResponseTime +
                           this.tavilyResponseTime + this.openrouterResponseTime;
                },

                getDataPoints() {
                    let points = 0;
                    if (this.finlightResult?.articles) points += this.finlightResult.articles.length;
                    if (this.claudeResult) points += 1;
                    if (this.tavilyResult?.results) points += this.tavilyResult.results.length;
                    if (this.openrouterResult) points += 1;
                    return points;
                },

                getApiCoverage() {
                    const total = 4; // 總共4個API
                    const tested = this.getSuccessfulTests();
                    return Math.round((tested / total) * 100) + '%';
                },

                getAllApiResults() {
                    const results = [];
                    if (this.finlightResult) results.push('Finlight');
                    if (this.claudeResult) results.push('Claude');
                    if (this.tavilyResult) results.push('Tavily');
                    if (this.openrouterResult) results.push('OpenRouter');
                    return results;
                }
            }
        }

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 多 API 測試平台已載入');
            console.log('📋 可測試的 API: Finlight, Claude, Tavily, OpenRouter');
        });
    </script>
</body>
</html>
