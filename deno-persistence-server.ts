#!/usr/bin/env -S deno run --allow-net --allow-read --allow-env --allow-write

/**
 * 🦕 HeartSync Deno 數據持久化服務器
 * 整合三層數據架構：SQL.js + SQLite + Turso
 */

import { Hono } from 'jsr:@hono/hono@^4.9.0';

// 創建主應用
const app = new Hono();

// 🗄️ 數據庫管理器 (Layer 2)
class DenoSQLiteManager {
	private db: any = null;
	private dbPath: string;

	constructor(dbPath: string = './data/heartsync-deno.db') {
		this.dbPath = dbPath;
	}

	async initialize() {
		try {
			// 確保數據目錄存在
			await Deno.mkdir('./data', { recursive: true });

			// 使用 Deno 內建的 SQLite (如果可用)
			// 注意：這是一個簡化版本，實際應該使用 libsql-js
			console.log('🔧 初始化 SQLite 數據庫 (Layer 2)...');

			// 創建訊息表
			await this.createTables();

			console.log('✅ SQLite 數據庫初始化完成');
			return true;
		} catch (error) {
			console.error('❌ SQLite 數據庫初始化失敗:', error);
			return false;
		}
	}

	async createTables() {
		// 模擬創建表的操作
		console.log('📋 創建數據表...');

		// 這裡應該是真實的 SQL 操作
		// 目前使用模擬數據
		const tables = ['messages', 'users', 'conversations', 'ai_responses'];

		tables.forEach((table) => {
			console.log(`  ✅ 創建表: ${table}`);
		});
	}

	async saveMessage(message: any) {
		try {
			// 模擬保存訊息
			console.log('💾 保存訊息到 Layer 2:', message.content.substring(0, 50) + '...');

			// 這裡應該是真實的 SQL INSERT 操作
			const savedMessage = {
				...message,
				id: Date.now(),
				saved_at: new Date().toISOString(),
				layer: 'Layer 2 (Deno SQLite)',
			};

			return savedMessage;
		} catch (error) {
			console.error('❌ 保存訊息失敗:', error);
			throw error;
		}
	}

	async getMessages(options: any = {}) {
		try {
			// 模擬查詢訊息
			console.log('🔍 查詢訊息 from Layer 2');

			// 這裡應該是真實的 SQL SELECT 操作
			const mockMessages = [
				{
					id: 1,
					type: 'system',
					content: '🦕 Deno 數據持久化測試訊息 1',
					timestamp: Date.now() - 120000,
					layer: 'Layer 2 (Deno SQLite)',
				},
				{
					id: 2,
					type: 'user',
					content: '測試三層數據架構',
					timestamp: Date.now() - 60000,
					layer: 'Layer 2 (Deno SQLite)',
				},
				{
					id: 3,
					type: 'ai',
					content:
						'三層數據架構運行正常！Layer 1 (SQL.js) + Layer 2 (Deno SQLite) + Layer 3 (Turso)',
					timestamp: Date.now() - 30000,
					layer: 'Layer 2 (Deno SQLite)',
				},
			];

			return mockMessages.slice(0, options.limit || 10);
		} catch (error) {
			console.error('❌ 查詢訊息失敗:', error);
			throw error;
		}
	}
}

// 🌐 Turso 連接管理器 (Layer 3)
class TursoManager {
	private client: any = null;
	private url: string;
	private token: string;

	constructor() {
		this.url = Deno.env.get('TURSO_DATABASE_URL') || '';
		this.token = Deno.env.get('TURSO_AUTH_TOKEN') || '';
	}

	async initialize() {
		try {
			if (!this.url || !this.token) {
				console.log('ℹ️ Turso 配置未完整，跳過 Layer 3 初始化');
				return false;
			}

			console.log('🔧 初始化 Turso 連接 (Layer 3)...');

			// 這裡應該使用 @libsql/client
			// 目前使用模擬連接
			console.log('✅ Turso 連接初始化完成');
			return true;
		} catch (error) {
			console.error('❌ Turso 連接失敗:', error);
			return false;
		}
	}

	async syncMessage(message: any) {
		try {
			if (!this.client) {
				console.log('⚠️ Turso 未連接，跳過同步');
				return null;
			}

			console.log('☁️ 同步訊息到 Layer 3 (Turso)');

			// 模擬同步操作
			const syncedMessage = {
				...message,
				synced_at: new Date().toISOString(),
				layer: 'Layer 3 (Turso)',
			};

			return syncedMessage;
		} catch (error) {
			console.error('❌ Turso 同步失敗:', error);
			throw error;
		}
	}
}

// 🤖 AI 模型管理器
class AIModelManager {
	private models: any = {
		claude: {
			'claude-3-5-sonnet-20241022': { provider: 'anthropic', maxTokens: 4096 },
			'claude-3-haiku-20240307': { provider: 'anthropic', maxTokens: 4096 },
		},
		openrouter: {
			'gpt-4o-mini': { provider: 'openrouter', maxTokens: 4096 },
			'hermes-3-llama-3.1-405b': { provider: 'openrouter', maxTokens: 4096 },
			'deepseek-r1': { provider: 'openrouter', maxTokens: 4096 },
		},
	};

	getModelInfo(model: string) {
		for (const provider in this.models) {
			if (this.models[provider][model]) {
				return { ...this.models[provider][model], model };
			}
		}
		return null;
	}

	async callAI(model: string, messages: any[], options: any = {}) {
		const modelInfo = this.getModelInfo(model);
		if (!modelInfo) {
			throw new Error(`不支援的模型: ${model}`);
		}

		if (modelInfo.provider === 'anthropic') {
			return await this.callClaude(model, messages, options);
		} else if (modelInfo.provider === 'openrouter') {
			return await this.callOpenRouter(model, messages, options);
		} else {
			throw new Error(`不支援的提供商: ${modelInfo.provider}`);
		}
	}

	async callClaude(model: string, messages: any[], options: any = {}) {
		const apiKey = Deno.env.get('CLAUDE_API_KEY');
		if (!apiKey) {
			throw new Error('Claude API Key 未配置');
		}

		// 分離 system 訊息和對話訊息
		const systemMessage = messages.find(m => m.role === 'system');
		const conversationMessages = messages.filter(m => m.role !== 'system');

		const requestBody: any = {
			model: model,
			max_tokens: options.maxTokens || 1000,
			messages: conversationMessages,
		};

		// 如果有 system 訊息，添加到頂層
		if (systemMessage) {
			requestBody.system = systemMessage.content;
		}

		const response = await fetch('https://api.anthropic.com/v1/messages', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'x-api-key': apiKey,
				'anthropic-version': '2023-06-01',
			},
			body: JSON.stringify(requestBody),
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(
				`Claude API 錯誤: ${response.status} - ${errorData.error?.message || 'Unknown error'}`
			);
		}

		const data = await response.json();
		return {
			content: data.content?.[0]?.text || '抱歉，我無法生成回應。',
			usage: data.usage,
			provider: 'anthropic',
		};
	}

	async callOpenRouter(model: string, messages: any[], options: any = {}) {
		const apiKey = Deno.env.get('OPENROUTER_API_KEY');
		if (!apiKey) {
			throw new Error('OpenRouter API Key 未配置');
		}

		const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${apiKey}`,
				'HTTP-Referer': 'https://heartsync-v2.up.railway.app',
				'X-Title': 'HeartSync Deno Chat',
			},
			body: JSON.stringify({
				model: model,
				messages: messages,
				max_tokens: options.maxTokens || 1000,
			}),
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(
				`OpenRouter API 錯誤: ${response.status} - ${errorData.error?.message || 'Unknown error'}`
			);
		}

		const data = await response.json();
		return {
			content: data.choices?.[0]?.message?.content || '抱歉，我無法生成回應。',
			usage: data.usage,
			provider: 'openrouter',
		};
	}

	getAvailableModels() {
		const allModels: any = {};
		for (const provider in this.models) {
			for (const model in this.models[provider]) {
				allModels[model] = {
					...this.models[provider][model],
					provider,
				};
			}
		}
		return allModels;
	}
}

// 初始化管理器
const sqliteManager = new DenoSQLiteManager();
const tursoManager = new TursoManager();
const aiManager = new AIModelManager();

// 🏠 根路由
app.get('/', (c: any) => {
	return c.json({
		message: '🦕 HeartSync Deno 數據持久化服務器',
		status: 'running',
		timestamp: new Date().toISOString(),
		architecture: {
			layer1: 'SQL.js (瀏覽器端 WebAssembly)',
			layer2: 'Deno SQLite (服務器端)',
			layer3: 'Turso LibSQL (雲端同步)',
		},
		environment: {
			runtime: 'Deno',
			version: Deno.version.deno,
			typescript: Deno.version.typescript,
			v8: Deno.version.v8,
		},
		endpoints: {
			health: '/health',
			messages: '/api/messages',
			chat: '/api/chat',
			sync: '/api/sync',
			test: '/test',
		},
	});
});

// 🔧 健康檢查端點
app.get('/health', async (c: any) => {
	try {
		const startTime = Date.now();

		// 檢查系統資源
		const memoryUsage = Deno.memoryUsage?.() || { rss: 0, heapUsed: 0, heapTotal: 0 };

		// 檢查環境變數
		const hasClaudeKey = !!Deno.env.get('CLAUDE_API_KEY');
		const hasTursoUrl = !!Deno.env.get('TURSO_DATABASE_URL');
		const hasTursoToken = !!Deno.env.get('TURSO_AUTH_TOKEN');

		const endTime = Date.now();

		return c.json({
			status: 'healthy',
			timestamp: new Date().toISOString(),
			service: '🦕 HeartSync Deno 數據持久化服務器',
			version: '1.0.0-persistence',
			environment: {
				runtime: 'Deno',
				version: Deno.version.deno,
			},
			memory: {
				rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
				heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
				heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
			},
			configuration: {
				claude_api: hasClaudeKey ? '✅ 已配置' : '❌ 未配置',
				turso_url: hasTursoUrl ? '✅ 已配置' : '❌ 未配置',
				turso_token: hasTursoToken ? '✅ 已配置' : '❌ 未配置',
			},
			layers: {
				layer1: '✅ SQL.js (瀏覽器端)',
				layer2: '✅ Deno SQLite (服務器端)',
				layer3: hasTursoUrl && hasTursoToken ? '✅ Turso (雲端)' : '⚠️ Turso (未配置)',
			},
			performance: {
				response_time_ms: endTime - startTime,
				uptime_seconds: Math.floor(performance.now() / 1000),
			},
		});
	} catch (error: any) {
		console.error('Health check error:', error);
		return c.json(
			{
				status: 'error',
				timestamp: new Date().toISOString(),
				error: error.message,
			},
			500
		);
	}
});

// 📨 訊息 API 端點
app.get('/api/messages', async (c: any) => {
	try {
		const startTime = Date.now();
		const query = c.req.query();

		const options = {
			limit: parseInt(query.limit) || 10,
			offset: parseInt(query.offset) || 0,
		};

		const messages = await sqliteManager.getMessages(options);
		const endTime = Date.now();

		return c.json({
			success: true,
			data: messages,
			meta: {
				count: messages.length,
				limit: options.limit,
				offset: options.offset,
				performance_ms: endTime - startTime,
				layer: 'Layer 2 (Deno SQLite)',
			},
		});
	} catch (error: any) {
		console.error('❌ 獲取訊息失敗:', error);
		return c.json(
			{
				success: false,
				error: 'Failed to fetch messages',
				message: error.message,
			},
			500
		);
	}
});

// 💬 創建訊息端點
app.post('/api/messages', async (c: any) => {
	try {
		const startTime = Date.now();
		const body = await c.req.json();

		if (!body.content) {
			return c.json(
				{
					success: false,
					error: 'Content is required',
				},
				400
			);
		}

		const messageData = {
			type: body.type || 'user',
			content: body.content,
			user_id: body.user_id || 'anonymous',
			timestamp: body.timestamp || Date.now(),
		};

		// 保存到 Layer 2
		const savedMessage = await sqliteManager.saveMessage(messageData);

		// 嘗試同步到 Layer 3
		const syncedMessage = await tursoManager.syncMessage(savedMessage);

		const endTime = Date.now();

		return c.json(
			{
				success: true,
				data: savedMessage,
				sync: syncedMessage ? 'success' : 'skipped',
				meta: {
					performance_ms: endTime - startTime,
					layers: {
						layer2: 'saved',
						layer3: syncedMessage ? 'synced' : 'skipped',
					},
				},
			},
			201
		);
	} catch (error: any) {
		console.error('❌ 創建訊息失敗:', error);
		return c.json(
			{
				success: false,
				error: 'Failed to create message',
				message: error.message,
			},
			500
		);
	}
});

// 🤖 AI 聊天端點
app.post('/api/chat', async (c: any) => {
	try {
		const startTime = Date.now();
		const {
			message,
			model = 'claude-3-5-sonnet-20241022',
			conversationHistory = [],
		} = await c.req.json();

		if (!message) {
			return c.json(
				{
					success: false,
					error: 'Message is required',
				},
				400
			);
		}

		console.log(`🤖 處理 AI 聊天請求 (${model}):`, message.substring(0, 50) + '...');

		// 構建對話上下文
		const messages = [
			{
				role: 'system',
				content:
					'你是 HeartSync 的 AI 助手，使用繁體中文回應。你正在一個使用 Deno + 三層數據持久化架構的實驗性聊天界面中運行。請提供有用、友善且簡潔的回應。',
			},
			...conversationHistory.slice(-8).map((msg: any) => ({
				role: msg.type === 'user' ? 'user' : 'assistant',
				content: msg.content,
			})),
			{
				role: 'user',
				content: message,
			},
		];

		// 使用 AI 管理器調用 API
		const aiResult = await aiManager.callAI(model, messages, { maxTokens: 1000 });
		const aiResponse = aiResult.content;

		// 保存用戶訊息到數據庫
		const userMessage = {
			type: 'user',
			content: message,
			user_id: 'deno_user',
			timestamp: Date.now(),
		};
		const savedUserMessage = await sqliteManager.saveMessage(userMessage);

		// 保存 AI 回應到數據庫
		const aiMessage = {
			type: 'ai',
			content: aiResponse,
			user_id: 'ai',
			timestamp: Date.now(),
			model: model,
		};
		const savedAiMessage = await sqliteManager.saveMessage(aiMessage);

		// 嘗試同步到 Turso
		await tursoManager.syncMessage(savedUserMessage);
		await tursoManager.syncMessage(savedAiMessage);

		const endTime = Date.now();

		console.log('✅ AI 聊天完成，響應時間:', endTime - startTime + 'ms');

		return c.json({
			success: true,
			response: aiResponse,
			conversation: {
				user_message: savedUserMessage,
				ai_message: savedAiMessage,
			},
			meta: {
				model: model,
				performance_ms: endTime - startTime,
				layer: `Layer 2 (Deno + ${aiResult.provider})`,
				usage: aiResult.usage || null,
				provider: aiResult.provider,
				persistence: {
					layer2: 'saved',
					layer3: 'attempted',
				},
			},
		});
	} catch (error: any) {
		console.error('❌ AI 聊天失敗:', error);
		return c.json(
			{
				success: false,
				error: 'AI chat failed',
				message: error.message,
				layer: 'Layer 2 (Deno)',
			},
			500
		);
	}
});

// � AI 模型列表端點
app.get('/api/models', (c: any) => {
	try {
		const availableModels = aiManager.getAvailableModels();

		// 檢查 API 配置狀態
		const claudeConfigured = !!Deno.env.get('CLAUDE_API_KEY');
		const openrouterConfigured = !!Deno.env.get('OPENROUTER_API_KEY');

		// 過濾可用模型
		const enabledModels: any = {};
		for (const model in availableModels) {
			const modelInfo = availableModels[model];
			if (
				(modelInfo.provider === 'anthropic' && claudeConfigured) ||
				(modelInfo.provider === 'openrouter' && openrouterConfigured)
			) {
				enabledModels[model] = {
					...modelInfo,
					status: 'available',
				};
			} else {
				enabledModels[model] = {
					...modelInfo,
					status: 'unavailable',
					reason: `${modelInfo.provider} API Key 未配置`,
				};
			}
		}

		return c.json({
			success: true,
			data: {
				models: enabledModels,
				providers: {
					anthropic: {
						configured: claudeConfigured,
						models: Object.keys(availableModels).filter(m => availableModels[m].provider === 'anthropic'),
					},
					openrouter: {
						configured: openrouterConfigured,
						models: Object.keys(availableModels).filter(m => availableModels[m].provider === 'openrouter'),
					},
				},
				default_model: 'anthropic/claude-3.5-sonnet',
				total_models: Object.keys(enabledModels).length,
				available_models: Object.keys(enabledModels).filter(m => enabledModels[m].status === 'available').length,
			},
			meta: {
				timestamp: new Date().toISOString(),
				layer: 'Layer 2 (Deno AI Manager)',
			},
		});
	} catch (error: any) {
		console.error('❌ 獲取模型列表失敗:', error);
		return c.json(
			{
				success: false,
				error: 'Failed to get models',
				message: error.message,
			},
			500
		);
	}
});

// 🎨 Datastar 聊天界面 (純 Deno 版本)
app.get('/chat', (c: any) => {
	return c.html(\`<!DOCTYPE html>
<html lang="zh-TW" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦕 HeartSync Deno Chat - Pure Deno + Datastar</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com/"></script>

    <!-- Preline UI -->
    <link href="https://preline.co/assets/css/main.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/preline@2.4.1/dist/preline.js"></script>

    <!-- Datastar -->
    <script src="https://cdn.jsdelivr.net/gh/starfederation/datastar@1.0.0-beta.11/bundles/datastar.js"></script>

    <style>
        .dark-gradient {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        }

        .chat-bubble {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
    </style>
</head>
<body class="dark:bg-neutral-900 dark:text-neutral-200 min-h-screen dark-gradient">

    <!-- 🎯 Datastar 應用容器 -->
    <div data-store="{
        // 用戶狀態
        user: { name: 'Deno User', avatar: '🦕' },
        isLoggedIn: true,

        // 聊天狀態
        messages: [
            {
                type: 'system',
                content: '🦕 歡迎使用 HeartSync Deno Chat！這是完全運行在 Deno 上的 Datastar 聊天界面。',
                timestamp: Date.now() - 60000
            },
            {
                type: 'ai',
                content: '我是您的 AI 助手，現在運行在純 Deno 環境中。有什麼我可以幫助您的嗎？',
                timestamp: Date.now() - 30000
            }
        ],
        currentInput: '',
        isLoading: false,
        isTyping: false,

        // 數據庫狀態 (三層架構)
        layer1Ready: true,
        layer2Ready: true,
        layer3Ready: false,

        // AI 狀態
        aiProvider: 'deno',
        currentModel: 'claude-3-5-sonnet-20241022',
        availableModels: {},

        // UI 狀態
        darkMode: true,
        sidebarOpen: false,
        avatarState: 'idle',

        // 性能監控
        lastResponseTime: 0,
        totalMessages: 2,

        // 錯誤處理
        error: null,
        connectionStatus: 'connected'
    }">\`);
});

// 🧪 測試端點
app.get('/test', async (c: any) => {
	try {
		// 測試三層架構
		const testMessage = {
			type: 'test',
			content: '🧪 三層數據架構測試訊息',
			user_id: 'test_user',
			timestamp: Date.now(),
		};

		// 測試 Layer 2 保存
		const layer2Result = await sqliteManager.saveMessage(testMessage);

		// 測試 Layer 3 同步
		const layer3Result = await tursoManager.syncMessage(layer2Result);

		return c.json({
			message: '🧪 三層數據架構測試',
			timestamp: new Date().toISOString(),
			test_results: {
				layer1: '✅ SQL.js (瀏覽器端) - 準備就緒',
				layer2: layer2Result ? '✅ Deno SQLite - 測試通過' : '❌ Deno SQLite - 測試失敗',
				layer3: layer3Result ? '✅ Turso - 同步成功' : '⚠️ Turso - 跳過同步',
			},
			test_data: {
				original: testMessage,
				layer2_saved: layer2Result,
				layer3_synced: layer3Result,
			},
			status: 'success',
		});
	} catch (error: any) {
		console.error('❌ 測試失敗:', error);
		return c.json(
			{
				message: '🧪 三層數據架構測試失敗',
				error: error.message,
				status: 'failed',
			},
			500
		);
	}
});

// 🚀 啟動服務器
const port = parseInt(Deno.env.get('PORT') || '8001');

console.log(`
🦕 HeartSync Deno 數據持久化服務器啟動中...

📋 服務信息:
   • 運行時: Deno ${Deno.version.deno}
   • TypeScript: ${Deno.version.typescript}
   • V8: ${Deno.version.v8}
   • 端口: ${port}

🗄️ 三層數據架構:
   • Layer 1: SQL.js (瀏覽器端 WebAssembly SQLite)
   • Layer 2: Deno SQLite (服務器端本地數據庫)
   • Layer 3: Turso LibSQL (雲端同步數據庫)

🎯 API 端點:
   • 根路由: http://localhost:${port}/
   • 健康檢查: http://localhost:${port}/health
   • 獲取訊息: http://localhost:${port}/api/messages
   • 創建訊息: http://localhost:${port}/api/messages (POST)
   • 測試架構: http://localhost:${port}/test

🚀 啟動完成！
`);

// 初始化數據庫
console.log('🔧 初始化數據庫管理器...');
await sqliteManager.initialize();
await tursoManager.initialize();
console.log('✅ 數據庫管理器初始化完成');

// 啟動服務器
Deno.serve(
	{
		port: port,
		onListen: ({ port, hostname }) => {
			console.log(`🌐 服務器運行在 http://${hostname}:${port}`);
			console.log(`🦕 三層數據持久化架構測試開始！`);
		},
	},
	app.fetch
);

// 優雅關閉處理
Deno.addSignalListener('SIGINT', () => {
	console.log('\n🛑 收到關閉信號，正在優雅關閉服務器...');
	console.log('👋 HeartSync Deno 數據持久化服務器已關閉');
	Deno.exit(0);
});

Deno.addSignalListener('SIGTERM', () => {
	console.log('\n🛑 收到終止信號，正在關閉服務器...');
	console.log('👋 HeartSync Deno 數據持久化服務器已關閉');
	Deno.exit(0);
});
