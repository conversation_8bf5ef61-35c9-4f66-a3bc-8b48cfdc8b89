<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CLI-Test-F: Finlight + <PERSON> 專業組合測試</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-green: #00ff41;
            --secondary-blue: #0099cc;
            --neutral-gray: #666666;
            --bg-dark: #0a0a0a;
            --bg-card: #1a1a1a;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --border-color: #333333;
            --success-color: #00ff88;
            --warning-color: #ffaa00;
            --error-color: #ff4444;
        }

        body {
            font-family: 'Courier New', monospace;
            background: var(--bg-dark);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--primary-green);
            border-radius: 8px;
            background: var(--bg-card);
        }

        .title {
            font-size: 1.8rem;
            color: var(--primary-green);
            margin-bottom: 10px;
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
        }

        .test-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .test-card:hover {
            border-color: var(--primary-green);
            box-shadow: 0 0 10px rgba(0, 255, 65, 0.2);
        }

        .card-title {
            color: var(--primary-green);
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--neutral-gray);
            transition: all 0.3s ease;
        }

        .status-indicator.success {
            background: var(--success-color);
            box-shadow: 0 0 8px var(--success-color);
        }

        .status-indicator.loading {
            background: var(--warning-color);
            animation: pulse 1.5s infinite;
        }

        .status-indicator.error {
            background: var(--error-color);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 5px;
            display: block;
        }

        .input-field {
            width: 100%;
            background: var(--bg-dark);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 10px;
            border-radius: 4px;
            font-family: inherit;
            font-size: 0.9rem;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--primary-green);
            box-shadow: 0 0 5px rgba(0, 255, 65, 0.3);
        }

        .textarea-field {
            min-height: 80px;
            resize: vertical;
        }

        .btn {
            background: transparent;
            border: 1px solid var(--primary-green);
            color: var(--primary-green);
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            width: 100%;
        }

        .btn:hover {
            background: var(--primary-green);
            color: var(--bg-dark);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn.loading {
            color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .result-container {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .result-title {
            color: var(--secondary-blue);
            font-size: 1.1rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .result-content {
            background: var(--bg-dark);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 0.85rem;
            line-height: 1.5;
        }

        .result-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid var(--border-color);
        }

        .meta-item {
            text-align: center;
        }

        .meta-label {
            color: var(--text-secondary);
            font-size: 0.8rem;
            display: block;
        }

        .meta-value {
            color: var(--primary-green);
            font-size: 0.9rem;
            font-weight: bold;
        }

        .error-message {
            color: var(--error-color);
            background: rgba(255, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.85rem;
        }

        .success-message {
            color: var(--success-color);
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid var(--success-color);
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.85rem;
        }

        .combined-analysis {
            background: var(--bg-card);
            border: 2px solid var(--secondary-blue);
            border-radius: 8px;
            padding: 25px;
            margin-top: 30px;
        }

        .analysis-title {
            color: var(--secondary-blue);
            font-size: 1.3rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-green);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 10px;
        }

        .nav-link {
            color: var(--primary-green);
            text-decoration: none;
            font-size: 0.8rem;
            display: block;
            padding: 5px 10px;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background: var(--primary-green);
            color: var(--bg-dark);
        }
    </style>
</head>
<body x-data="finlightTestApp()">
<!-- 導航 -->
<div class="navigation">
    <a href="/" class="nav-link">← 回首頁</a>
    <a href="/cli-preview-c" class="nav-link">Cohere 測試</a>
</div>

<div class="container">
    <!-- 標題 -->
    <div class="header">
        <h1 class="title">CLI-Test-F: Finlight + Claude 組合測試</h1>
        <p class="subtitle">專業金融新聞 × AI 深度分析 = InfoSync Pro 技術驗證</p>
    </div>

    <!-- 測試區域 -->
    <div class="test-grid">
        <!-- Finlight API 測試 -->
        <div class="test-card">
            <h2 class="card-title">
                <div class="status-indicator" :class="finlightStatus"></div>
                📊 Finlight 金融新聞 API
            </h2>

            <div class="input-group">
                <label class="input-label">搜索關鍵字</label>
                <input type="text"
                       class="input-field"
                       x-model="finlightQuery"
                       placeholder="例如: NVIDIA, 台積電, Fed rate"
                       @keydown.enter="testFinlight()">
            </div>

            <div class="input-group">
                <label class="input-label">新聞數量</label>
                <select class="input-field" x-model="finlightLimit">
                    <option value="5">5 篇</option>
                    <option value="10">10 篇</option>
                    <option value="20">20 篇</option>
                </select>
            </div>

            <button class="btn"
                    :class="{ 'loading': finlightLoading }"
                    :disabled="finlightLoading || !finlightQuery.trim()"
                    @click="testFinlight()">
                <span x-show="!finlightLoading">🔍 搜索金融新聞</span>
                <span x-show="finlightLoading" class="loading-spinner"></span>
                <span x-show="finlightLoading">搜索中...</span>
            </button>

            <!-- Finlight 結果 -->
            <div x-show="finlightResult" class="result-container">
                <h3 class="result-title">
                    📰 新聞結果
                    <span x-text="'(' + (finlightResult?.articles?.length || 0) + ' 篇)'"></span>
                </h3>
                <div class="result-content" x-html="formatFinlightResult(finlightResult)"></div>
                <div class="result-meta">
                    <div class="meta-item">
                        <span class="meta-label">響應時間</span>
                        <span class="meta-value" x-text="finlightResponseTime + 'ms'"></span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">新聞來源</span>
                        <span class="meta-value" x-text="finlightResult?.articles?.length || 0"></span>
                    </div>
                </div>
            </div>

            <!-- Finlight 錯誤 -->
            <div x-show="finlightError" class="error-message">
                <strong>❌ Finlight API 錯誤:</strong><br>
                <span x-text="finlightError"></span>
            </div>
        </div>

        <!-- Claude API 測試 -->
        <div class="test-card">
            <h2 class="card-title">
                <div class="status-indicator" :class="claudeStatus"></div>
                🤖 Claude AI 分析
            </h2>

            <div class="input-group">
                <label class="input-label">分析模式</label>
                <select class="input-field" x-model="claudeMode">
                    <option value="investment">投資影響分析</option>
                    <option value="sentiment">情緒和風險評估</option>
                    <option value="summary">新聞摘要整理</option>
                    <option value="prediction">市場預測分析</option>
                </select>
            </div>

            <div class="input-group">
                <label class="input-label">自定義提示 (可選)</label>
                <textarea class="input-field textarea-field"
                          x-model="claudeCustomPrompt"
                          placeholder="輸入自定義分析要求..."></textarea>
            </div>

            <button class="btn"
                    :class="{ 'loading': claudeLoading }"
                    :disabled="claudeLoading || !finlightResult"
                    @click="analyzeWithClaude()">
                <span x-show="!claudeLoading">🧠 開始 AI 分析</span>
                <span x-show="claudeLoading" class="loading-spinner"></span>
                <span x-show="claudeLoading">分析中...</span>
            </button>

            <!-- Claude 結果 -->
            <div x-show="claudeResult" class="result-container">
                <h3 class="result-title">🎯 AI 分析結果</h3>
                <div class="result-content" x-text="claudeResult"></div>
                <div class="result-meta">
                    <div class="meta-item">
                        <span class="meta-label">分析時間</span>
                        <span class="meta-value" x-text="claudeResponseTime + 'ms'"></span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">Token 用量</span>
                        <span class="meta-value" x-text="claudeTokens || 'N/A'"></span>
                    </div>
                </div>
            </div>

            <!-- Claude 錯誤 -->
            <div x-show="claudeError" class="error-message">
                <strong>❌ Claude API 錯誤:</strong><br>
                <span x-text="claudeError"></span>
            </div>
        </div>
    </div>

    <!-- 組合分析結果 -->
    <div x-show="finlightResult && claudeResult" class="combined-analysis">
        <h2 class="analysis-title">🚀 InfoSync Pro 組合分析預覽</h2>

        <div class="result-content">
            <div style="margin-bottom: 20px;">
                <strong style="color: var(--primary-green);">📊 數據來源:</strong>
                Finlight API (專業金融新聞)
            </div>

            <div style="margin-bottom: 20px;">
                <strong style="color: var(--secondary-blue);">🧠 AI 分析:</strong>
                Claude AI (深度投資洞察)
            </div>

            <div style="padding: 15px; background: var(--bg-dark); border-radius: 4px; margin-top: 15px;">
                <strong style="color: var(--warning-color);">💡 這就是 InfoSync Pro 的核心價值:</strong><br>
                專業金融數據 + AI 智能分析 = 投資決策優勢
            </div>
        </div>

        <div class="success-message" style="margin-top: 20px;">
            ✅ 技術驗證成功！Finlight + Claude 組合完全可行，準備開始 InfoSync Pro 開發！
        </div>
    </div>
</div>

<script>
    function finlightTestApp() {
        return {
            // Finlight 狀態
            finlightQuery: 'NVIDIA earnings',
            finlightLimit: '10',
            finlightLoading: false,
            finlightResult: null,
            finlightError: null,
            finlightResponseTime: 0,
            finlightStatus: 'neutral',

            // Claude 狀態
            claudeMode: 'investment',
            claudeCustomPrompt: '',
            claudeLoading: false,
            claudeResult: null,
            claudeError: null,
            claudeResponseTime: 0,
            claudeTokens: null,
            claudeStatus: 'neutral',

            // 測試 Finlight API
            async testFinlight() {
                if (!this.finlightQuery.trim()) return;

                this.finlightLoading = true;
                this.finlightError = null;
                this.finlightResult = null;
                this.finlightStatus = 'loading';
                const startTime = Date.now();

                try {
                    const response = await fetch('/api/finlight/search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            query: this.finlightQuery,
                            pageSize: parseInt(this.finlightLimit),
                            order: 'DESC'
                        })
                    });

                    this.finlightResponseTime = Date.now() - startTime;

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    this.finlightResult = data;
                    this.finlightStatus = 'success';

                    // 自動觸發 Claude 分析
                    if (data.articles && data.articles.length > 0) {
                        setTimeout(() => this.analyzeWithClaude(), 1000);
                    }

                } catch (error) {
                    console.error('Finlight API Error:', error);
                    this.finlightError = error.message;
                    this.finlightStatus = 'error';
                } finally {
                    this.finlightLoading = false;
                }
            },

            // 分析 Claude
            async analyzeWithClaude() {
                if (!this.finlightResult) return;

                this.claudeLoading = true;
                this.claudeError = null;
                this.claudeResult = null;
                this.claudeStatus = 'loading';
                const startTime = Date.now();

                try {
                    // 準備新聞內容供 Claude 分析
                    const newsContent = this.finlightResult.articles
                        .slice(0, 5) // 只取前5篇避免Token超限
                        .map(article => `${article.title}\n${article.description || ''}`)
                        .join('\n\n---\n\n');

                    const analysisPrompts = {
                        investment: `作為專業投資分析師，請分析以下新聞對投資決策的影響：
1. 關鍵投資信號和風險點
2. 可能受影響的股票或行業
3. 建議的投資行動 (買入/賣出/觀望)
4. 風險等級評估 (1-10分)`,

                        sentiment: `請分析以下新聞的市場情緒和風險：
1. 整體市場情緒 (樂觀/中性/悲觀)
2. 主要風險因素識別
3. 情緒信心指數 (1-10分)
4. 短期和長期影響預測`,

                        summary: `請整理以下新聞的關鍵要點：
1. 3個最重要的市場信息
2. 核心事件時間線
3. 關鍵數據和指標
4. 後續關注要點`,

                        prediction: `基於以下新聞，請提供市場預測：
1. 短期市場趨勢 (1-4週)
2. 中期影響評估 (1-3個月)
3. 關鍵催化劑和時間點
4. 預測信心等級 (1-10分)`
                    };

                    const prompt = this.claudeCustomPrompt.trim() || analysisPrompts[this.claudeMode];

                    const response = await fetch('/api/claude/analyze', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            newsContent: newsContent,
                            analysisPrompt: prompt,
                            mode: this.claudeMode
                        })
                    });

                    this.claudeResponseTime = Date.now() - startTime;

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    this.claudeResult = data.analysis;
                    this.claudeTokens = data.tokens;
                    this.claudeStatus = 'success';

                } catch (error) {
                    console.error('Claude API Error:', error);
                    this.claudeError = error.message;
                    this.claudeStatus = 'error';
                } finally {
                    this.claudeLoading = false;
                }
            },

            // 格式化 Finlight 結果
            formatFinlightResult(result) {
                if (!result || !result.articles) return '';

                return result.articles.slice(0, 3).map(article => `
                        <div style="margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid var(--border-color);">
                            <strong style="color: var(--primary-green);">${article.title}</strong><br>
                            <span style="color: var(--text-secondary); font-size: 0.8rem;">
                                ${new Date(article.publishedAt).toLocaleString('zh-TW')}
                                ${article.source ? ` • ${article.source}` : ''}
                            </span><br>
                            <span style="color: var(--text-primary); font-size: 0.85rem;">
                                ${article.description || ''}
                            </span>
                        </div>
                    `).join('') + (result.articles.length > 3 ?
                    `<div style="color: var(--warning-color); text-align: center; margin-top: 10px;">
                            ...還有 ${result.articles.length - 3} 篇新聞
                        </div>` : '');
            }
        }
    }
</script>
</body>
</html>