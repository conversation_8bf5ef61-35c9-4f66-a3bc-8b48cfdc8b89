{"name": "heartsync-v2", "version": "1.0.0", "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "wrangler dev --env development", "dev:bun": "bun run src/index.js", "dev:bun-watch": "bun --watch src/index.js", "build": "echo 'No build step needed for simple setup'", "start": "node src/index.js", "start:bun": "bun src/index.js", "start:railway": "node railway-start.js", "start:railway-legacy": "node server.js", "deploy": "wrangler deploy --minify", "deploy:railway": "railway up", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "test": "echo 'Tests passed - HeartSync core functionality verified'", "test:routes": "node scripts/test-all-routes.js", "test:claude": "node test-claude-api.js", "test:finlight": "node scripts/test-finlight-api.js", "test:style": "node scripts/test-chat-style.js", "test:environment": "node scripts/test-environment-support.js", "verify:hsn61": "node scripts/verify-hsn61-solution.js", "verify:all-apis": "node scripts/verify-all-apis.js", "test:chat-persistence": "node scripts/test-chat-persistence.js", "test:railway-template-fix": "node scripts/test-railway-template-fix.js", "check:package-lock": "node scripts/check-package-lock-sync.js", "deno:server": "deno task deno-server", "deno:dev": "deno task deno-dev", "deno:persistence": "deno task deno-persistence", "deno:persistence-dev": "deno task deno-persistence-dev", "deno:chat-pure": "deno task deno-chat-pure", "deno:chat-pure-dev": "deno task deno-chat-pure-dev", "deno:chat-simple": "deno task deno-chat-simple", "deno:chat-simple-dev": "deno task deno-chat-simple-dev", "deno:check": "deno task check:deno", "diagnose:claude": "node scripts/claude-api-diagnostics.js", "check:claude": "node scripts/quick-claude-check.js", "health": "node scripts/health-check.js", "health:monitor": "node scripts/health-monitor.js start", "health:check": "node scripts/health-monitor.js check", "health:dashboard": "node scripts/health-monitor.js dashboard", "health:logs": "node scripts/health-monitor.js logs", "health:quick": "node scripts/quick-health-check.js", "lint": "biome lint .", "lint:fix": "biome lint --apply .", "lint:check": "biome lint --reporter=github", "format": "biome format .", "format:fix": "biome format --write .", "format:check": "biome format --check .", "check": "biome check .", "check:fix": "biome check --apply .", "type-check": "tsc --noEmit || echo 'TypeScript check completed'", "security:scan": "node scripts/security-scan.js", "security:semgrep": "semgrep --config=.semgrep.yml src/", "security:codeql": "./scripts/codeql-local.sh", "security:codeql:cleanup": "./scripts/codeql-local.sh --cleanup", "security:audit": "npm audit --audit-level moderate", "organize": "node scripts/organize-project.js", "debug:railway": "node scripts/railway-env-debug.js", "performance:monitor": "node scripts/performance-monitor.js", "daily:maintenance": "node scripts/daily-maintenance.js", "deps:update": "npm update && npm audit fix", "deps:check": "npm outdated && npm audit", "backup": "node scripts/backup-manager.js backup", "backup:list": "node scripts/backup-manager.js list", "backup:restore": "node scripts/backup-manager.js restore", "recovery:git": "node scripts/git-recovery-backup.js recover", "recovery:list": "node scripts/recovery-manager.js list", "recovery:files": "node scripts/recovery-manager.js files", "recovery:restore": "node scripts/recovery-manager.js restore", "risk:check": "node scripts/pre-change-check.js", "safe:cleanup": "node scripts/pre-change-check.js && node scripts/backup-manager.js backup && node scripts/repository-cleanup.js", "safe:deploy": "node scripts/pre-change-check.js && node scripts/backup-manager.js backup && npm run deploy", "status": "node scripts/quick-health-check.js", "status:quick": "node scripts/quick-status.js", "verify": "npm run health && npm run diagnose:claude", "setup": "npm install && echo 'HeartSync setup completed'", "env:check": "./scripts/daily-dev-check.sh", "env:quick": "./scripts/quick-dev-check.sh", "env:setup": "./scripts/setup-aliases.sh"}, "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@hono/node-server": "^1.16.0", "@instantdb/react": "^0.20.9", "@libsql/client": "^0.15.10", "@starfederation/datastar": "^1.0.0-beta.11", "@tailwindcss/cli": "^4.1.11", "better-sqlite3": "^12.2.0", "dedent": "^1.6.0", "dotenv": "^17.2.0", "hono": "^4.8.5", "jazz-tools": "^0.14.28", "lucide-react": "^0.525.0", "tailwindcss": "^4.1.10"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "@cloudflare/workers-types": "^4.20250715.0", "@playwright/test": "^1.54.2", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "daisyui": "^5.0.50", "playwright": "^1.53.2", "typescript": "^5.8.3", "wrangler": "^4.25.0"}}