# HeartSync 開發最佳實踐與備份規則

## 🛡️ 核心原則

**安全第一**: 在進行任何重大變更前，必須先建立適當的備份保護機制。

## 📋 強制備份規則

### 🚨 必須備份的情況

以下操作**必須**在執行前先進行本地備份：

#### 1. 重大程式碼變更
- 修改核心應用邏輯 (`src/index.js`)
- 重構路由系統 (`src/routes/`)
- 更新資料庫配置或架構
- 修改 API 整合代碼
- 變更安全配置

#### 2. 文件系統操作
- 刪除或移動多個文件
- 重新組織目錄結構
- 清理歷史文件
- 批量重命名操作

#### 3. 配置文件變更
- 修改 `package.json` 依賴
- 更新 `wrangler.toml` 部署配置
- 變更環境變數設定
- 修改 Git 配置

#### 4. 文檔管理
- 刪除重要文檔
- 重新組織文檔結構
- 批量更新文檔內容
- 清理報告文件

#### 5. 部署相關
- 準備生產部署
- 更新部署腳本
- 修改 CI/CD 配置

## 🔧 備份執行流程

### 標準備份流程

```bash
# 1. 檢查當前狀態
git status
git log --oneline -5

# 2. 創建本地備份
node scripts/backup-manager.js backup

# 3. 確認備份成功
node scripts/backup-manager.js list

# 4. 執行變更操作
# ... 你的變更操作 ...

# 5. 測試變更結果
npm test  # 如果有測試
npm run dev  # 驗證功能

# 6. 提交變更 (如果成功)
git add .
git commit -m "描述性提交信息"
```

### 高風險操作流程

對於特別高風險的操作，使用增強備份流程：

```bash
# 1. 創建 Git 分支
git checkout -b backup-before-major-change

# 2. 提交當前狀態
git add .
git commit -m "Backup before major change"

# 3. 創建本地備份
node scripts/backup-manager.js backup

# 4. 從 Git 歷史創建恢復點
node scripts/git-recovery-backup.js recover

# 5. 回到主分支執行變更
git checkout main
# ... 執行變更 ...

# 6. 測試完成後刪除備份分支 (可選)
git branch -d backup-before-major-change
```

## 📊 備份類型與用途

### 1. 即時備份 (Real-time Backup)
- **用途**: 日常開發中的快速保護
- **命令**: `node scripts/backup-manager.js backup`
- **適用**: 小型變更、實驗性修改

### 2. Git 歷史恢復 (Git Recovery)
- **用途**: 恢復已刪除的歷史文件
- **命令**: `node scripts/git-recovery-backup.js recover`
- **適用**: 文件清理後的恢復需求

### 3. 分支備份 (Branch Backup)
- **用途**: 重大變更的完整保護
- **命令**: `git checkout -b backup-branch`
- **適用**: 架構重構、大規模重組

## ⚠️ 風險評估指南

### 🔴 高風險操作 (必須多重備份)
- 刪除超過 10 個文件
- 修改核心架構
- 更新主要依賴版本
- 重構資料庫結構
- 部署到生產環境

### 🟡 中風險操作 (必須標準備份)
- 修改單個重要文件
- 添加新功能模組
- 更新配置文件
- 重新組織文檔

### 🟢 低風險操作 (建議備份)
- 修復小錯誤
- 更新文檔內容
- 添加註釋
- 格式化代碼

## 🔄 恢復程序

### 快速恢復
```bash
# 查看可用備份
node scripts/recovery-manager.js list

# 恢復特定文件
node scripts/recovery-manager.js restore <timestamp> <filename>

# 恢復整個類別
node scripts/recovery-manager.js category <timestamp> <category>
```

### Git 恢復
```bash
# 恢復到特定提交
git checkout <commit-hash> -- <filename>

# 重置到上一個提交
git reset --hard HEAD~1

# 恢復整個分支
git checkout backup-branch
```

## 📝 檢查清單

### 變更前檢查
- [ ] 確認變更範圍和影響
- [ ] 評估風險等級
- [ ] 選擇適當的備份策略
- [ ] 執行備份操作
- [ ] 驗證備份完整性

### 變更後檢查
- [ ] 測試變更功能
- [ ] 檢查相關功能是否正常
- [ ] 確認沒有破壞性影響
- [ ] 更新相關文檔
- [ ] 提交變更到版本控制

### 恢復檢查
- [ ] 確認需要恢復的文件
- [ ] 選擇正確的備份時間點
- [ ] 執行恢復操作
- [ ] 驗證恢復結果
- [ ] 測試功能完整性

## 🚀 自動化建議

### 預提交鉤子 (Pre-commit Hook)
```bash
#!/bin/sh
# .git/hooks/pre-commit

# 檢查是否有重大變更
if git diff --cached --name-only | grep -E "(src/index\.js|package\.json|wrangler\.toml)"; then
    echo "⚠️  檢測到重大變更，建議先執行備份："
    echo "node scripts/backup-manager.js backup"
    echo ""
    echo "繼續提交請按 Enter，取消請按 Ctrl+C"
    read
fi
```

### 開發腳本整合
```json
{
  "scripts": {
    "safe-cleanup": "node scripts/backup-manager.js backup && node scripts/repository-cleanup.js",
    "safe-deploy": "node scripts/backup-manager.js backup && npm run deploy",
    "backup-and-test": "node scripts/backup-manager.js backup && npm test"
  }
}
```

## 📞 緊急恢復聯絡

如果遇到嚴重問題：

1. **停止所有操作**
2. **不要進行額外的提交**
3. **查看最近的備份**: `node scripts/recovery-manager.js list`
4. **恢復到安全狀態**: 使用最近的備份或 Git 歷史
5. **分析問題原因**
6. **更新備份策略**

---

## 📚 相關文檔

- [備份與恢復指南](./BACKUP_RECOVERY_GUIDE.md)
- [Repository 清理報告](./REPOSITORY_CLEANUP_REPORT.md)
- [項目狀態文檔](./PROJECT_STATUS.md)

---

*遵循這些最佳實踐，確保 HeartSync 項目的安全性和可恢復性。記住：備份是開發者最好的朋友！*
