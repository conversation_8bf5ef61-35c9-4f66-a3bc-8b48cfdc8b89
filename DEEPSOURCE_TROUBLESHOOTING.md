# DeepSource 故障排除指南

## 問題：DeepSource 顯示 'worker' 環境錯誤

### 已完成的修復步驟：

1. ✅ **更新 `.deepsource.toml` 配置**
   - 移除無效的環境設定
   - 確保只使用 `nodejs` 和 `browser` 環境
   - 添加全面的 `skip_doc_coverage` 模式

2. ✅ **創建 GitHub Actions 工作流程**
   - 新增 `.github/workflows/deepsource.yml`
   - 自動化 DeepSource 集成

3. ✅ **完全重新創建配置檔案**
   - 刪除舊的 `.deepsource.toml`
   - 創建全新的配置檔案

### 如果問題仍然存在，請嘗試：

#### 方法 1：手動觸發重新分析
1. 訪問 [DeepSource Dashboard](https://deepsource.io/gh/QutritSystems/heartsync-v2/)
2. 點擊 "Re-analyze" 或 "Trigger Analysis" 按鈕
3. 等待分析完成（通常需要 2-5 分鐘）

#### 方法 2：檢查 GitHub Actions
1. 訪問 GitHub repository 的 Actions 標籤
2. 確認 DeepSource workflow 正在運行
3. 檢查是否有任何錯誤訊息

#### 方法 3：驗證 DeepSource 設置
1. 確認 repository 已正確連接到 DeepSource
2. 檢查 DeepSource 中的 repository 設定
3. 確認分析器權限和設定

#### 方法 4：聯繫 DeepSource 支援
如果以上方法都無效，可能需要：
1. 檢查 DeepSource 狀態頁面
2. 聯繫 DeepSource 支援團隊
3. 報告配置快取問題

### 當前配置詳情：

**環境設定：**
- ✅ `nodejs` - 用於服務器端代碼
- ✅ `browser` - 用於客戶端代碼

**跳過的檔案類型：**
- 測試輔助檔案和腳本
- 配置檔案 (*.config.js, *.config.ts)
- 構建產物 (dist, build, node_modules)
- TypeScript 類型檔案 (*.d.ts, types)
- 報告和備份檔案

### 預期結果：
推送新的配置後，DeepSource 應該會自動開始新的分析，並且不再顯示 'worker' 環境錯誤。

### 監控步驟：
1. 檢查 GitHub Actions 是否成功運行
2. 監控 DeepSource dashboard 的狀態變化
3. 確認新的分析結果沒有配置錯誤
