# HeartSync v2 環境變數配置範例
# 複製此文件為 .env 並填入真實的 API Key

# Claude API Key (從 Anthropic 控制台獲取)
CLAUDE_API_KEY=sk-ant-api03-your-api-key-here

# OpenRouter API Key (從 OpenRouter 控制台獲取)
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-key-here
OPENROUTER_REFERER=https://heartsync.dev

# Cohere API Key (從 Cohere 控制台獲取)
COHERE_API_KEY=your-cohere-api-key-here

# Finlight API Key (從 Finlight 控制台獲取)
FINLIGHT_API_KEY=sk_your-finlight-api-key-here

# Tavily API Key (從 Tavily 控制台獲取) 
TAVILY_API_KEY=tvly-your-tavily-api-key-here

# 開發環境設置
ENVIRONMENT=development

# 可選：日誌級別
LOG_LEVEL=info

# 可選：Rate Limiting 設置
RATE_LIMIT_REQUESTS=10
RATE_LIMIT_WINDOW=60000

# 注意：
# 1. 絕不要將真實的 API Key 提交到版本控制
# 2. .env 文件已在 .gitignore 中排除
# 3. 生產環境請使用 wrangler secret 命令設置
