#!/bin/bash

# 🧪 HSN-50 Playwright 重複發送檢測 - 快速設置腳本

echo "🚀 開始設置 Playwright 測試環境..."

# 1. 安裝 Playwright (如果尚未安裝)
if ! command -v npx playwright &> /dev/null; then
    echo "📦 安裝 Playwright..."
    npm install -D @playwright/test
    npx playwright install
else
    echo "✅ Playwright 已安裝"
fi

# 2. 創建測試目錄結構
echo "📁 創建測試目錄..."
mkdir -p tests test-results

# 3. 檢查 HeartSync 服務是否運行
echo "🔍 檢查 HeartSync 服務狀態..."
if curl -f http://localhost:8787 >/dev/null 2>&1; then
    echo "✅ HeartSync 服務正在運行 (localhost:8787)"
else
    echo "⚠️ HeartSync 服務未運行，請先啟動:"
    echo "   npm run dev"
    echo ""
    echo "等待服務啟動後再執行測試..."
    exit 1
fi

# 4. 檢查測試頁面
echo "🔍 檢查測試頁面..."
if curl -f http://localhost:8787/duplicate-test >/dev/null 2>&1; then
    echo "✅ 重複發送測試頁面可訪問"
else
    echo "⚠️ 重複發送測試頁面不可訪問，請檢查路由配置"
    echo "嘗試的路由: /duplicate-test"
fi

# 5. 檢查 CLI Preview 路由
echo "🔍 檢查 CLI Preview 路由..."
if curl -f http://localhost:8787/CLI-preview >/dev/null 2>&1; then
    echo "✅ CLI Preview 路由可訪問"
else
    echo "⚠️ CLI Preview 路由不可訪問，請檢查路由配置"
    echo "嘗試的路由: /CLI-preview"
fi

# 6. 執行基礎檢測
echo "🧪 執行基礎功能檢測..."
cat > tests/basic-check.spec.js << 'EOF'
import { test, expect } from '@playwright/test';

test('基礎聊天界面檢測', async ({ page }) => {
  await page.goto('http://localhost:8787/duplicate-test');

  // 檢查頁面標題
  await expect(page).toHaveTitle(/.*HSN-50.*|.*重複發送測試.*/);

  // 檢查輸入框
  const input = page.locator('input[type="text"]').first();
  await expect(input).toBeVisible();

  // 檢查是否有發送機制 (按鈕或 Enter 鍵)
  const hasButton = await page.locator('button').count() > 0;
  const inputExists = await input.count() > 0;

  console.log(`檢測結果: 按鈕=${hasButton}, 輸入框=${inputExists}`);
  expect(hasButton || inputExists).toBeTruthy();
});
EOF

# 7. 執行基礎檢測
echo "🏃 執行基礎檢測..."
npx playwright test tests/basic-check.spec.js --reporter=line

# 8. 提供執行指令
echo ""
echo "🎉 設置完成！現在可以執行重複發送檢測:"
echo ""
echo "   # 執行完整測試套件"
echo "   npx playwright test hsn-50-duplicate-detection"
echo ""
echo "   # 以可視化模式執行"
echo "   npx playwright test hsn-50-duplicate-detection --ui"
echo ""
echo "   # 調試模式 (逐步執行)"
echo "   npx playwright test hsn-50-duplicate-detection --debug"
echo ""
echo "   # 只在瀏覽器中執行 (可觀察過程)"
echo "   npx playwright test hsn-50-duplicate-detection --headed"
echo ""
echo "   # 檢視測試報告"
echo "   npx playwright show-report"
echo ""

# 9. 創建快速測試腳本
cat > run-duplicate-test.sh << 'EOF'
#!/bin/bash

echo "🧪 執行聊天重複發送檢測..."
echo "📊 測試項目: 快速點擊、Enter鍵、網路延遲、Loading狀態、移動端觸控"
echo ""

# 確保服務運行
if ! curl -f http://localhost:8787 >/dev/null 2>&1; then
    echo "❌ HeartSync 服務未運行，請先執行: npm run dev"
    exit 1
fi

# 執行測試
npx playwright test hsn-50-duplicate-detection --reporter=line

echo ""
echo "✅ 測試完成！查看詳細報告:"
echo "   npx playwright show-report"
EOF

chmod +x run-duplicate-test.sh

echo "💡 快速測試腳本已創建: ./run-duplicate-test.sh"
echo ""
echo "🔧 如果需要調整設置，請編輯以下文件:"
echo "   - tests/hsn-50-duplicate-detection.spec.js (測試邏輯)"
echo "   - playwright.config.js (配置)"
echo ""
echo "📋 Linear 任務追蹤: HSN-50"
