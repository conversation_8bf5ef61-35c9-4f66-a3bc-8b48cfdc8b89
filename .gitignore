# prod
dist/

# dev
.yarn/
!.yarn/releases
.vscode/*
!.vscode/launch.json
!.vscode/*.code-snippets
.idea/workspace.xml
.idea/usage.statistics.xml
.idea/shelf

# deps
node_modules/
.wrangler

# env
.env
.env.production
.dev.vars

# logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# misc
.DS_Store

# 測試和調試檔案 (更精確的規則，避免誤刪重要路由)
# 只忽略明確的測試文件，不影響路由文件
test-*.html
test-*.json
debug-output/
traces/
performance-reports/
test-reports/
test-results/
# 明確指定的測試文件
test-claude-api.js
test-real-api.html
test-emergency-fix.html

# 臨時檔案
temp_*.json
*.tmp
*.temp

# 備份檔案 (更精確的規則，保留重要的路由備份)
# 只忽略明確標記為臨時備份的文件
*.backup
*.bak
*-temp-backup.*
*-auto-backup.*

# 歷史備份檔案 (日期格式)
*[Jan-*].js
*[Feb-*].js
*[Mar-*].js
*[Apr-*].js
*[May-*].js
*[Jun-*].js
*[Jul-*].js
*[Aug-*].js
*[Sep-*].js
*[Oct-*].js
*[Nov-*].js
*[Dec-*].js
*[backup-*].js
*[backup-before-*].js
*-backup.js
*-v*.js

# IDE 和工具配置 (某些可能需要保留)
.augment/
.clj-kondo/
.continue/
.lsp/

# 部署和監控檔案
deployment-report.json
monitoring.config.json

# 維護和分析報告
performance-report.json
security-report.json
maintenance-report-*.json
latest-maintenance-report.json

# CodeQL 分析結果
codeql-results/
*.sarif
*.codeql-db/

# 文檔草稿 (保留重要文檔)
*草稿*.md
*draft*.md
*temp*.md

# Logs
logs
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/

# Local backups (不同步到倉庫)
.local-backups/
backup-manifest-*.json
