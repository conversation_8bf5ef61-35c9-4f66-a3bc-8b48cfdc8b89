# Page snapshot

```yaml
- heading "🧪 HSN-50 重複發送問題測試" [level=1]
- paragraph: 測試目標：檢測並修復 AI 回應重複兩次的問題
- text: 10:20:15 PM
- strong: SYSTEM
- text: ": 測試環境已準備就緒 10:20:16 PM"
- strong: USER
- text: ": 延遲測試 1751725216156 10:20:17 PM"
- strong: AI
- text: ": AI 回應: 延遲測試 1751725216156 (回應時間: 10:20:17 PM) 10:20:17 PM"
- strong: ERROR
- text: ": 抱歉，AI 服務暫時不可用。請稍後再試。"
- textbox "輸入測試訊息..."
- button "發送" [disabled]
- button "快速連點測試"
- button "清除訊息"
- button "關閉調試"
- heading "🔍 調試信息" [level=3]
- strong: 總訊息數：
- text: "4"
- strong: 重複訊息數：
- text: "0"
- strong: API 調用次數：
- text: "3"
- strong: Loading 狀態：
- text: 否
- heading "最近的 API 調用記錄：" [level=4]
- text: 10:20:16 PM - 開始 AI 調用 10:20:17 PM - AI 回應成功 10:20:17 PM - 觸發異常條件 [錯誤]
```