# Page snapshot

```yaml
- heading "🧪 HSN-50 重複發送問題測試" [level=1]
- paragraph: 測試目標：檢測並修復 AI 回應重複兩次的問題
- text: 10:19:53 PM
- strong: SYSTEM
- text: ": 測試環境已準備就緒 10:19:54 PM"
- strong: SYSTEM
- text: ": 開始快速連點測試... 10:19:54 PM"
- strong: USER
- text: ": 快速連點測試訊息 10:19:55 PM"
- strong: AI
- text: ": AI 回應: 快速連點測試訊息 (回應時間: 10:19:55 PM) 10:19:55 PM"
- strong: ERROR
- text: ": 抱歉，AI 服務暫時不可用。請稍後再試。"
- textbox "輸入測試訊息..."
- button "發送" [disabled]
- button "快速連點測試"
- button "清除訊息"
- button "關閉調試"
- heading "🔍 調試信息" [level=3]
- strong: 總訊息數：
- text: "5"
- strong: 重複訊息數：
- text: "0"
- strong: API 調用次數：
- text: "3"
- strong: Loading 狀態：
- text: 否
- heading "最近的 API 調用記錄：" [level=4]
- text: 10:19:54 PM - 開始 AI 調用 10:19:55 PM - AI 回應成功 10:19:55 PM - 觸發異常條件 [錯誤]
```