# 🔍 HeartSync 錯誤監控與診斷指南

## 📋 概覽

為了幫助 AI 助手更好地診斷和修復 HeartSync 的問題，我們提供了多種錯誤監控和診斷工具。

---

## 🛠️ 方案 1: 內建錯誤監控系統 (已實現)

### ✅ 功能特性
- **自動錯誤收集**: 攔截所有 JavaScript 錯誤、Console 錯誤、Promise 拒絕
- **網路錯誤監控**: 監控 API 調用失敗
- **實時報告**: 自動發送錯誤到後端 API
- **本地存儲**: 錯誤數據保存到 localStorage
- **詳細日誌**: 包含堆疊追蹤、時間戳、用戶環境信息

### 🎯 使用方法

#### 1. 自動啟動
錯誤監控在頁面載入時自動啟動，無需手動配置。

#### 2. 手動控制
```javascript
// 在瀏覽器控制台中使用以下命令：

// 生成完整錯誤報告
window.getErrorReport()

// 開始記錄詳細日誌
window.startErrorRecording()

// 停止記錄詳細日誌
window.stopErrorRecording()

// 清除所有錯誤數據
window.clearErrors()

// 導出錯誤報告為 JSON 文件
window.errorMonitor.exportReport()
```

#### 3. 查看實時錯誤
```javascript
// 查看當前錯誤統計
console.log('錯誤數量:', window.errorMonitor.errors.length);
console.log('警告數量:', window.errorMonitor.warnings.length);

// 查看最新錯誤
console.log('最新錯誤:', window.errorMonitor.errors.slice(-5));
```

### 📊 錯誤報告格式
```json
{
  "summary": {
    "totalErrors": 5,
    "totalWarnings": 12,
    "sessionDuration": 120000,
    "url": "http://localhost:8787/chat-alpine",
    "timestamp": "2025-01-21T10:30:00.000Z"
  },
  "errors": [
    {
      "id": "err_1642761000000_abc123",
      "type": "javascript",
      "level": "error",
      "message": "Cannot read property 'enhancedChatApp' of undefined",
      "stack": "Error: ...",
      "timestamp": 1642761000000,
      "url": "http://localhost:8787/chat-alpine"
    }
  ],
  "systemInfo": {
    "userAgent": "Mozilla/5.0...",
    "viewport": { "width": 1920, "height": 1080 }
  }
}
```

---

## 🛠️ 方案 2: Playwright MCP Server (推薦給 AI 助手)

### ✅ 功能特性
- **遠程瀏覽器控制**: AI 可以直接操作瀏覽器
- **Console 錯誤獲取**: `browser_console_messages` 工具
- **頁面快照**: `browser_snapshot` 獲取頁面狀態
- **自動化測試**: 可以自動重現錯誤場景

### 🎯 安裝配置

#### 1. 安裝 Playwright MCP
```bash
npm install -g @playwright/mcp@latest
```

#### 2. VS Code/Cursor 配置
```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": [
        "@playwright/mcp@latest",
        "--headless",
        "--allowed-origins", "localhost:8787"
      ]
    }
  }
}
```

#### 3. AI 助手使用方法
```javascript
// AI 可以使用這些工具：
// 1. 導航到 HeartSync 頁面
browser_navigate("http://localhost:8787/chat-alpine")

// 2. 獲取 Console 錯誤
browser_console_messages()

// 3. 獲取頁面快照
browser_snapshot()

// 4. 截圖錯誤狀態
browser_take_screenshot()

// 5. 與頁面互動測試
browser_click("發送按鈕")
browser_type("測試訊息")
```

---

## 🛠️ 方案 3: 手動錯誤收集

### 📝 用戶操作步驟

#### 1. 重現錯誤
1. 訪問 `http://localhost:8787/chat-alpine`
2. 打開瀏覽器開發者工具 (F12)
3. 切換到 Console 標籤
4. 重現錯誤操作

#### 2. 收集錯誤信息
```javascript
// 在 Console 中執行：
window.getErrorReport()
```

#### 3. 導出錯誤報告
```javascript
// 導出為 JSON 文件
window.errorMonitor.exportReport()
```

#### 4. 分享錯誤信息
將導出的 JSON 文件或 Console 輸出分享給 AI 助手。

---

## 🔧 常見錯誤類型與解決方案

### 1. Alpine.js 相關錯誤
```javascript
// 錯誤: Cannot read property 'enhancedChatApp' of undefined
// 解決: 檢查函數定義是否正確載入
typeof window.enhancedChatApp // 應該返回 'function'
```

### 2. API 調用錯誤
```javascript
// 錯誤: Failed to fetch
// 解決: 檢查網路連接和 API 端點
fetch('/api/chat/send', { method: 'POST' })
```

### 3. 模型切換錯誤
```javascript
// 錯誤: Cannot read property 'selectedModel' of undefined
// 解決: 檢查 Alpine.js 數據綁定
Alpine.store('chat') // 檢查 store 狀態
```

---

## 📊 錯誤監控最佳實踐

### 🎯 對於用戶
1. **保持錯誤監控開啟**: 系統會自動收集錯誤
2. **定期檢查錯誤**: 使用 `window.getErrorReport()`
3. **重現錯誤時記錄**: 使用 `window.startErrorRecording()`
4. **分享詳細報告**: 導出完整的錯誤報告

### 🎯 對於 AI 助手
1. **使用 Playwright MCP**: 獲得最佳診斷能力
2. **結合多種工具**: 內建監控 + MCP + 手動收集
3. **分析錯誤模式**: 查看錯誤類型和頻率
4. **測試修復效果**: 修復後重新檢查錯誤數量

---

## 🚀 快速診斷檢查清單

### ✅ 基本檢查
```javascript
// 1. 檢查核心函數
typeof window.enhancedChatApp // 'function'

// 2. 檢查 Alpine.js
typeof Alpine // 'object'

// 3. 檢查錯誤監控
typeof window.errorMonitor // 'object'

// 4. 生成錯誤報告
window.getErrorReport()
```

### ✅ 進階檢查
```javascript
// 1. 檢查 API 連接
fetch('/api/chat/send', { 
  method: 'POST', 
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ message: 'test' })
})

// 2. 檢查模型配置
Alpine.store('chat')?.selectedModel

// 3. 檢查聊天歷史
localStorage.getItem('heartsync_chat_coffee-lover')
```

---

## 📞 故障排除

### 🚨 如果錯誤監控不工作
1. 檢查腳本載入: `window.errorMonitor`
2. 手動重新載入: `location.reload()`
3. 清除緩存: Ctrl+Shift+R

### 🚨 如果 Playwright MCP 不可用
1. 使用內建錯誤監控
2. 手動收集 Console 錯誤
3. 截圖錯誤狀態

### 🚨 如果需要即時協助
1. 執行 `window.getErrorReport()`
2. 複製完整輸出
3. 分享給 AI 助手

---

**這套錯誤監控系統讓 AI 助手能夠更準確地診斷和修復 HeartSync 的問題！** 🎯
