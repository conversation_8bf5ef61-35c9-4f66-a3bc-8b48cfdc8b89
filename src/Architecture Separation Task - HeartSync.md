# HeartSync 架構分離 + 里程碑 2 開發任務

## 🎯 任務概覽

### 核心目標
實施路由分離策略 (Option A)，將 Datastar + Jazz 用戶系統部署到獨立路由，避免與現有 Alpine.js HeartSync 功能產生衝突，同時完成里程碑 2 的核心功能開發。

### 架構分離理念
```yaml
風險隔離原則:
- 新功能不影響現有穩定功能
- 可獨立測試和調試各個系統
- 漸進式整合，用戶可選擇版本
- 清晰的技術邊界和責任劃分

多版本並存策略:
- / (主路由) - 現有 HeartSync + Alpine.js
- /users (新路由) - Datastar + Jazz 用戶系統
- /datastar-test (測試) - 技術驗證環境
- /v2 (未來) - 整合版本
```

## 🛠️ Phase 1: 路由分離架構實施

### 1.1 創建獨立用戶系統路由
```javascript
// 在現有 index.js 中添加新路由
app.get('/users', (c) => {
  return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HeartSync 用戶系統 v2.0</title>

    <!-- Datastar 核心 -->
    <script type="module" src="https://cdn.jsdelivr.net/npm/@sudodevnull/datastar@latest/dist/datastar.js"></script>

    <!-- Jazz 依賴 -->
    <script src="https://unpkg.com/jazz-browser@latest/dist/index.js"></script>

    <!-- 樣式 (復用 HeartSync 設計語言) -->
    <style>
        /* 繼承 HeartSync 的設計系統 */
        * { box-sizing: border-box; margin: 0; padding: 0; }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%);
            min-height: 100vh;
            color: #374151;
        }

        .container { max-width: 600px; margin: 0 auto; padding: 20px; }

        .header {
            text-align: center;
            padding: 40px 0;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ec4899, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #6b7280;
            font-size: 1rem;
        }

        .version-badge {
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.875rem;
            margin-top: 10px;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }

        .nav-link {
            display: inline-block;
            color: #6b7280;
            text-decoration: none;
            margin: 10px 15px 10px 0;
            padding: 8px 0;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .nav-link:hover {
            color: #ec4899;
            border-bottom-color: #ec4899;
        }

        .nav-link.active {
            color: #ec4899;
            border-bottom-color: #ec4899;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">HeartSync 用戶系統</h1>
            <p class="subtitle">Datastar + Jazz 驅動的本地優先架構</p>
            <span class="version-badge">v2.0 Alpha</span>

            <!-- 版本導航 -->
            <div style="margin-top: 20px;">
                <a href="/" class="nav-link">回到主應用</a>
                <a href="/users" class="nav-link active">用戶系統 v2</a>
                <a href="/datastar-test" class="nav-link">技術測試</a>
            </div>
        </header>

        <!-- 用戶系統主要內容將在此實現 -->
        <div id="user-system-app">
            <div class="card">
                <h2>🚀 Datastar + Jazz 用戶系統</h2>
                <p>獨立的用戶管理環境，使用最新的本地優先技術架構。</p>
            </div>
        </div>
    </div>

    <script>
        console.log('🎯 HeartSync 用戶系統 v2.0 載入中...');
        console.log('📋 當前路由: /users');
        console.log('🛠️ 技術棧: Datastar + Jazz');

        // Jazz 初始化將在里程碑 2 中實現
        // Datastar 用戶系統將在里程碑 2 中實現
    </script>
</body>
</html>`)
})
```

### 1.2 確保主應用穩定性
```javascript
// 確認主路由 '/' 功能完全正常
// 現有的 Alpine.js HeartSync 功能不受影響
// 添加版本導航，讓用戶可以在不同版本間切換
```

### 1.3 創建版本對照頁面 (可選)
```javascript
app.get('/versions', (c) => {
  return c.html(`
    <h1>HeartSync 版本對照</h1>
    <div class="version-grid">
      <div class="version-card">
        <h3>主應用 (v1.x)</h3>
        <p>Alpine.js + 現有功能</p>
        <a href="/">進入使用</a>
      </div>
      <div class="version-card">
        <h3>用戶系統 (v2.0)</h3>
        <p>Datastar + Jazz</p>
        <a href="/users">進入使用</a>
      </div>
    </div>
  `)
})
```

## 🚀 Phase 2: 里程碑 2 核心功能開發

### 2.1 Jazz 用戶身份系統
```javascript
// 在 /users 路由中實現
const userSystemInitialization = `
// Jazz 客戶端初始化
async function initJazzUserSystem() {
    try {
        // 創建 Jazz 應用實例
        const jazz = window.Jazz.createJazzApp({
            auth: 'guest',
            peer: 'wss://cloud.jazz.tools'
        });

        console.log('✅ Jazz 連接成功');

        // 創建用戶資料映射
        const userProfile = jazz.createMap({
            id: generateUserId(),
            displayName: '',
            avatar: {
                type: 'gradient',
                value: 'bg-pink-200'
            },
            preferences: {
                theme: 'auto',
                language: 'zh-TW'
            },
            profile: {
                bio: '',
                joinDate: Date.now()
            }
        });

        // 將 Jazz 實例掛載到全域
        window.jazzClient = jazz;
        window.userProfile = userProfile;

        console.log('🎯 用戶系統初始化完成');
        return true;

    } catch (error) {
        console.error('❌ Jazz 初始化失敗:', error);
        return false;
    }
}

// 自動初始化
document.addEventListener('DOMContentLoaded', initJazzUserSystem);
`;
```

### 2.2 Datastar 用戶界面實現
```html
<!-- 添加到 /users 路由的 user-system-app div 中 -->
<div data-store="{
    user: {
        id: null,
        displayName: '',
        avatar: { type: 'gradient', value: 'bg-pink-200' },
        profile: { bio: '' }
    },
    ui: {
        editMode: false,
        loading: false,
        syncStatus: 'connected'
    }
}">
    <!-- 用戶資料展示區 -->
    <div class="card">
        <div class="user-profile-header">
            <div class="avatar-display"
                 data-class="user.avatar.value"
                 data-text="user.displayName ? user.displayName.slice(0,2) : '用'">
            </div>
            <div class="user-info">
                <h3 data-text="user.displayName || '未設定名稱'"></h3>
                <p data-text="user.profile.bio || '還沒有個人簡介'"></p>
            </div>
            <button data-on-click="$ui.editMode = !$ui.editMode"
                    data-text="$ui.editMode ? '取消編輯' : '編輯資料'">
            </button>
        </div>
    </div>

    <!-- 編輯模式 -->
    <div data-show="$ui.editMode" class="card edit-form">
        <h3>編輯個人資料</h3>

        <div class="form-group">
            <label>顯示名稱</label>
            <input type="text"
                   data-model="$user.displayName"
                   placeholder="輸入你的名稱"
                   maxlength="20">
        </div>

        <div class="form-group">
            <label>個人簡介</label>
            <textarea data-model="$user.profile.bio"
                      placeholder="介紹一下自己吧..."
                      maxlength="100"></textarea>
        </div>

        <div class="form-group">
            <label>頭像風格</label>
            <div class="avatar-options">
                <button data-on-click="$user.avatar.value = 'bg-pink-200'"
                        class="avatar-option bg-pink-200">粉</button>
                <button data-on-click="$user.avatar.value = 'bg-blue-200'"
                        class="avatar-option bg-blue-200">藍</button>
                <button data-on-click="$user.avatar.value = 'bg-green-200'"
                        class="avatar-option bg-green-200">綠</button>
                <button data-on-click="$user.avatar.value = 'bg-purple-200'"
                        class="avatar-option bg-purple-200">紫</button>
            </div>
        </div>

        <div class="form-actions">
            <button data-on-click="saveUserProfile()"
                    data-class="$ui.loading ? 'loading' : ''"
                    class="save-btn">
                <span data-show="!$ui.loading">儲存變更</span>
                <span data-show="$ui.loading">儲存中...</span>
            </button>
        </div>
    </div>

    <!-- 同步狀態指示 -->
    <div class="sync-indicator" data-show="$ui.syncStatus">
        <span data-text="getSyncStatusText($ui.syncStatus)"></span>
    </div>
</div>
```

### 2.3 本地優先邏輯實現
```javascript
// 添加到 /users 路由的 script 區域
const userSystemLogic = `
// 用戶資料操作函數
window.saveUserProfile = async function() {
    try {
        // 設置載入狀態
        datastar.store.ui.loading = true;
        datastar.store.ui.syncStatus = 'syncing';

        // 本地立即更新 (極限本地化)
        const userData = datastar.store.user;
        localStorage.setItem('heartsync_user_v2', JSON.stringify(userData));

        // 背景同步到 Jazz
        if (window.userProfile) {
            await window.userProfile.update(userData);
            console.log('✅ 資料已同步到 Jazz');
        }

        // 更新 UI 狀態
        datastar.store.ui.loading = false;
        datastar.store.ui.editMode = false;
        datastar.store.ui.syncStatus = 'connected';

        // 用戶反饋
        showNotification('資料已儲存', 'success');

    } catch (error) {
        console.error('❌ 儲存失敗:', error);
        datastar.store.ui.loading = false;
        datastar.store.ui.syncStatus = 'error';
        showNotification('儲存失敗，請稍後再試', 'error');
    }
};

// 從本地載入用戶資料
window.loadUserFromLocal = function() {
    const savedUser = localStorage.getItem('heartsync_user_v2');
    if (savedUser) {
        try {
            const userData = JSON.parse(savedUser);
            datastar.store.user = userData;
            console.log('📋 已載入本地用戶資料');
        } catch (error) {
            console.error('❌ 本地資料載入失敗:', error);
        }
    }
};

// 同步狀態文字
window.getSyncStatusText = function(status) {
    const statusMap = {
        'connected': '✅ 已連線',
        'syncing': '🔄 同步中',
        'offline': '📱 離線模式',
        'error': '❌ 同步錯誤'
    };
    return statusMap[status] || '❓ 未知狀態';
};

// 簡單通知系統
window.showNotification = function(message, type = 'info') {
    // 簡單的通知實現
    console.log(\`📢 \${type.toUpperCase()}: \${message}\`);
    // 可以後續擴展為真實的 UI 通知
};

// 生成用戶 ID
window.generateUserId = function() {
    return 'user_' + Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
};

// 頁面載入時自動執行
document.addEventListener('DOMContentLoaded', function() {
    // 載入本地資料
    loadUserFromLocal();

    // 如果沒有用戶 ID，生成一個
    if (!datastar.store.user.id) {
        datastar.store.user.id = generateUserId();
    }
});

// 離線檢測
window.addEventListener('online', function() {
    datastar.store.ui.syncStatus = 'connected';
    console.log('🌐 網路已連線');
});

window.addEventListener('offline', function() {
    datastar.store.ui.syncStatus = 'offline';
    console.log('📱 進入離線模式');
});
`;
```

## 📊 驗收標準與測試

### 功能驗收清單
- [ ] `/users` 路由正常運作，不影響主應用
- [ ] Jazz 連接成功，無控制台錯誤
- [ ] 用戶資料可以編輯和本地保存
- [ ] 頭像選擇功能正常
- [ ] 離線模式正確處理
- [ ] 版本間導航順暢

### 技術驗收清單
- [ ] Datastar 狀態管理穩定
- [ ] Jazz 同步機制無錯誤
- [ ] 本地存儲機制完整
- [ ] 網路狀態檢測正確
- [ ] 錯誤處理機制完善

### 用戶體驗驗收
- [ ] 界面設計與 HeartSync 風格一致
- [ ] 操作流程直觀易懂
- [ ] 響應速度滿意 (< 100ms)
- [ ] 編輯模式切換順暢
- [ ] 狀態反饋及時明確

## 🎯 完成後的系統狀態

### 路由結構
```
/ - HeartSync 主應用 (Alpine.js, 穩定運行)
/users - 用戶系統 v2.0 (Datastar + Jazz, 新功能)
/datastar-test - 技術測試環境
/versions - 版本對照頁面 (可選)
```

### 技術棧隔離
```yaml
主應用技術棧:
- Hono + Alpine.js + 本地存儲
- 所有現有功能保持穩定

用戶系統技術棧:
- Hono + Datastar + Jazz
- 本地優先 + 即時同步

未來整合:
- 數據共享機制
- 統一用戶身份
- 漸進式功能遷移
```

## 🚀 下一步預告

完成此階段後，我們將具備：
- ✅ 穩定的多版本並存架構
- ✅ 功能完整的 Datastar + Jazz 用戶系統
- ✅ 本地優先的數據處理能力
- ✅ 為 Claude API 聊天功能做好準備

**預計完成時間**: 60-90分鐘
**主要產出**: 獨立運行的用戶系統 v2.0
**戰略價值**: 驗證新技術棧，為完整整合做準備

準備開始這個架構分離和功能開發的任務！🎯
