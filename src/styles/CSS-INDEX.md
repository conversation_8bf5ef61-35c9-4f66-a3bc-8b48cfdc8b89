# 🎨 HSN CLI Preview - CSS 樣式索引

## 📋 目錄結構 (Table of Contents)

### 1. 🎯 CSS 變數定義 (CSS Variables)
- **位置**: `:root` 選擇器
- **行數**: 112-199
- **內容**: 主色調、霓虹色彩、背景色系、間距、字體、動畫時間、陰影效果
- **搜索標籤**: `variables`, `colors`, `spacing`, `typography`

### 2. 🔧 基礎重置 (Base Reset)
- **位置**: `*`, `body`
- **行數**: 200-220
- **內容**: 全域重置、基礎字體、復古風格效果
- **搜索標籤**: `reset`, `base`, `retro`, `pixelated`

### 3. 🖥️ 終端容器 (Terminal Container)
- **位置**: `.terminal-container`
- **行數**: 221-230
- **內容**: 主容器佈局、背景漸層
- **搜索標籤**: `container`, `terminal`, `layout`

### 4. 📝 標題區域 (Header Section)
- **位置**: `.terminal-header`, `.terminal-title`, `.terminal-subtitle`
- **行數**: 231-350
- **內容**: 標題樣式、哥德式裝飾、CRT 效果、8-bit 像素風
- **搜索標籤**: `header`, `title`, `gothic`, `crt`, `8bit`

### 5. 💬 聊天介面 (Chat Interface)
- **位置**: `.chat-container`, `.chat-history`, `.message-*`
- **行數**: 351-421
- **內容**: 聊天容器、訊息樣式、知識增強顯示
- **搜索標籤**: `chat`, `message`, `knowledge`, `tavily`

### 6. 🎭 水母頭像系統 (Jellyfish Avatar)
- **位置**: `.mini-jellyfish-avatar`, `.jellyfish-avatar`, `.sphere-layer`
- **行數**: 422-519 + 925-1120
- **內容**: 3D 透明水母、霓虹發光、打字狀態變化
- **搜索標籤**: `jellyfish`, `avatar`, `3d`, `neon`, `transparent`

### 7. 🌈 動畫定義 (Animations)
- **位置**: `@keyframes`
- **行數**: 520-696
- **內容**: 水母動畫、CRT 效果、哥德式流動、脈衝效果
- **搜索標籤**: `animation`, `keyframes`, `glow`, `pulse`, `wave`

### 8. 📱 響應式設計 (Responsive)
- **位置**: `@media`, 滾動條樣式
- **行數**: 860-923
- **內容**: 手機適配、復古風格滾動條
- **搜索標籤**: `responsive`, `mobile`, `scrollbar`, `retro`

### 9. 🎨 主題色彩 (Theme Colors)
- **位置**: `.user-color-*`
- **行數**: 1134-1158
- **內容**: 個性化用戶色彩系統
- **搜索標籤**: `theme`, `colors`, `user`, `personalization`

### 10. 🔍 搜索優化 (Search Optimization)
- **位置**: 註釋區塊
- **行數**: 1159-1181
- **內容**: 搜索關鍵詞索引、標籤系統
- **搜索標籤**: `search`, `optimization`, `keywords`, `tags`

## 🏷️ 搜索關鍵詞索引

### 🎨 視覺效果
- `terminal`, `retro`, `amber`, `crt`, `neon`
- `gothic`, `ornament`, `pixel`, `8bit`
- `glow`, `pulse`, `wave`, `flicker`

### 🐙 水母系統
- `jellyfish`, `avatar`, `3d`, `transparent`
- `sphere`, `tentacle`, `core`, `mini`

### 💬 聊天功能
- `chat`, `ai`, `knowledge`, `tavily`
- `message`, `user`, `system`, `loading`

### 🎭 動畫效果
- `animation`, `keyframes`, `transition`
- `hover`, `active`, `focus`

### 📱 響應式
- `responsive`, `mobile`, `desktop`
- `media`, `breakpoint`

### 🔧 技術架構
- `css-variables`, `modular`, `organized`
- `performance`, `optimization`

## 📊 性能優化建議

### ✅ 已實施
1. **CSS 變數系統** - 統一色彩和間距管理
2. **模組化註釋** - 清晰的區塊劃分
3. **搜索優化標記** - 快速定位樣式
4. **響應式設計** - 移動端適配

### 🔄 未來改進
1. **文件拆分** - 將大型 CSS 拆分為多個模組
2. **CSS-in-JS** - 考慮動態樣式需求
3. **性能監控** - 添加 CSS 性能指標
4. **主題切換** - 實現多主題支持

## 🛠️ 維護指南

### 📝 添加新樣式
1. 確定樣式所屬的區塊
2. 添加適當的註釋和搜索標籤
3. 使用 CSS 變數而非硬編碼值
4. 更新此索引文檔

### 🔍 查找樣式
1. 使用搜索標籤快速定位
2. 參考行數範圍
3. 檢查相關的 CSS 變數

### 🎨 修改主題
1. 優先修改 CSS 變數
2. 保持色彩系統一致性
3. 測試響應式效果
4. 驗證動畫性能
