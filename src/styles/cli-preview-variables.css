/* ========================================
 * 🎨 HSN CLI PREVIEW - CSS 變數定義
 * ========================================
 * 
 * 📋 變數索引 (Variables Index)
 * ────────────────────────────────────────
 * 1. 🎨 主色調系統 (Primary Colors)
 * 2. 🌈 霓虹色彩 (Neon Colors)
 * 3. 🖤 背景色系 (Background Colors)
 * 4. 📏 間距系統 (Spacing System)
 * 5. 🔤 字體系統 (Typography)
 * 6. ⏱️ 動畫時間 (Animation Timing)
 * 7. 🎭 陰影效果 (Shadow Effects)
 * 
 * 🏷️ 搜索標籤: variables, colors, spacing, typography
 * ======================================== */

:root {
	/* ========================================
     * 1. 🎨 主色調系統 (Primary Colors)
     * ======================================== */
	--color-primary-amber: #ffb000;
	--color-secondary-amber: #ff8000;
	--color-tertiary-amber: #e6a366;
	--color-muted-amber: #cc8833;
	--color-dark-amber: #996633;

	/* ========================================
     * 2. 🌈 霓虹色彩 (Neon Colors)
     * ======================================== */
	--color-neon-cyan: #00ffff;
	--color-neon-magenta: #ff00ff;
	--color-neon-yellow: #ffff00;
	--color-neon-green: #00ff41;

	/* ========================================
     * 3. 🖤 背景色系 (Background Colors)
     * ======================================== */
	--bg-primary: linear-gradient(135deg, #0f0f0f 0%, #1f1f1f 100%);
	--bg-secondary: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
	--bg-tertiary: rgba(0, 0, 0, 0.95);

	/* ========================================
     * 4. 📏 間距系統 (Spacing System)
     * ======================================== */
	--spacing-xs: 4px;
	--spacing-sm: 8px;
	--spacing-md: 12px;
	--spacing-lg: 20px;
	--spacing-xl: 30px;

	/* ========================================
     * 5. 🔤 字體系統 (Typography)
     * ======================================== */
	--font-mono:
		"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New",
		monospace;
	--font-size-xs: 10px;
	--font-size-sm: 12px;
	--font-size-base: 14px;
	--font-size-lg: 18px;

	/* ========================================
     * 6. ⏱️ 動畫時間 (Animation Timing)
     * ======================================== */
	--anim-fast: 0.3s;
	--anim-normal: 1s;
	--anim-slow: 3s;
	--anim-very-slow: 8s;

	/* ========================================
     * 7. 🎭 陰影效果 (Shadow Effects)
     * ======================================== */
	--shadow-glow-amber: 0 0 15px var(--color-primary-amber);
	--shadow-glow-strong:
		0 0 25px rgba(255, 176, 0, 0.5), 0 0 35px rgba(255, 128, 0, 0.3);
}

/* ========================================
 * 🌈 個性化用戶色彩系統
 * ======================================== */
.user-color-1 {
	--user-color-primary: var(--color-neon-green);
	--user-color-secondary: #41ff00;
}

.user-color-2 {
	--user-color-primary: #ff6600;
	--user-color-secondary: #ff9900;
}

.user-color-3 {
	--user-color-primary: var(--color-neon-cyan);
	--user-color-secondary: #0099ff;
}

.user-color-4 {
	--user-color-primary: var(--color-neon-magenta);
	--user-color-secondary: #ff66ff;
}

.user-color-5 {
	--user-color-primary: var(--color-neon-yellow);
	--user-color-secondary: #ffcc00;
}
