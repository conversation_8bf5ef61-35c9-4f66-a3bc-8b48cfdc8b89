🚨 HeartSync 關鍵修復腳本開始執行...
chat-alpine:19 🔍 檢查當前狀態:
chat-alpine:20    - window.enhancedChatApp 類型: undefined
chat-alpine:21    - Alpine 是否載入: false
chat-alpine:25 🔧 定義 enhancedChatApp 函數...
chat-alpine:303 ✅ enhancedChatApp 函數已定義
chat-alpine:28 ✅ enhancedChatApp 函數被調用
chat-alpine:311 ✅ 函數測試成功，返回對象包含屬性: (10) ['messages', 'currentPersona', 'inputText', 'isLoading', 'isTyping', 'connectionStatus', 'conversationLength', 'selectedModel', 'showAdvancedSettings', 'selectedAdvancedModel']
chat-alpine:316 🎯 關鍵修復腳本執行完成
chat-alpine:342 🔧 關鍵修復腳本載入完成，可使用 diagnoseAlpineIssues() 進行診斷
chat-alpine:891 Uncaught SyntaxError: Invalid regular expression: missing / (at chat-alpine:891:61)
(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation
(anonymous) @ (index):64
(anonymous) @ (index):64
chat-alpine:2135 Uncaught SyntaxError: Unexpected token '}' (at chat-alpine:2135:13)
chat-alpine:28 ✅ enhancedChatApp 函數被調用
chat-alpine:77 🚀 HeartSync 初始化開始
chat-alpine:81 ✅ HeartSync 初始化完成
chat-alpine:77 🚀 HeartSync 初始化開始
chat-alpine:81 ✅ HeartSync 初始化完成
Error: Doc not initialized yet
    at content.bundle.js:204:199558
    at new Promise (<anonymous>)
    at h.<anonymous> (content.bundle.js:204:199437)
    at Generator.next (<anonymous>)
    at content.bundle.js:204:191492
    at new Promise (<anonymous>)
    at B (content.bundle.js:204:191237)
    at h.submitOps (content.bundle.js:204:199375)
    at gA (content.bundle.js:2:13115)
    at h.submitOps (content.bundle.js:2:12935)
Promise.catch
disposers.name @ content.bundle.js:725
gA @ content.bundle.js:2
Send diff operations on node content change @ content.bundle.js:2
M @ content.bundle.js:2
(anonymous) @ content.bundle.js:2
e.runReaction_ @ content.bundle.js:2
ie @ content.bundle.js:2
ne @ content.bundle.js:2
(anonymous) @ content.bundle.js:2
Ac @ content.bundle.js:102
ne @ content.bundle.js:2
Ee @ content.bundle.js:2
Ae @ content.bundle.js:2
wA @ content.bundle.js:2
gA @ content.bundle.js:2
updateNodeContents @ content.bundle.js:2
r @ content.bundle.js:204
(anonymous) @ content.bundle.js:204
callback @ content.bundle.js:204
observerCallback @ content.bundle.js:204
(anonymous) @ content.bundle.js:204
childList
value @ content.bundle.js:725
content.bundle.js:102 Ignoring unrecognized message {app: true, event: 'user-update', data: {…}}
l.handleMessage @ content.bundle.js:102
A.onmessage @ content.bundle.js:102
A._handleMessage @ content.bundle.js:204
content.bundle.js:102 Ignoring unrecognized message {app: true, event: 'create_document', data: {…}}
