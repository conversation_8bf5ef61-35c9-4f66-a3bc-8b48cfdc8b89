// src/instant.ts - InstantDB 配置
import { init } from '@instantdb/react';

// ⚠️ 替換為您的實際 InstantDB App ID
const APP_ID = '1c81583f-e8de-4448-982e-1c616a2b3432';

// 定義資料 Schema
const schema = {
	users: {
		id: 'string',
		email: 'string',
		name: 'string',
		avatarColor: 'string',
		isGuest: 'boolean',
		createdAt: 'number',
	},
	posts: {
		id: 'string',
		content: 'string',
		authorId: 'string',
		tags: 'array',
		likesCount: 'number',
		commentsCount: 'number',
		createdAt: 'number',
	},
	comments: {
		id: 'string',
		postId: 'string',
		authorId: 'string',
		content: 'string',
		createdAt: 'number',
	},
	likes: {
		id: 'string',
		postId: 'string',
		userId: 'string',
		createdAt: 'number',
	},
};

// 初始化 InstantDB
export const db = init({
	appId: APP_ID,
	schema,
});

// 型別定義
export type User = {
	id: string;
	email: string;
	name: string;
	avatarColor: string;
	isGuest: boolean;
	createdAt: number;
};

export type Post = {
	id: string;
	content: string;
	authorId: string;
	tags: string[];
	likesCount: number;
	commentsCount: number;
	createdAt: number;
};

export type Comment = {
	id: string;
	postId: string;
	authorId: string;
	content: string;
	createdAt: number;
};

// 設置初始測試資料
export const seedInitialData = async () => {
	console.log('🌱 正在設置初始資料...');

	try {
		// 創建測試用戶
		await db.transact([
			db.users.create({
				id: 'user1',
				email: '<EMAIL>',
				name: '咖啡愛好者小雅',
				avatarColor: 'bg-pink-200',
				isGuest: false,
				createdAt: Date.now(),
			}),
			db.users.create({
				id: 'user2',
				email: '<EMAIL>',
				name: '愛讀書的晴子',
				avatarColor: 'bg-purple-200',
				isGuest: false,
				createdAt: Date.now(),
			}),
			db.users.create({
				id: 'user3',
				email: '<EMAIL>',
				name: '健身達人Amy',
				avatarColor: 'bg-blue-200',
				isGuest: false,
				createdAt: Date.now(),
			}),
		]);

		// 創建測試貼文
		await db.transact([
			db.posts.create({
				id: 'post1',
				content: '今天去了一家新開的咖啡廳，拿鐵的拉花超美！有人也喜歡探索新店嗎？☕️',
				authorId: 'user1',
				tags: ['咖啡', '美食探索'],
				likesCount: 12,
				commentsCount: 3,
				createdAt: Date.now() - 2 * 60 * 60 * 1000, // 2小時前
			}),
			db.posts.create({
				id: 'post2',
				content: '剛讀完村上春樹的《第一人稱單數》，每個短篇都像一面鏡子。想找人討論劇情和感想！📚',
				authorId: 'user2',
				tags: ['閱讀', '文學', '村上春樹'],
				likesCount: 18,
				commentsCount: 7,
				createdAt: Date.now() - 5 * 60 * 60 * 1000, // 5小時前
			}),
			db.posts.create({
				id: 'post3',
				content:
					'今天完成了5K晨跑，沿著河濱公園的路線真的很棒！運動完心情超好，誰也想一起晨跑呢？🏃‍♀️',
				authorId: 'user3',
				tags: ['運動', '健康生活', '晨跑'],
				likesCount: 25,
				commentsCount: 4,
				createdAt: Date.now() - 8 * 60 * 60 * 1000, // 8小時前
			}),
		]);

		console.log('✅ 初始資料設置完成！');
	} catch (error) {
		console.error('❌ 設置初始資料失敗:', error);
	}
};
