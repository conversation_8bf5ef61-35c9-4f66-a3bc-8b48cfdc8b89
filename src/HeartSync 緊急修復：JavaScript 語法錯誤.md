# HeartSync 緊急修復：JavaScript 語法錯誤

## 🚨 問題診斷

從錯誤報告看，有兩個主要問題：

1. **第 551 行：未結束的正則表達式** - `unterminated regular expression literal`
2. **第 1761 行：預期表達式但得到 '}'** - `expected expression, got '}'`
3. **Alpine.js 無法找到 `window.enhancedChatApp` 函數**

## 🔧 快速修復方案

### 修復 1：檢查正則表達式（第 551 行附近）

```javascript
// ❌ 錯誤：未結束的正則表達式
const pattern = /為什麼|怎麼會|如何|請解釋|分析|比較|評估/;  // 缺少結束斜線

// ✅ 正確：
const complexPatterns = [
  /為什麼|怎麼會|如何|請解釋|分析|比較|評估/,
  /哲學|意義|目的|本質|深層/,
  /複雜|困難|挑戰|問題/
];
```

### 修復 2：檢查物件或陣列語法（第 1761 行附近）

```javascript
// ❌ 錯誤：多餘的逗號或括號
const config = {
  model: 'some-model',
  temperature: 0.7,
}; // 這裡可能有多餘的 }

// ✅ 正確：
const ADVANCED_MODEL_OPTIONS = {
  'deepseek-v3': {
    model: 'deepseek/deepseek-v3',
    displayName: 'DeepSeek V3',
    description: '程式設計與技術討論',
    temperature: 0.3,
    provider: 'openrouter'
  },
  // ... 其他選項
};
```

### 修復 3：確保 Alpine.js 函數正確定義

```javascript
// 在 <script> 標籤的最外層定義
window.enhancedChatApp = function() {
  return {
    // 基礎狀態
    currentPersona: 'coffee-lover',
    messages: [],
    inputText: '',
    isLoading: false,
    isTyping: false,
    connectionStatus: '已連線',
    conversationLength: 0,

    // 模型相關狀態
    selectedModel: 'valkyrie',
    showAdvancedSettings: false,
    selectedAdvancedModel: null,
    useClaudeSonnet4: false,
    useClaudeHaikuDirect: false,
    showExperimentalModels: false,

    // 其他狀態
    enableModelFallback: true,
    showModelInfo: true,
    autoScroll: true,
    showPersonaSwitch: false,
    targetPersona: null,
    personaChangeWarning: false,

    // 進階模型選項
    advancedModels: {
      'deepseek-v3': {
        model: 'deepseek/deepseek-v3',
        displayName: 'DeepSeek V3',
        description: '程式設計與技術討論',
        temperature: 0.3,
        provider: 'openrouter'
      },
      'claude-haiku-direct': {
        model: 'claude-3-haiku-20240307',
        displayName: 'Claude 3 Haiku (直連)',
        description: '快速回應，成本優化',
        temperature: 0.6,
        provider: 'claude-api'
      },
      'claude-sonnet': {
        model: 'anthropic/claude-3-5-sonnet-20240620',
        displayName: 'Claude Sonnet 3.5',
        description: '深度對話，綜合能力',
        temperature: 0.7,
        provider: 'openrouter'
      },
      'gpt-4o-mini': {
        model: 'openai/gpt-4o-mini',
        displayName: 'GPT-4o Mini',
        description: '經濟實惠，快速回應',
        temperature: 0.7,
        provider: 'openrouter'
      },
      'gpt-4o': {
        model: 'openai/gpt-4o',
        displayName: 'GPT-4o',
        description: '最強大的對話能力',
        temperature: 0.7,
        provider: 'openrouter'
      }
    },

    // 初始化方法
    init() {
      console.log('🚀 HeartSync 初始化開始');
      this.loadModelPreference();
      this.loadChatHistory();
      console.log('✅ HeartSync 初始化完成');
    },

    // 人格相關方法
    getPersonaName(persona) {
      const names = {
        'coffee-lover': '小雅',
        'tech-enthusiast': 'Alex',
        'bookworm': '晴子'
      };
      return names[persona] || '未知';
    },

    getPersonaEmoji(persona) {
      const emojis = {
        'coffee-lover': '☕',
        'tech-enthusiast': '💻',
        'bookworm': '📚'
      };
      return emojis[persona] || '🤖';
    },

    getPersonaDescription(persona) {
      const descriptions = {
        'coffee-lover': '溫暖的咖啡愛好者',
        'tech-enthusiast': '技術達人',
        'bookworm': '愛讀書的文學少女'
      };
      return descriptions[persona] || '';
    },

    getPersonaAvatarClass(persona) {
      return `avatar-${persona}`;
    },

    // 模型相關方法
    getCurrentAPIProvider() {
      if (this.useClaudeSonnet4) return 'Claude Sonnet 4 (實驗性)';
      if (this.useClaudeHaikuDirect) return 'Claude API 直連';
      return 'OpenRouter';
    },

    getCurrentModelDisplay() {
      if (this.selectedAdvancedModel && this.showAdvancedSettings) {
        return this.advancedModels[this.selectedAdvancedModel]?.displayName || 'Unknown';
      }
      return this.selectedModel === 'valkyrie' ? 'Valkyrie 49B' : 'Anubis Pro 105B';
    },

    getModelProvider() {
      if (this.useClaudeHaikuDirect) return 'Anthropic';
      return 'OpenRouter';
    },

    getModelSpecialty() {
      const specialties = {
        'valkyrie': '平衡對話',
        'anubis': '深度思考',
        'deepseek-v3': '程式設計',
        'claude-haiku-direct': '快速回應',
        'claude-sonnet': '綜合能力'
      };
      const key = this.selectedAdvancedModel || this.selectedModel;
      return specialties[key] || '通用對話';
    },

    isCostSavingMode() {
      return this.selectedModel === 'valkyrie' || this.useClaudeHaikuDirect;
    },

    isProMode() {
      return this.selectedModel === 'anubis' || this.selectedAdvancedModel === 'gpt-4o';
    },

    // 人格統計
    getPersonaStats(persona) {
      // 這裡可以從 localStorage 載入實際統計
      return {
        hasHistory: false,
        conversationCount: 0
      };
    },

    // 切換人格
    switchPersona(persona) {
      if (this.currentPersona === persona) return;

      this.targetPersona = persona;
      if (this.messages.length > 0) {
        this.showPersonaSwitch = true;
      } else {
        this.confirmPersonaSwitch();
      }
    },

    confirmPersonaSwitch() {
      this.currentPersona = this.targetPersona;
      this.messages = [];
      this.loadChatHistory();
      this.showPersonaSwitch = false;
      this.targetPersona = null;
    },

    cancelPersonaSwitch() {
      this.showPersonaSwitch = false;
      this.targetPersona = null;
    },

    // 模型切換
    handleModelChange() {
      console.log('🔄 模型切換到:', this.selectedModel);
      this.selectedAdvancedModel = null;
      this.saveModelPreference();
    },

    // 儲存和載入偏好
    saveModelPreference() {
      const preference = {
        simpleModel: this.selectedModel,
        advancedModel: this.selectedAdvancedModel,
        showAdvanced: this.showAdvancedSettings,
        useClaudeHaikuDirect: this.useClaudeHaikuDirect,
        useClaudeSonnet4: this.useClaudeSonnet4
      };
      localStorage.setItem('heartsync_model_preference', JSON.stringify(preference));
    },

    loadModelPreference() {
      const saved = localStorage.getItem('heartsync_model_preference');
      if (saved) {
        try {
          const preference = JSON.parse(saved);
          this.selectedModel = preference.simpleModel || 'valkyrie';
          this.selectedAdvancedModel = preference.advancedModel;
          this.showAdvancedSettings = preference.showAdvanced || false;
          this.useClaudeHaikuDirect = preference.useClaudeHaikuDirect || false;
          this.useClaudeSonnet4 = preference.useClaudeSonnet4 || false;
        } catch (e) {
          console.log('載入模型偏好失敗，使用預設值');
        }
      }
    },

    // 載入聊天記錄
    loadChatHistory() {
      const historyKey = `heartsync_chat_${this.currentPersona}`;
      const saved = localStorage.getItem(historyKey);
      if (saved) {
        try {
          this.messages = JSON.parse(saved);
          this.conversationLength = this.messages.length;
        } catch (e) {
          console.log('載入聊天記錄失敗');
          this.messages = [];
        }
      }
    },

    // 發送訊息
    async sendMessage() {
      if (!this.inputText.trim() || this.isLoading) return;

      const message = this.inputText.trim();
      this.inputText = '';

      // 添加用戶訊息
      this.messages.push({
        content: message,
        isUser: true,
        timestamp: new Date().toISOString()
      });

      this.isLoading = true;
      this.isTyping = true;

      try {
        // 決定使用的模型
        let modelPreference = this.selectedModel;
        if (this.showAdvancedSettings && this.selectedAdvancedModel) {
          modelPreference = this.selectedAdvancedModel;
        }
        if (this.useClaudeHaikuDirect) {
          modelPreference = 'claude-haiku-direct';
        }
        if (this.useClaudeSonnet4) {
          modelPreference = 'claude-sonnet-4';
        }

        const response = await fetch('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: message,
            persona: this.currentPersona,
            conversationHistory: this.messages.slice(-10),
            modelPreference: modelPreference
          })
        });

        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        // 添加 AI 回應
        this.messages.push({
          content: data.response,
          isUser: false,
          timestamp: new Date().toISOString(),
          modelUsed: data.modelUsed
        });

        this.conversationLength = this.messages.length;
        this.saveChatHistory();

      } catch (error) {
        console.error('發送訊息失敗:', error);
        this.messages.push({
          content: '抱歉，訊息發送失敗。請稍後再試。',
          isUser: false,
          timestamp: new Date().toISOString(),
          isError: true
        });
      } finally {
        this.isLoading = false;
        this.isTyping = false;
        this.$nextTick(() => this.scrollToBottom());
      }
    },

    // 儲存聊天記錄
    saveChatHistory() {
      const historyKey = `heartsync_chat_${this.currentPersona}`;
      localStorage.setItem(historyKey, JSON.stringify(this.messages));
    },

    // 自動調整輸入框大小
    autoResize(event) {
      const textarea = event.target;
      textarea.style.height = 'auto';
      textarea.style.height = textarea.scrollHeight + 'px';
    },

    // 滾動到底部
    scrollToBottom() {
      if (!this.autoScroll) return;
      this.$nextTick(() => {
        const container = this.$refs.chatMessages;
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },

    // 切換自動滾動
    toggleAutoScroll() {
      this.autoScroll = !this.autoScroll;
      if (this.autoScroll) {
        this.scrollToBottom();
      }
    },

    // 清空對話
    clearChat() {
      if (confirm('確定要清空當前對話嗎？')) {
        this.messages = [];
        this.conversationLength = 0;
        this.saveChatHistory();
      }
    }
  };
};
```

## 🚀 立即修復步驟

1. **檢查第 551 行附近的正則表達式**
   - 確保所有正則表達式都有正確的結束符號 `/`
   - 檢查是否有未轉義的特殊字符

2. **檢查第 1761 行附近的語法**
   - 尋找多餘的逗號、括號或大括號
   - 確保物件和陣列語法正確

3. **確保 Alpine.js 函數定義在全域範圍**
   - 將函數定義放在 `<script>` 標籤的最外層
   - 確保在 Alpine.js 載入之前定義

4. **測試修復**
   ```javascript
   // 在 console 中測試
   console.log(typeof window.enhancedChatApp); // 應該輸出 "function"
   ```

## 🔍 除錯提示

如果還有問題，可以：

1. 使用瀏覽器的開發者工具，點擊錯誤訊息查看確切位置
2. 在出錯的行號前後加入 `console.log` 來定位問題
3. 暫時註解掉可疑的程式碼區塊，逐步排除

需要我幫你檢查特定的程式碼區塊嗎？
