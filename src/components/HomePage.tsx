import { Heart, MessageCircle, Plus, Send, X } from "lucide-react";
// HomePage.tsx - 整合 InstantDB + 發文功能
import React, { useEffect, useState } from "react";
import { db, type Post, seedInitialData, type User } from "../instant";
import PostCreation from "./PostCreation";

export default function HomePage() {
	// 🔥 InstantDB 即時查詢
	const { data, isLoading, error } = db.useQuery({
		posts: {
			author: {},
			comments: {
				author: {},
			},
			likes: {},
		},
		users: {},
	});

	const [currentUser, setCurrentUser] = useState<User | null>(null);
	const [showCreatePost, setShowCreatePost] = useState(false);
	const [showCommentModal, setShowCommentModal] = useState<string | null>(null);
	const [newComment, setNewComment] = useState("");

	// 隱藏貼文功能
	const [hiddenPosts, setHiddenPosts] = useState<Set<string>>(new Set());

	// 隱藏貼文
	const hidePost = (postId: string) => {
		setHiddenPosts((prev) => new Set([...prev, postId]));
	};

	// 初始化資料和用戶
	useEffect(() => {
		const initData = async () => {
			// 先設置用戶，再載入資料
			if (data?.users && data.users.length > 0 && !currentUser) {
				setCurrentUser(data.users[0]);
				console.log("✅ 用戶設置成功:", data.users[0]);
			}

			// 如果沒有貼文，載入範例資料
			if (data?.posts && data.posts.length === 0) {
				console.log("📝 載入範例資料...");
				await seedInitialData();
			}
		};
		initData();
	}, [data?.users, data?.posts]);

	// 調試用 - 顯示當前用戶狀態
	useEffect(() => {
		console.log("🔍 當前用戶狀態:", currentUser);
		console.log("📊 數據狀態:", {
			posts: data?.posts?.length,
			users: data?.users?.length,
		});
	}, [currentUser, data]);

	// 按讚功能
	const handleLike = async (postId: string) => {
		if (!currentUser) return;

		const post = data?.posts?.find((p) => p.id === postId);
		if (!post) return;

		const existingLike = post.likes?.find((l) => l.userId === currentUser.id);

		if (existingLike) {
			// 取消按讚
			await db.transact([
				db.likes.delete(existingLike.id),
				db.posts.update(postId, {
					likesCount: Math.max(0, (post.likesCount || 1) - 1),
				}),
			]);
		} else {
			// 新增按讚
			await db.transact([
				db.likes.create({
					id: `like_${postId}_${currentUser.id}_${Date.now()}`,
					postId,
					userId: currentUser.id,
					createdAt: Date.now(),
				}),
				db.posts.update(postId, {
					likesCount: (post.likesCount || 0) + 1,
				}),
			]);
		}
	};

	// 新增留言
	const handleAddComment = async (postId: string) => {
		if (!currentUser || !newComment.trim()) return;

		const post = data?.posts?.find((p) => p.id === postId);
		if (!post) return;

		const commentId = `comment_${postId}_${Date.now()}`;

		await db.transact([
			db.comments.create({
				id: commentId,
				postId,
				authorId: currentUser.id,
				content: newComment.trim(),
				createdAt: Date.now(),
			}),
			db.posts.update(postId, {
				commentsCount: (post.commentsCount || 0) + 1,
			}),
		]);

		setNewComment("");
		setShowCommentModal(null);
	};

	// 載入狀態
	if (isLoading) {
		return (
			<div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4"></div>
					<p className="text-gray-600">正在連線到 InstantDB...</p>
				</div>
			</div>
		);
	}

	// 錯誤狀態
	if (error) {
		return (
			<div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 flex items-center justify-center">
				<div className="text-center max-w-md mx-auto p-6">
					<div className="text-red-500 text-6xl mb-4">⚠️</div>
					<h2 className="text-xl font-semibold text-gray-900 mb-2">連線錯誤</h2>
					<p className="text-red-600 mb-4">{error.message}</p>
					<p className="text-sm text-gray-600 mb-4">
						請檢查：
						<br />
						1. InstantDB App ID 是否正確
						<br />
						2. 網路連線是否正常
						<br />
						3. InstantDB 服務是否可用
					</p>
					<button
						onClick={() => window.location.reload()}
						className="px-4 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600"
					>
						重新載入
					</button>
				</div>
			</div>
		);
	}

	const posts = data?.posts || [];

	return (
		<div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50">
			{/* 頂部導航 */}
			<header className="bg-white shadow-sm border-b border-pink-100 sticky top-0 z-40">
				<div className="max-w-2xl mx-auto px-4 py-4 flex justify-between items-center">
					<div>
						<h1 className="text-2xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
							HeartSync v2.0
						</h1>
						<p className="text-sm text-gray-600">
							在這裡，用分享遇見真心 ✨ InstantDB
						</p>
					</div>

					{currentUser && (
						<div className="flex items-center space-x-4">
							<span className="text-sm text-gray-700">
								👋 {currentUser.name}
							</span>
							<button
								onClick={() => setShowCreatePost(true)}
								className="px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-full text-sm hover:from-pink-600 hover:to-purple-600 transition-colors flex items-center space-x-2"
							>
								<Plus className="w-4 h-4" />
								<span>發文</span>
							</button>
						</div>
					)}
				</div>
			</header>

			<main className="max-w-2xl mx-auto px-4 py-8">
				{/* 隱藏貼文管理 */}
				{hiddenPosts.size > 0 && (
					<div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
						<div className="flex items-center justify-between mb-2">
							<span className="text-sm text-gray-600">
								已隱藏 {hiddenPosts.size} 篇貼文
							</span>
							<button
								onClick={() => setHiddenPosts(new Set())}
								className="text-sm text-purple-600 hover:text-purple-700 font-medium"
							>
								顯示全部
							</button>
						</div>
						<div className="flex flex-wrap gap-2">
							{Array.from(hiddenPosts).map((postId) => {
								const hiddenPost = data?.posts?.find((p) => p.id === postId);
								const hiddenAuthor = data?.users?.find(
									(u) => u.id === hiddenPost?.authorId,
								);
								return (
									<button
										key={postId}
										onClick={() => {
											setHiddenPosts((prev) => {
												const newSet = new Set(prev);
												newSet.delete(postId);
												return newSet;
											});
										}}
										className="px-3 py-1 bg-white border border-gray-300 rounded-full text-xs text-gray-600 hover:bg-gray-100 transition-colors"
										title="點擊恢復顯示"
									>
										{hiddenAuthor?.name || "未知用戶"}:{" "}
										{hiddenPost?.content?.slice(0, 20) || ""}...
									</button>
								);
							})}
						</div>
					</div>
				)}

				{/* 即時同步指示器 */}
				<div className="mb-6 text-center">
					<div className="inline-flex items-center space-x-4 px-4 py-2 bg-white rounded-full shadow-sm border border-pink-100">
						<span className="text-sm text-gray-600">
							🔥 即時同步：{posts.length} 篇貼文
						</span>
						<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
						<span className="text-xs text-gray-500">InstantDB</span>
					</div>
				</div>

				{/* 歡迎訊息（如果沒有貼文） */}
				{posts.length === 0 && (
					<div className="text-center py-12">
						<div className="text-6xl mb-4">🌟</div>
						<h2 className="text-xl font-semibold text-gray-900 mb-2">
							歡迎來到 HeartSync！
						</h2>
						<p className="text-gray-600 mb-4">
							這裡還沒有貼文，成為第一個分享想法的人吧！
						</p>
						{currentUser && (
							<button
								onClick={() => setShowCreatePost(true)}
								className="px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg font-medium hover:from-pink-600 hover:to-purple-600 transition-colors"
							>
								發表第一篇貼文
							</button>
						)}
					</div>
				)}

				{/* 調試信息 */}
				{process.env.NODE_ENV === "development" && (
					<div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
						<div>
							<strong>調試信息:</strong>
						</div>
						<div>
							當前用戶: {currentUser ? `✅ ${currentUser.name}` : "❌ 未設置"}
						</div>
						<div>貼文數量: {posts.length}</div>
						<div>用戶數量: {data?.users?.length || 0}</div>
					</div>
				)}

				{/* 貼文列表 */}
				<div className="space-y-6">
					{posts
						.filter((post) => !hiddenPosts.has(post.id)) // 過濾隱藏的貼文
						.sort((a, b) => b.createdAt - a.createdAt)
						.map((post) => {
							const author = data?.users?.find((u) => u.id === post.authorId);
							const postComments = post.comments || [];
							const isLiked = post.likes?.some(
								(like) => like.userId === currentUser?.id,
							);

							return (
								<article
									key={post.id}
									className="bg-white rounded-xl p-6 shadow-sm border border-pink-100 hover:shadow-md transition-shadow"
								>
									{/* 作者資訊與操作按鈕 */}
									<div className="flex items-center justify-between mb-4">
										<div className="flex items-center">
											<div
												className={`w-12 h-12 ${author?.avatarColor || "bg-gray-200"} rounded-full flex items-center justify-center`}
											>
												<span className="text-sm font-medium text-gray-700">
													{author?.name?.slice(0, 2) || "??"}
												</span>
											</div>
											<div className="ml-3">
												<h3 className="font-medium text-gray-900">
													{author?.name || "未知用戶"}
												</h3>
												<p className="text-sm text-gray-500">
													{new Date(post.createdAt).toLocaleString("zh-TW", {
														year: "numeric",
														month: "short",
														day: "numeric",
														hour: "2-digit",
														minute: "2-digit",
													})}
												</p>
											</div>
										</div>

										<div className="flex items-center space-x-2">
											{/* 隱藏貼文按鈕 */}
											<button
												onClick={() => hidePost(post.id)}
												className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
												title="隱藏此貼文"
											>
												<X className="w-4 h-4" />
											</button>

											{/* 即時同步標示 */}
											<div className="flex items-center space-x-1">
												<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
												<span className="text-xs text-gray-400">即時</span>
											</div>
										</div>
									</div>

									{/* 貼文內容 */}
									<p className="text-gray-800 mb-4 leading-relaxed whitespace-pre-wrap">
										{post.content}
									</p>

									{/* 標籤 */}
									{post.tags && post.tags.length > 0 && (
										<div className="flex flex-wrap gap-2 mb-4">
											{post.tags.map((tag, index) => (
												<span
													key={`${tag}-${index}`}
													className="px-3 py-1 bg-pink-50 text-pink-600 text-sm rounded-full hover:bg-pink-100 transition-colors cursor-pointer"
												>
													#{tag}
												</span>
											))}
										</div>
									)}

									{/* 互動按鈕 */}
									<div className="flex items-center justify-between pt-4 border-t border-gray-100">
										<div className="flex items-center space-x-6">
											<button
												onClick={() => handleLike(post.id)}
												className={`flex items-center space-x-2 transition-colors ${
													isLiked
														? "text-pink-600"
														: "text-gray-600 hover:text-pink-600"
												}`}
											>
												<Heart
													className={`w-5 h-5 ${isLiked ? "fill-current" : ""}`}
												/>
												<span className="font-medium">
													{post.likesCount || 0}
												</span>
												<span className="text-sm">喜歡</span>
											</button>

											<button
												onClick={() => setShowCommentModal(post.id)}
												className="flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-colors"
											>
												<MessageCircle className="w-5 h-5" />
												<span className="font-medium">
													{post.commentsCount || 0}
												</span>
												<span className="text-sm">留言</span>
											</button>
										</div>

										<span className="text-xs text-gray-400 flex items-center space-x-1">
											<span>InstantDB</span>
											<div className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></div>
										</span>
									</div>

									{/* 留言預覽 */}
									{postComments.length > 0 && (
										<div className="mt-4 space-y-2">
											<div className="text-sm text-gray-600 font-medium">
												留言 ({postComments.length})
											</div>
											{postComments.slice(0, 2).map((comment) => {
												const commentAuthor = data?.users?.find(
													(u) => u.id === comment.authorId,
												);
												return (
													<div
														key={comment.id}
														className="bg-gray-50 rounded-lg p-3"
													>
														<div className="flex items-start space-x-2">
															<div
																className={`w-6 h-6 ${commentAuthor?.avatarColor || "bg-gray-200"} rounded-full flex items-center justify-center flex-shrink-0`}
															>
																<span className="text-xs font-medium text-gray-700">
																	{commentAuthor?.name?.slice(0, 1) || "?"}
																</span>
															</div>
															<div className="flex-1">
																<div className="flex items-center space-x-2">
																	<span className="text-sm font-medium text-gray-900">
																		{commentAuthor?.name || "未知用戶"}
																	</span>
																	<span className="text-xs text-gray-500">
																		{new Date(comment.createdAt).toLocaleString(
																			"zh-TW",
																		)}
																	</span>
																</div>
																<p className="text-sm text-gray-700 mt-1">
																	{comment.content}
																</p>
															</div>
														</div>
													</div>
												);
											})}
											{postComments.length > 2 && (
												<button
													onClick={() => setShowCommentModal(post.id)}
													className="text-sm text-purple-600 hover:text-purple-700 font-medium"
												>
													查看全部 {postComments.length} 則留言 →
												</button>
											)}
										</div>
									)}
								</article>
							);
						})}
				</div>
			</main>

			{/* 浮動發文按鈕 - 始終顯示 */}
			<div className="fixed bottom-6 right-6 z-50">
				<button
					onClick={() => {
						console.log("🎯 發文按鈕被點擊，當前用戶:", currentUser);
						if (!currentUser) {
							console.log("⚠️ 用戶未設置，嘗試自動設置...");
							if (data?.users && data.users.length > 0) {
								setCurrentUser(data.users[0]);
							}
						}
						setShowCreatePost(true);
					}}
					className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center hover:scale-105 group"
					title="發表新貼文"
				>
					<Plus className="w-7 h-7 group-hover:rotate-90 transition-transform duration-200" />
				</button>

				{/* 發文提示（如果用戶已設置） */}
				{currentUser && (
					<div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-3 py-1 rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
						發表新貼文
					</div>
				)}
			</div>

			{/* 發文彈窗 - 確保正確傳遞 currentUser */}
			{showCreatePost && currentUser && (
				<PostCreation
					currentUser={{
						id: currentUser.id,
						name: currentUser.name,
						email: currentUser.email,
					}}
					onClose={() => setShowCreatePost(false)}
				/>
			)}

			{/* 留言彈窗 */}
			{showCommentModal && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-2xl p-6 max-w-md w-full max-h-[80vh] overflow-y-auto">
						<div className="flex justify-between items-center mb-4">
							<h3 className="text-lg font-semibold text-gray-900">留言</h3>
							<button
								onClick={() => setShowCommentModal(null)}
								className="text-gray-400 hover:text-gray-600"
							>
								<X className="w-6 h-6" />
							</button>
						</div>

						<div className="space-y-3 mb-4">
							<div className="flex space-x-3">
								<textarea
									value={newComment}
									onChange={(e) => setNewComment(e.target.value)}
									placeholder="寫下你的想法..."
									className="flex-1 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
									rows={3}
								/>
							</div>
							<button
								onClick={() => handleAddComment(showCommentModal)}
								disabled={!newComment.trim()}
								className="w-full py-2 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:from-pink-600 hover:to-purple-600"
							>
								<Send className="w-4 h-4 inline mr-2" />
								發送留言
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
