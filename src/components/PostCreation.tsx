import { Hash, Image, Send, Smile, X } from "lucide-react";
// PostCreation.tsx - 革命性簡單的發文功能
import type React from "react";
import { useState } from "react";
import { db } from "../instant";

interface PostCreationProps {
	currentUser: {
		id: string;
		name: string;
		email: string;
	};
	onClose: () => void;
}

export default function PostCreation({
	currentUser,
	onClose,
}: PostCreationProps) {
	const [content, setContent] = useState("");
	const [tags, setTags] = useState<string[]>([]);
	const [tagInput, setTagInput] = useState("");
	const [isPosting, setIsPosting] = useState(false);
	const [charCount, setCharCount] = useState(0);

	const MAX_CHARS = 500;
	const MAX_TAGS = 5;

	// 🔥 革命性特色：直接操作資料庫，無需 API！
	const handleCreatePost = async () => {
		if (!content.trim() || isPosting) return;

		setIsPosting(true);

		try {
			const postId = `post_${Date.now()}_${currentUser.id}`;

			// 一個操作完成所有資料寫入，自動同步到所有客戶端
			await db.transact([
				db.posts.create({
					id: postId,
					content: content.trim(),
					authorId: currentUser.id,
					tags: tags,
					likesCount: 0,
					commentsCount: 0,
					createdAt: Date.now(),
				}),
			]);

			// 🎉 成功！自動清理表單
			setContent("");
			setTags([]);
			setTagInput("");
			setCharCount(0);
			onClose();

			// 可選：顯示成功提示
			// toast.success('貼文發佈成功！')
		} catch (error) {
			console.error("發文失敗:", error);
			// 處理錯誤
			alert("發文失敗，請稍後再試");
		} finally {
			setIsPosting(false);
		}
	};

	const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		const newContent = e.target.value;
		if (newContent.length <= MAX_CHARS) {
			setContent(newContent);
			setCharCount(newContent.length);
		}
	};

	const handleAddTag = () => {
		const tag = tagInput.trim();
		if (tag && !tags.includes(tag) && tags.length < MAX_TAGS) {
			setTags([...tags, tag]);
			setTagInput("");
		}
	};

	const handleRemoveTag = (tagToRemove: string) => {
		setTags(tags.filter((tag) => tag !== tagToRemove));
	};

	const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" || e.key === ",") {
			e.preventDefault();
			handleAddTag();
		}
	};

	const isPostDisabled = !content.trim() || isPosting || charCount > MAX_CHARS;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
			<div className="bg-white rounded-2xl w-full max-w-lg shadow-xl">
				{/* 標題列 */}
				<div className="flex items-center justify-between p-4 border-b border-gray-100">
					<h2 className="text-lg font-semibold text-gray-900">發表新貼文</h2>
					<button
						onClick={onClose}
						className="text-gray-400 hover:text-gray-600 p-1"
					>
						<X className="w-5 h-5" />
					</button>
				</div>

				{/* 內容區 */}
				<div className="p-4 space-y-4">
					{/* 用戶資訊 */}
					<div className="flex items-center space-x-3">
						<div className="w-10 h-10 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full flex items-center justify-center">
							<span className="text-white font-medium">
								{currentUser.name.slice(0, 2)}
							</span>
						</div>
						<div>
							<div className="font-medium text-gray-900">
								{currentUser.name}
							</div>
							<div className="text-sm text-gray-500">正在分享想法...</div>
						</div>
					</div>

					{/* 內容輸入 */}
					<div className="space-y-2">
						<textarea
							value={content}
							onChange={handleContentChange}
							placeholder="分享你的想法、經歷或心情..."
							className="w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500"
							rows={6}
						/>

						{/* 字數統計 */}
						<div className="flex justify-between items-center text-sm">
							<span className="text-gray-500">
								還可以輸入 {MAX_CHARS - charCount} 個字符
							</span>
							<span
								className={`font-medium ${charCount > MAX_CHARS * 0.9 ? "text-orange-500" : "text-gray-400"}`}
							>
								{charCount}/{MAX_CHARS}
							</span>
						</div>
					</div>

					{/* 標籤區域 */}
					<div className="space-y-3">
						<div className="flex items-center space-x-2">
							<Hash className="w-4 h-4 text-gray-400" />
							<span className="text-sm font-medium text-gray-700">標籤</span>
							<span className="text-xs text-gray-500">
								({tags.length}/{MAX_TAGS})
							</span>
						</div>

						{/* 已添加的標籤 */}
						{tags.length > 0 && (
							<div className="flex flex-wrap gap-2">
								{tags.map((tag) => (
									<span
										key={tag}
										className="inline-flex items-center px-3 py-1 bg-pink-50 text-pink-700 text-sm rounded-full"
									>
										#{tag}
										<button
											onClick={() => handleRemoveTag(tag)}
											className="ml-1 text-pink-500 hover:text-pink-700"
										>
											<X className="w-3 h-3" />
										</button>
									</span>
								))}
							</div>
						)}

						{/* 標籤輸入 */}
						{tags.length < MAX_TAGS && (
							<div className="flex space-x-2">
								<input
									type="text"
									value={tagInput}
									onChange={(e) => setTagInput(e.target.value)}
									onKeyPress={handleTagInputKeyPress}
									placeholder="添加標籤"
									className="flex-1 px-3 py-2 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-pink-500 focus:border-transparent"
									maxLength={20}
								/>
								<button
									onClick={handleAddTag}
									disabled={!tagInput.trim()}
									className="px-3 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									添加
								</button>
							</div>
						)}
					</div>

					{/* 功能按鈕（未來擴展） */}
					<div className="flex items-center space-x-4 py-2 border-t border-gray-100">
						<button className="flex items-center space-x-2 text-gray-500 hover:text-gray-700 text-sm">
							<Image className="w-4 h-4" />
							<span>圖片</span>
							<span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded">
								即將推出
							</span>
						</button>
						<button className="flex items-center space-x-2 text-gray-500 hover:text-gray-700 text-sm">
							<Smile className="w-4 h-4" />
							<span>表情</span>
							<span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded">
								即將推出
							</span>
						</button>
					</div>
				</div>

				{/* 操作按鈕 */}
				<div className="flex justify-between items-center p-4 bg-gray-50 rounded-b-2xl">
					<div className="text-sm text-gray-500">
						{content.trim() ? (
							<span className="text-green-600">✓ 準備發布</span>
						) : (
							<span>請輸入內容</span>
						)}
					</div>

					<div className="flex space-x-3">
						<button
							onClick={onClose}
							className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
						>
							取消
						</button>
						<button
							onClick={handleCreatePost}
							disabled={isPostDisabled}
							className="px-6 py-2 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg font-medium hover:from-pink-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2"
						>
							{isPosting ? (
								<>
									<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
									<span>發布中...</span>
								</>
							) : (
								<>
									<Send className="w-4 h-4" />
									<span>發布貼文</span>
								</>
							)}
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}

// 使用範例
export function PostCreationExample() {
	const [showCreatePost, setShowCreatePost] = useState(false);
	const currentUser = {
		id: "user1",
		name: "咖啡愛好者小雅",
		email: "<EMAIL>",
	};

	return (
		<div>
			{/* 觸發按鈕 */}
			<button
				onClick={() => setShowCreatePost(true)}
				className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-full shadow-lg hover:shadow-xl transition-shadow duration-200 flex items-center justify-center"
			>
				<Send className="w-6 h-6" />
			</button>

			{/* 發文彈窗 */}
			{showCreatePost && (
				<PostCreation
					currentUser={currentUser}
					onClose={() => setShowCreatePost(false)}
				/>
			)}
		</div>
	);
}
