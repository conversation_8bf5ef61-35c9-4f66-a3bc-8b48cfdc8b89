# HeartSync 首頁 InstantDB 整合任務

## 🎯 **任務概述**

將 HeartSync v2.1 的主頁面升級為支援本地和雲端混合模式的 v2.1+ 版本，讓用戶可以選擇使用本地儲存或 InstantDB 即時同步功能。

## 📋 **當前狀態**

### **已完成**
- ✅ InstantDB 測試頁面正常運作 (`/v3` 路由)
- ✅ InstantDB 連接和即時同步功能驗證成功
- ✅ APP_ID 設置完成：`1c81583f-e8de-4448-982e-1c616a2b3432`
- ✅ 權限規則設置完成
- ✅ 原有 v2.1 功能完整正常

### **目標狀態**
- 🎯 主頁面 (`/`) 支援儲存模式選擇
- 🎯 用戶可以在本地模式和雲端模式間切換
- 🎯 保留所有現有功能的完整性
- 🎯 新增即時同步狀態指示

## 🔧 **技術要求**

### **核心功能需求**
1. **儲存模式選擇器**
   - 首次訪問時顯示模式選擇橫幅
   - 支援"本地模式"和"雲端模式"選擇
   - 用戶選擇後記住偏好設定

2. **InstantDB 動態載入**
   - 只有在選擇雲端模式時才載入 InstantDB SDK
   - 使用動態 import：`import('https://unpkg.com/@instantdb/core@0.10.23/dist/index.js')`
   - 載入失敗時自動回退到本地模式

3. **混合資料管理**
   - 本地模式：使用現有的 localStorage 邏輯
   - 雲端模式：資料同步到 InstantDB
   - 模式切換時保持資料一致性

4. **即時同步狀態顯示**
   - 顯示當前儲存模式指示器
   - 雲端模式下顯示連接狀態和在線用戶數
   - 同步狀態的視覺回饋

### **UI/UX 要求**
1. **模式選擇橫幅**
   - 位於 header 下方，首次訪問時顯示
   - 兩個選項：本地模式 (💾) 和雲端模式 (☁️)
   - 包含簡潔的說明文字

2. **儲存模式指示器**
   - 固定在右上角
   - 顯示當前模式和狀態
   - 雲端模式下有同步動畫效果

3. **模式切換按鈕**
   - 在 header 中添加模式切換圖示
   - 點擊可重新顯示模式選擇器

## 📁 **檔案結構**

```
src/
├── index.js (主要修改目標)
├── routes/
│   └── instantdb.js (已完成，參考用)
└── instant.ts (配置檔案，已完成)
```

## 🔧 **實作細節**

### **InstantDB 初始化配置**
```javascript
// 使用的 APP_ID
const INSTANT_APP_ID = '1c81583f-e8de-4448-982e-1c616a2b3432';

// 初始化代碼
this.instantDB = InstantDB.init({
    appId: INSTANT_APP_ID
});

// 資料訂閱
this.instantDB.subscribeQuery({
    posts: {},
    comments: {},
    users: {}
}, (result) => {
    // 處理即時資料更新
});
```

### **儲存模式管理**
```javascript
// localStorage 鍵值
const STORAGE_MODE_KEY = 'heartsync_storage_mode';
const MODE_SELECTED_KEY = 'heartsync_mode_selected';

// 支援的模式
const STORAGE_MODES = {
    local: '本地模式',
    cloud: '雲端模式'
};
```

### **錯誤處理**
- InstantDB 連接失敗時自動回退到本地模式
- 顯示適當的錯誤訊息和狀態
- 網路斷線時的優雅降級

## ⚠️ **重要注意事項**

### **保持向後相容**
- 所有現有的 v2.1 功能必須完全保留
- 現有的用戶資料和狀態不能丟失
- UI/UX 體驗不能降級

### **漸進式增強**
- 本地模式作為基準，雲端模式作為增強
- 用戶可以隨時在兩種模式間切換
- 不強制用戶使用雲端功能

### **效能考量**
- InstantDB SDK 只在需要時載入
- 避免不必要的網路請求
- 保持頁面載入速度

## 🧪 **測試要求**

### **功能測試**
1. **模式選擇**
   - [ ] 首次訪問顯示模式選擇器
   - [ ] 本地模式正常運作
   - [ ] 雲端模式成功連接 InstantDB
   - [ ] 模式切換功能正常

2. **資料同步**
   - [ ] 雲端模式下新建貼文即時同步
   - [ ] 多瀏覽器分頁同步測試
   - [ ] 網路斷線重連測試

3. **錯誤處理**
   - [ ] InstantDB 連接失敗回退測試
   - [ ] 網路異常處理
   - [ ] 無效 APP_ID 處理

### **UI 測試**
- [ ] 模式指示器顯示正確
- [ ] 同步狀態動畫正常
- [ ] 響應式設計在各螢幕尺寸正常
- [ ] 所有現有 UI 元素保持正常

## 📦 **交付內容**

### **檔案修改**
1. **src/index.js** - 主要更新檔案
   - 添加儲存模式選擇功能
   - 整合 InstantDB 動態載入
   - 保留所有現有功能

### **新增功能**
- 儲存模式選擇器 UI
- InstantDB 整合邏輯
- 同步狀態指示器
- 模式切換功能

### **測試驗證**
- 功能測試清單完成
- 多瀏覽器相容性驗證
- 效能測試結果

## 🎯 **成功標準**

1. **功能完整性** - 所有 v2.1 功能正常運作
2. **模式選擇** - 用戶可以選擇和切換儲存模式
3. **即時同步** - 雲端模式下多用戶即時協作正常
4. **用戶體驗** - 直觀易用的介面和流暢的切換體驗
5. **穩定性** - 錯誤處理完善，回退機制可靠

## 🚀 **下一步規劃**

完成此任務後，將為以下功能奠定基礎：
- 用戶註冊和認證系統
- 更豐富的即時協作功能
- 個人資料和隱私設定
- Datastar 技術棧評估

---

**預估完成時間：2-3 小時**
**優先級：高**
**複雜度：中等**
