# HeartSync 增強用戶系統設計

## 🎯 **用戶系統架構**

### **用戶類型層級**
```yaml
1. 訪客用戶 (Anonymous)
   - 瀏覽功能
   - 基本互動 (按讚、留言)
   - 暫時性身份

2. 試用用戶 (Trial)
   - 完整社交功能
   - 隨機生成身份
   - 可認領升級
   - 本地或雲端儲存

3. 註冊用戶 (Registered)
   - 永久身份
   - 個人資料管理
   - 隱私設置
   - 跨設備同步

4. 認證用戶 (Verified)
   - 藍勾標誌
   - 優先顯示
   - 更多功能權限
```

## 🔧 **用戶資料結構**

### **基礎用戶模型**
```javascript
const UserSchema = {
  // 基本資訊
  id: 'string',           // 唯一識別碼
  email: 'string?',       // 電子信箱 (註冊用戶)
  username: 'string?',    // 用戶名 (唯一)
  displayName: 'string',  // 顯示名稱

  // 身份狀態
  userType: 'anonymous|trial|registered|verified',
  isGuest: 'boolean',
  isTrial: 'boolean',
  isVerified: 'boolean',

  // 外觀設定
  avatarClass: 'string',  // CSS 類別
  avatarUrl: 'string?',   // 自訂頭像
  themePreference: 'light|cloudy|dark',

  // 個人資訊
  bio: 'string?',
  location: 'string?',
  website: 'string?',
  interests: 'array',

  // 社交資料
  affection: 'number',        // 好感度
  interactions: 'number',     // 互動次數
  commonInterests: 'number',  // 共同興趣
  followers: 'number',
  following: 'number',

  // 系統資料
  createdAt: 'number',
  lastActive: 'number',
  storageMode: 'local|cloud|hybrid',

  // 權限設定
  privacy: {
    profileVisible: 'boolean',
    postsVisible: 'public|friends|private',
    allowMessages: 'boolean'
  },

  // 試用用戶特有
  guestSessionId: 'string?',
  trialStartDate: 'number?',
  canClaim: 'boolean'
}
```

## 🚀 **用戶流程設計**

### **1. 首次訪問流程**
```
訪客進入 → 顯示模式選擇 → 選擇儲存模式 → 生成試用身份 → 開始使用
```

### **2. 試用用戶升級流程**
```
試用用戶 → 點擊升級 → 輸入基本資訊 → 驗證 → 成為註冊用戶 → 認領歷史資料
```

### **3. 跨設備同步流程**
```
用戶登入新設備 → 驗證身份 → 選擇同步模式 → 合併本地數據 → 完成同步
```

## 💡 **用戶體驗功能**

### **智能身份建議**
```javascript
const generateSmartIdentity = (userInterests, timeOfDay, location) => {
  // 基於興趣、時間、地點生成個性化身份建議
  const identityTemplate = {
    morning: ['晨跑愛好者', '咖啡達人', '早起鳥兒'],
    afternoon: ['陽光探索者', '午後讀者', '創意工作者'],
    evening: ['夜貓思考家', '電影愛好者', '音樂發燒友']
  }

  return identityTemplate[getTimeCategory(timeOfDay)]
    .filter(id => matchesInterests(id, userInterests))
    .sort(() => Math.random() - 0.5)[0]
}
```

### **好感度系統增強**
```javascript
const calculateAffinity = (user1, user2, interactions) => {
  let affinity = 0

  // 基礎互動加分
  affinity += interactions.likes * 2
  affinity += interactions.comments * 5
  affinity += interactions.shares * 3

  // 共同興趣加分
  const commonInterests = findCommonInterests(user1.interests, user2.interests)
  affinity += commonInterests.length * 10

  // 互動頻率加分
  const recentInteractions = getRecentInteractions(user1.id, user2.id, 7) // 7天內
  affinity += recentInteractions * 1.5

  // 時間衰減
  const daysSinceFirstInteraction = getDaysSince(interactions.firstInteraction)
  const timeBonus = Math.max(0, 100 - daysSinceFirstInteraction * 2)

  return Math.min(100, affinity + timeBonus)
}
```

## 🎨 **用戶介面功能**

### **個人檔案頁面**
```html
<!-- 用戶個人檔案模態框 -->
<div class="user-profile-modal">
  <!-- 頭像區域 -->
  <div class="avatar-section">
    <div class="avatar-large" :class="user.avatarClass">
      <span x-text="getAvatarText(user)"></span>
    </div>
    <button x-show="isOwnProfile" @click="changeAvatar()">更換頭像</button>
  </div>

  <!-- 基本資訊 -->
  <div class="basic-info">
    <h2 x-text="user.displayName"></h2>
    <p class="username" x-text="'@' + user.username"></p>
    <div class="user-badges">
      <span x-show="user.isVerified" class="verified-badge">✓ 已認證</span>
      <span x-show="user.isTrial" class="trial-badge">🎭 試用中</span>
    </div>
  </div>

  <!-- 個人簡介 -->
  <div class="bio-section">
    <p x-text="user.bio"></p>
    <div class="interests">
      <template x-for="interest in user.interests">
        <span class="interest-tag" x-text="interest"></span>
      </template>
    </div>
  </div>

  <!-- 統計資訊 -->
  <div class="stats-grid">
    <div class="stat">
      <span class="stat-number" x-text="user.postsCount || 0"></span>
      <span class="stat-label">貼文</span>
    </div>
    <div class="stat">
      <span class="stat-number" x-text="user.followers || 0"></span>
      <span class="stat-label">粉絲</span>
    </div>
    <div class="stat">
      <span class="stat-number" x-text="user.following || 0"></span>
      <span class="stat-label">追蹤</span>
    </div>
    <div class="stat">
      <span class="stat-number" x-text="user.affection || 0"></span>
      <span class="stat-label">好感度</span>
    </div>
  </div>

  <!-- 操作按鈕 -->
  <div class="action-buttons">
    <button x-show="!isOwnProfile" @click="sendHeartSync(user)"
            class="btn-primary">💕 發送 HeartSync</button>
    <button x-show="!isOwnProfile" @click="toggleFollow(user)"
            class="btn-secondary" x-text="isFollowing(user) ? '取消追蹤' : '追蹤'"></button>
    <button x-show="isOwnProfile" @click="editProfile()"
            class="btn-secondary">編輯個人資料</button>
  </div>
</div>
```

### **用戶設定頁面功能**
```javascript
const userSettingsFeatures = {
  // 個人資料設定
  profileSettings: {
    displayName: { maxLength: 50, required: true },
    username: { maxLength: 30, unique: true, pattern: /^[a-zA-Z0-9_]+$/ },
    bio: { maxLength: 160 },
    location: { maxLength: 50 },
    website: { validation: 'url' },
    interests: { maxItems: 10 }
  },

  // 隱私設定
  privacySettings: {
    profileVisibility: ['public', 'friends', 'private'],
    postVisibility: ['public', 'friends', 'private'],
    allowMessages: 'boolean',
    showOnlineStatus: 'boolean',
    allowTagging: 'boolean'
  },

  // 通知設定
  notificationSettings: {
    likes: 'boolean',
    comments: 'boolean',
    follows: 'boolean',
    mentions: 'boolean',
    heartSyncs: 'boolean'
  },

  // 儲存偏好
  storagePreferences: {
    defaultMode: ['local', 'cloud', 'hybrid'],
    autoSync: 'boolean',
    offlineMode: 'boolean',
    dataSaver: 'boolean'
  }
}
```

## 🔐 **身份驗證系統**

### **試用用戶升級流程**
```javascript
const upgradeTrialUser = async (trialUser, registrationData) => {
  // 1. 驗證註冊資料
  const validation = validateRegistrationData(registrationData)
  if (!validation.valid) throw new Error(validation.message)

  // 2. 檢查用戶名唯一性
  const usernameExists = await checkUsernameExists(registrationData.username)
  if (usernameExists) throw new Error('用戶名已存在')

  // 3. 升級用戶身份
  const upgradedUser = {
    ...trialUser,
    userType: 'registered',
    isTrial: false,
    isGuest: false,
    email: registrationData.email,
    username: registrationData.username,
    upgradeDate: Date.now(),
    claimedData: {
      posts: getUserPosts(trialUser.id),
      comments: getUserComments(trialUser.id),
      interactions: getUserInteractions(trialUser.id)
    }
  }

  // 4. 保存到雲端 (如果使用雲端模式)
  if (storageMode === 'cloud') {
    await saveUserToCloud(upgradedUser)
  }

  // 5. 更新本地儲存
  localStorage.setItem('heartsync_user', JSON.stringify(upgradedUser))

  return upgradedUser
}
```

### **跨設備同步機制**
```javascript
const syncUserAcrossDevices = async (userCredentials) => {
  try {
    // 1. 從雲端獲取用戶資料
    const cloudUser = await fetchUserFromCloud(userCredentials)

    // 2. 獲取本地資料
    const localData = getLocalUserData()

    // 3. 智能合併資料
    const mergedData = mergeUserData(cloudUser, localData, {
      strategy: 'latest-wins', // 最新資料優先
      conflictResolution: 'manual', // 衝突時手動解決
      preserveLocal: ['preferences', 'theme'] // 保留本地偏好
    })

    // 4. 顯示合併結果給用戶確認
    if (mergedData.conflicts.length > 0) {
      const userChoice = await showConflictResolutionModal(mergedData.conflicts)
      mergedData = applyUserChoices(mergedData, userChoice)
    }

    // 5. 保存最終結果
    await Promise.all([
      saveUserToCloud(mergedData.final),
      saveUserToLocal(mergedData.final)
    ])

    return mergedData.final
  } catch (error) {
    console.error('跨設備同步失敗:', error)
    throw new Error('同步失敗，請稍後重試')
  }
}
```

## 📊 **用戶分析和洞察**

### **用戶行為追蹤**
```javascript
const trackUserBehavior = {
  engagement: {
    postsCreated: 'number',
    commentsPosted: 'number',
    likesGiven: 'number',
    profileViews: 'number',
    sessionDuration: 'number'
  },

  social: {
    friendsMade: 'number',
    heartSyncsSent: 'number',
    heartSyncsReceived: 'number',
    conversationStarters: 'number'
  },

  preferences: {
    activeHours: 'array',
    favoriteTopics: 'array',
    interactionStyle: 'analytical|emotional|casual',
    contentPreference: 'text|visual|mixed'
  }
}
```

### **個性化推薦系統**
```javascript
const generatePersonalizedSuggestions = (user, allUsers, posts) => {
  const suggestions = {
    // 推薦用戶
    suggestedUsers: findSimilarUsers(user, allUsers, {
      factors: ['interests', 'activityTime', 'interactionStyle'],
      limit: 5
    }),

    // 推薦內容
    suggestedPosts: recommendPosts(user, posts, {
      based: ['interests', 'pastLikes', 'friendsActivity'],
      excludeViewed: true,
      limit: 10
    }),

    // 推薦話題
    suggestedTopics: suggestTopics(user, {
      trending: true,
      personalInterests: true,
      communityActive: true
    })
  }

  return suggestions
}
```

## 🎯 **立即實作優先級**

### **Phase 1: 基礎增強 (今天晚上)**
```yaml
高優先級:
- ✅ 模式選擇器整合到首頁
- ✅ InstantDB 條件載入
- ✅ 用戶狀態顯示增強
- 🔧 試用用戶升級流程

中優先級:
- 🔧 個人資料頁面
- 🔧 基本隱私設定
- 🔧 跨設備提示
```

### **Phase 2: 進階功能 (本週)**
```yaml
- 用戶名系統
- 好感度算法優化
- 推薦系統基礎版
- 通知系統
```

### **Phase 3: 社交增強 (下週)**
```yaml
- 追蹤/粉絲系統
- 私訊功能
- 群組功能
- 進階個性化
```

## 🔥 **創新功能點子**

### **HeartSync 獨特功能**
```javascript
const uniqueFeatures = {
  // 情感智能匹配
  emotionalMatching: {
    analyzePostSentiment: true,
    matchEmotionalNeeds: true,
    suggestComfortWords: true
  },

  // 興趣演化追蹤
  interestEvolution: {
    trackChangingInterests: true,
    suggestNewTopics: true,
    celebrateMilestones: true
  },

  // 社交溫度計
  socialThermometer: {
    measureCommunityWarmth: true,
    showKindnessLevel: true,
    encouragePositivity: true
  }
}
```

這個增強的用戶系統將讓 HeartSync 從簡單的社交平台進化為真正智能的人際連接工具！ 🚀
