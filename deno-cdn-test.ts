#!/usr/bin/env -S deno run --allow-net --allow-read --allow-env --allow-write

/**
 * 🧪 HeartSync CDN 測試服務器
 * 專門用於測試各種 CDN 和框架的載入狀況
 */

import { Hono } from 'jsr:@hono/hono@^4.9.0';

const app = new Hono();

// 🏠 根路由
app.get('/', (c: any) => {
	return c.json({
		message: '🧪 HeartSync CDN 測試服務器',
		status: 'running',
		timestamp: new Date().toISOString(),
		tests: {
			datastar: '/test/datastar',
			tailwind: '/test/tailwind',
			preline: '/test/preline',
			all: '/test/all'
		}
	});
});

// 🧪 Datastar CDN 測試頁面
app.get('/test/datastar', (c: any) => {
	return c.html(`<!DOCTYPE html>
<html lang="zh-TW" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Datastar CDN 測試</title>
    
    <!-- 基礎樣式 -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success { background: #10b981; color: white; }
        .status.error { background: #ef4444; color: white; }
        .status.loading { background: #f59e0b; color: white; }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .counter {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
        }
    </style>
</head>
<body>

    <div class="test-container">
        <h1>🧪 Datastar CDN 測試實驗室</h1>
        <p>這個頁面專門用於測試 Datastar 的各種 CDN 源和配置</p>

        <!-- 測試 1: 多個 CDN 源測試 -->
        <div class="test-section">
            <h2>📡 CDN 源測試</h2>
            <div id="cdn-status">
                <div>CDN 1 (jsdelivr @main): <span id="cdn1-status" class="status loading">測試中...</span></div>
                <div>CDN 2 (jsdelivr @v1.0.0-beta.11): <span id="cdn2-status" class="status loading">測試中...</span></div>
                <div>CDN 3 (unpkg): <span id="cdn3-status" class="status loading">測試中...</span></div>
                <div>CDN 4 (skypack): <span id="cdn4-status" class="status loading">測試中...</span></div>
            </div>
            
            <button onclick="testAllCDNs()">🔄 重新測試所有 CDN</button>
        </div>

        <!-- 測試 2: Datastar 功能測試 -->
        <div class="test-section" data-signals="{counter: 0, message: 'Hello Datastar!'}">
            <h2>⚡ Datastar 功能測試</h2>
            
            <div>
                <p>計數器: <span class="counter" data-text="$counter">0</span></p>
                <button data-on-click="$counter++">➕ 增加</button>
                <button data-on-click="$counter--">➖ 減少</button>
                <button data-on-click="$counter = 0">🔄 重置</button>
            </div>
            
            <div style="margin-top: 20px;">
                <p>訊息: <span data-text="$message">Hello Datastar!</span></p>
                <input data-bind-message type="text" placeholder="輸入訊息..." 
                       style="padding: 8px; border-radius: 4px; border: 1px solid #ccc; background: rgba(255,255,255,0.1); color: white;">
            </div>
            
            <div style="margin-top: 20px;">
                <div id="datastar-status">Datastar 狀態: <span id="ds-status" class="status loading">檢查中...</span></div>
            </div>
        </div>

        <!-- 測試 3: 錯誤診斷 -->
        <div class="test-section">
            <h2>🔍 錯誤診斷</h2>
            <button onclick="runDiagnostics()">🔬 執行完整診斷</button>
            <button onclick="clearLog()">🗑️ 清空日誌</button>
            
            <div id="diagnostic-log" class="log">
                === CDN 測試日誌 ===<br>
                等待診斷開始...<br>
            </div>
        </div>

        <!-- 測試 4: 網路狀態 -->
        <div class="test-section">
            <h2>🌐 網路狀態</h2>
            <div id="network-info">
                <div>連線狀態: <span id="online-status" class="status">檢查中...</span></div>
                <div>用戶代理: <span id="user-agent" style="font-size: 12px; color: #ccc;"></span></div>
                <div>時間戳: <span id="timestamp"></span></div>
            </div>
        </div>
    </div>

    <!-- 動態載入 Datastar CDN -->
    <script>
        // 🧪 CDN 測試管理器
        class CDNTester {
            constructor() {
                this.cdnSources = [
                    {
                        name: 'jsdelivr @main',
                        url: 'https://cdn.jsdelivr.net/gh/starfederation/datastar@main/bundles/datastar.js',
                        id: 'cdn1'
                    },
                    {
                        name: 'jsdelivr @v1.0.0-beta.11',
                        url: 'https://cdn.jsdelivr.net/gh/starfederation/datastar@v1.0.0-beta.11/bundles/datastar.js',
                        id: 'cdn2'
                    },
                    {
                        name: 'unpkg',
                        url: 'https://unpkg.com/@starfederation/datastar@1.0.0-beta.11/dist/datastar.js',
                        id: 'cdn3'
                    },
                    {
                        name: 'skypack',
                        url: 'https://cdn.skypack.dev/@starfederation/datastar@1.0.0-beta.11',
                        id: 'cdn4'
                    }
                ];
                
                this.log = this.log.bind(this);
                this.init();
            }
            
            init() {
                this.updateNetworkInfo();
                this.testAllCDNs();
                this.checkDatastarStatus();
                
                // 每5秒檢查一次狀態
                setInterval(() => {
                    this.checkDatastarStatus();
                    this.updateNetworkInfo();
                }, 5000);
            }
            
            log(message) {
                const logElement = document.getElementById('diagnostic-log');
                const timestamp = new Date().toLocaleTimeString();
                logElement.innerHTML += \`[\${timestamp}] \${message}<br>\`;
                logElement.scrollTop = logElement.scrollHeight;
                console.log(\`[CDN Test] \${message}\`);
            }
            
            async testCDN(source) {
                this.log(\`測試 CDN: \${source.name}\`);
                
                try {
                    const response = await fetch(source.url, { method: 'HEAD' });
                    const status = response.ok ? 'success' : 'error';
                    const statusText = response.ok ? \`✅ 可用 (\${response.status})\` : \`❌ 失敗 (\${response.status})\`;
                    
                    document.getElementById(\`\${source.id}-status\`).className = \`status \${status}\`;
                    document.getElementById(\`\${source.id}-status\`).textContent = statusText;
                    
                    this.log(\`CDN \${source.name}: \${statusText}\`);
                    return response.ok;
                } catch (error) {
                    document.getElementById(\`\${source.id}-status\`).className = 'status error';
                    document.getElementById(\`\${source.id}-status\`).textContent = \`❌ 網路錯誤\`;
                    
                    this.log(\`CDN \${source.name}: ❌ 網路錯誤 - \${error.message}\`);
                    return false;
                }
            }
            
            async testAllCDNs() {
                this.log('開始測試所有 CDN 源...');
                
                for (const source of this.cdnSources) {
                    await this.testCDN(source);
                }
                
                this.log('CDN 測試完成');
            }
            
            checkDatastarStatus() {
                const hasDs = !!window.ds;
                const hasDatastar = !!window.datastar;
                const hasDatastarCap = !!window.Datastar;
                
                let status = 'error';
                let statusText = '❌ 未載入';
                
                if (hasDs) {
                    status = 'success';
                    statusText = \`✅ window.ds 可用 (v\${window.ds.version || 'unknown'})\`;
                } else if (hasDatastar) {
                    status = 'success';
                    statusText = '✅ window.datastar 可用';
                } else if (hasDatastarCap) {
                    status = 'success';
                    statusText = '✅ window.Datastar 可用';
                }
                
                document.getElementById('ds-status').className = \`status \${status}\`;
                document.getElementById('ds-status').textContent = statusText;
            }
            
            updateNetworkInfo() {
                document.getElementById('online-status').className = \`status \${navigator.onLine ? 'success' : 'error'}\`;
                document.getElementById('online-status').textContent = navigator.onLine ? '✅ 線上' : '❌ 離線';
                document.getElementById('user-agent').textContent = navigator.userAgent;
                document.getElementById('timestamp').textContent = new Date().toLocaleString();
            }
            
            runDiagnostics() {
                this.log('=== 開始完整診斷 ===');
                this.log(\`瀏覽器: \${navigator.userAgent}\`);
                this.log(\`線上狀態: \${navigator.onLine}\`);
                this.log(\`時間: \${new Date().toISOString()}\`);
                
                // 檢查全域變數
                this.log(\`window.ds: \${!!window.ds}\`);
                this.log(\`window.datastar: \${!!window.datastar}\`);
                this.log(\`window.Datastar: \${!!window.Datastar}\`);
                
                // 檢查 script 標籤
                const scripts = Array.from(document.querySelectorAll('script[src]'));
                const datastartScripts = scripts.filter(s => s.src && s.src.includes('datastar'));
                this.log(\`Datastar scripts 數量: \${datastartScripts.length}\`);
                
                datastartScripts.forEach((script, index) => {
                    this.log(\`Script \${index + 1}: \${script.src}\`);
                });
                
                // 檢查 data-signals 元素
                const signalsElements = document.querySelectorAll('[data-signals]');
                this.log(\`data-signals 元素數量: \${signalsElements.length}\`);
                
                this.log('=== 診斷完成 ===');
            }
            
            clearLog() {
                document.getElementById('diagnostic-log').innerHTML = '=== CDN 測試日誌 ===<br>';
            }
        }
        
        // 全域函數
        window.testAllCDNs = () => window.cdnTester.testAllCDNs();
        window.runDiagnostics = () => window.cdnTester.runDiagnostics();
        window.clearLog = () => window.cdnTester.clearLog();
        
        // 初始化測試器
        document.addEventListener('DOMContentLoaded', () => {
            window.cdnTester = new CDNTester();
        });
    </script>

    <!-- 嘗試載入 Datastar (使用最有希望的 CDN) -->
    <script type="module" src="https://cdn.jsdelivr.net/gh/starfederation/datastar@main/bundles/datastar.js"></script>

</body>
</html>`);
});

// 🚀 啟動服務器
const port = parseInt(Deno.env.get('PORT') || '8004');

console.log(`
🧪 HeartSync CDN 測試服務器啟動中...

📋 服務信息:
   • 運行時: Deno ${Deno.version.deno}
   • 端口: ${port}
   • 用途: CDN 和框架測試

🎯 測試端點:
   • 主頁: http://localhost:${port}/
   • Datastar 測試: http://localhost:${port}/test/datastar

🚀 啟動完成！
`);

Deno.serve(
	{
		port: port,
		onListen: ({ port, hostname }) => {
			console.log(`🌐 CDN 測試服務器運行在 http://${hostname}:${port}`);
			console.log(`🧪 開始 CDN 測試！`);
		},
	},
	app.fetch
);
