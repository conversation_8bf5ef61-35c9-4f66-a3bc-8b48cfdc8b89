# hsn-terminal-chat.ps1
# HSN Terminal Chat Assistant (PowerShell Version)

# HSN API Endpoints
$HSN_PORTS = @(8787, 3000)
$HSN_API = $null

# Color output function
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = 'White'
    )
    Write-Host $Text -ForegroundColor $Color
}

# Function to detect available HSN port
function Find-HSNPort {
    foreach ($port in $HSN_PORTS) {
        $testUrl = "http://localhost:$port/api/hsn/chat"
        try {
            # Test connection with a simple request
            $testResponse = Invoke-WebRequest -Uri "http://localhost:$port" -Method GET -TimeoutSec 5 -ErrorAction Stop
            Write-ColorText "Found HSN service on port $port" 'Green'
            return $testUrl
        }
        catch {
            # Port not available, try next
            continue
        }
    }
    return $null
}

# API call function
function Invoke-HSNAPI {
    param([string]$Message)

    try {
        $body = @{
            message = $Message
            provider = 'openrouter'
            model = 'claude-3-5-sonnet-20241022'
            conversationHistory = @()
            systemPrompt = 'You are HSN (HeartSync Network) AI knowledge exploration partner. Please respond in English with a professional but friendly tone.'
        } | ConvertTo-Json -Depth 10

        $response = Invoke-RestMethod -Uri $HSN_API -Method POST -Body $body -ContentType 'application/json' -TimeoutSec 30
        return $response
    }
    catch {
        throw $_
    }
}

# Main chat loop
function Start-Chat {
    Write-ColorText 'HSN Terminal Assistant Started' 'Green'
    Write-ColorText 'Type "exit" to end conversation' 'Yellow'
    Write-ColorText 'Type "help" to see available commands' 'Cyan'
    Write-ColorText '─────────────────────────────' 'Gray'
    Write-Host ''

    while ($true) {
        Write-Host 'You > ' -ForegroundColor Green -NoNewline
        $input = Read-Host
        $trimmedInput = $input.Trim()

        switch ($trimmedInput.ToLower()) {
            'exit' {
                Write-ColorText 'Goodbye! Looking forward to next knowledge exploration!' 'Yellow'
                return
            }
            'quit' {
                Write-ColorText 'Goodbye! Looking forward to next knowledge exploration!' 'Yellow'
                return
            }
            'bye' {
                Write-ColorText 'Goodbye! Looking forward to next knowledge exploration!' 'Yellow'
                return
            }
            'help' {
                Write-ColorText 'Available commands:' 'Cyan'
                Write-ColorText '  exit/quit/bye - End conversation' 'White'
                Write-ColorText '  help - Show this help' 'White'
                Write-ColorText '  clear - Clear screen' 'White'
                Write-ColorText '  Any question - Start knowledge exploration' 'White'
            }
            'clear' {
                Clear-Host
                Write-ColorText 'HSN Terminal Assistant' 'Green'
                Write-ColorText '─────────────────────────────' 'Gray'
            }
            '' {
                # Empty input, ignore
                continue
            }
            default {
                Write-Host 'Thinking...' -ForegroundColor Yellow -NoNewline

                try {
                    $response = Invoke-HSNAPI -Message $trimmedInput

                    if ($response.success -and $response.response) {
                        Write-Host "`r" -NoNewline
                        Write-Host (' ' * 20) -NoNewline
                        Write-Host "`r" -NoNewline
                        Write-ColorText "AI > $($response.response)" 'Cyan'
                    }
                    else {
                        Write-Host "`r" -NoNewline
                        Write-ColorText 'API response format error' 'Red'
                    }
                }
                catch {
                    Write-Host "`r" -NoNewline
                    Write-ColorText 'Connection error, please check:' 'Red'
                    Write-ColorText '   1. HSN service running on localhost:8787 or localhost:3000' 'White'
                    Write-ColorText '   2. Network connection is normal' 'White'
                    Write-ColorText '   3. API endpoint is correct' 'White'
                    Write-ColorText "   Error details: $($_.Exception.Message)" 'Gray'
                }
            }
        }

        Write-Host ''
    }
}

# Start chat
try {
    # Detect available HSN port
    Write-ColorText 'Detecting HSN service...' 'Yellow'
    $HSN_API = Find-HSNPort

    if ($HSN_API) {
        Start-Chat
    }
    else {
        Write-ColorText 'No HSN service found on ports 8787 or 3000' 'Red'
        Write-ColorText 'Please start the HSN service and try again.' 'White'
    }
}
catch {
    Write-ColorText "Script execution error: $($_.Exception.Message)" 'Red'
}
