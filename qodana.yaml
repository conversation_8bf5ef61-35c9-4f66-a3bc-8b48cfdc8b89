# Qodana Configuration for HeartSync v2
# 優化配置以提升掃描速度

version: '1.0'
linter: 'jetbrains/qodana-js:2025.1'

# 使用快速分析配置
profile:
  name: qodana.starter  # 使用更輕量的配置

# 簡化排除規則以提升掃描速度
exclude:
  - name: "non-source-files"
    paths:
      - "node_modules/**"
      - "dist/**"
      - "build/**"
      - ".wrangler/**"
      - "coverage/**"
      - ".qodana/**"
      - "playwright-report/**"
      - "scripts/**"
      - "static/**"
      - "streamline-landing-page/**"
      - "backups/**"
      - ".local-backups/**"
      - "**/*backup*"
      - "**/*-backup*"
      - "*.log"
      - "*.tmp"
      - "*.temp"
      - ".env*"
      - ".DS_Store"
      - "data/**"

# 只掃描核心源代碼文件
include:
  - name: "core-source"
    paths:
      - "src/**/*.js"
      - "src/**/*.ts"
      - "server.js"
      - "deno-server.ts"
      - "deno-server-pure.ts"

# 快速依賴安裝
bootstrap: |
  echo "⚡ 快速模式：跳過依賴安裝以節省時間"
