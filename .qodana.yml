# Qodana Configuration for HeartSync v2
# Simplified configuration to avoid unsupported keys

version: '1.0'
linter: 'jetbrains/qodana-js:2025.1'

# Analysis profile
profile:
  name: qodana.recommended

# Exclude files and directories
exclude:
  # Directory exclusions
  - name: "backups"
    paths:
      - "backups/**"
  
  - name: "playwright-report" 
    paths:
      - "playwright-report/**"
      
  - name: "scripts"
    paths:
      - "scripts/**"
      
  - name: "static"
    paths:
      - "static/**"
      
  - name: "streamline-landing-page"
    paths:
      - "streamline-landing-page/**"
      
  - name: "node_modules"
    paths:
      - "node_modules/**"
      
  - name: "build-output"
    paths:
      - "dist/**"
      - "build/**"
      - ".wrangler/**"
      
  # Individual file exclusions
  - name: "test-files"
    paths:
      - "finlight_test_page.html"
      - "multi_api_test_page.html"
      - "hsn-terminal-chat.js"
      - "verify-fix.js"
      - "playwright-debug-cli-test-b.js"
      
  # Config and temporary files
  - name: "config-and-temp"
    paths:
      - "*.log"
      - "*.tmp"
      - "*.temp"
      - ".env*"
      - ".DS_Store"
      - "Thumbs.db"
      - "data/**"

# Include files and directories for analysis
include:
  - name: "source-code"
    paths:
      - "src/**/*.js"
      - "src/**/*.ts"
      - "server.js"
      
  - name: CheckDependencyLicenses

# Bootstrap script for dependency installation
bootstrap: |
  npm ci --only=production || npm install --only=production || true
