# Warp Memory Configuration
# Add this to your ~/.zshrc or ~/.bashrc

# mem0 Memory System Aliases
alias mem0-init="cd /Users/<USER>/heartsync-v2 && python warp_memory_init.py"
alias mem0-status="cd /Users/<USER>/heartsync-v2 && source venv/bin/activate && python -c 'from mem0 import MemoryClient; import os; os.environ[\"MEM0_API_KEY\"]=\"m0-Yth3aS1H72Yipwi4ieeEw1UlwqfECVIvGOnYQ4Qj\"; c=MemoryClient(); m=c.get_all(user_id=\"Weiss@Warp\"); print(f\"📚 {len(m)} memories stored for Weiss@Warp\")'"
alias mem0-track="cd /Users/<USER>/heartsync-v2 && source venv/bin/activate && python conversation_tracker.py"
alias mem0-utils="cd /Users/<USER>/heartsync-v2 && source venv/bin/activate && python mem0_utils.py"
alias mem0-enhanced="cd /Users/<USER>/heartsync-v2 && source venv/bin/activate && python enhanced_mem0_utils.py"
alias agent-config="cd /Users/<USER>/heartsync-v2 && python3 agent_config_utils.py"

# Auto-reminder function for Warp sessions
warp_ai_reminder() {
    echo "🤖 Warp AI Session Started"
    echo "💾 Remember: mem0 memory system is available!"
    echo "🔧 Quick commands: mem0-init | mem0-status | mem0-track"
    echo "📍 Location: /Users/<USER>/heartsync-v2"
}

# Uncomment the line below to show reminder on every new shell session
# warp_ai_reminder

# Environment variables
export MEM0_USER_ID="Weiss@Warp"
export MEM0_PROJECT_PATH="/Users/<USER>/heartsync-v2"
