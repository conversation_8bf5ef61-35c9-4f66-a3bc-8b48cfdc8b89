#!/usr/bin/env -S deno run --allow-net --allow-read --allow-env --allow-write

/**
 * 🦕 HeartSync Deno 服務器
 * 專為 Datastar + Preline UI + Deno 技術棧設計
 * 支援三層數據持久化架構
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
// import { serveStatic } from 'hono/serve-static';
import chatDenoApiRoutes from './src/deno-api/chat-deno-api.js';
// 導入路由 (使用相對路徑，Deno 兼容)
import chatDatastarDenoRoutes from './src/routes/chat-datastar-deno.js';

// 創建主應用
const app = new Hono();

// 全局 CORS 設置 (簡化配置以避免類型衝突)
app.use('*', cors());

// 靜態文件服務 (暫時禁用，Deno 版本的 serveStatic API 不同)
// app.use('/static/*', serveStatic({ root: './' }));

// 🏠 根路由
app.get('/', (c) => {
	return c.json({
		message: '🦕 HeartSync Deno Server',
		status: 'running',
		timestamp: new Date().toISOString(),
		environment: {
			runtime: 'Deno',
			version: Deno.version.deno,
			typescript: Deno.version.typescript,
			v8: Deno.version.v8,
		},
		endpoints: {
			chat: '/chat-datastar-deno',
			api: {
				health: '/api/deno/chat/health',
				messages: '/api/deno/chat/messages',
				chat: '/api/deno/chat/chat',
				stats: '/api/deno/chat/stats',
				test: '/api/deno/chat/test',
			},
		},
		features: {
			datastar: true,
			preline_ui: true,
			three_layer_persistence: true,
			ai_integration: true,
			dark_theme: true,
		},
	});
});

// 🔧 健康檢查端點
app.get('/health', async (c) => {
	try {
		const startTime = Date.now();

		// 檢查系統資源
		const memoryUsage = Deno.memoryUsage?.() || { rss: 0, heapUsed: 0, heapTotal: 0 };

		// 檢查環境變數
		const hasClaudeKey = !!Deno.env.get('CLAUDE_API_KEY');
		const hasTursoUrl = !!Deno.env.get('TURSO_DATABASE_URL');
		const hasTursoToken = !!Deno.env.get('TURSO_AUTH_TOKEN');

		const endTime = Date.now();

		return c.json({
			status: 'healthy',
			timestamp: new Date().toISOString(),
			service: '🦕 HeartSync Deno Server',
			version: '1.0.0-experimental',
			environment: {
				runtime: 'Deno',
				version: Deno.version.deno,
				permissions: {
					net: await checkPermission('net'),
					read: await checkPermission('read'),
					write: await checkPermission('write'),
					env: await checkPermission('env'),
				},
			},
			memory: {
				rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
				heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
				heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
			},
			configuration: {
				claude_api: hasClaudeKey ? '✅ 已配置' : '❌ 未配置',
				turso_database: hasTursoUrl ? '✅ 已配置' : '❌ 未配置',
				turso_auth: hasTursoToken ? '✅ 已配置' : '❌ 未配置',
			},
			performance: {
				response_time_ms: endTime - startTime,
				uptime_seconds: Math.floor(performance.now() / 1000),
			},
		});
	} catch (error) {
		console.error('Health check error:', error);
		return c.json(
			{
				status: 'error',
				timestamp: new Date().toISOString(),
				error: error instanceof Error ? error.message : 'Unknown error',
			},
			500
		);
	}
});

// 檢查 Deno 權限
async function checkPermission(name: string): Promise<string> {
	try {
		const status = await Deno.permissions.query({ name: name as any });
		return status.state;
	} catch (error) {
		return 'unknown';
	}
}

// 🗨️ 註冊聊天路由
console.log('🔧 註冊聊天路由...');
app.route('/', chatDatastarDenoRoutes);
console.log('✅ 聊天路由註冊完成');

// 🔌 註冊 API 路由
console.log('🔧 註冊 Deno API 路由...');
app.route('/api/deno/chat', chatDenoApiRoutes);
console.log('✅ Deno API 路由註冊完成');

// 🚀 啟動服務器
const port = parseInt(Deno.env.get('PORT') || '8000');

console.log(`
🦕 HeartSync Deno Server 啟動中...

📋 服務信息:
   • 運行時: Deno ${Deno.version.deno}
   • TypeScript: ${Deno.version.typescript}
   • V8: ${Deno.version.v8}
   • 端口: ${port}

🎯 主要端點:
   • 聊天界面: http://localhost:${port}/chat-datastar-deno
   • 健康檢查: http://localhost:${port}/health
   • API 文檔: http://localhost:${port}/

🔧 技術棧:
   • 前端: Datastar + Preline UI + Tailwind CSS
   • 後端: Deno + Hono.js
   • 數據庫: SQL.js + libsql-js + Turso LibSQL (三層架構)
   • AI: Claude API

🌟 特色功能:
   • ✅ Dark 主題支援
   • ✅ 響應式設計
   • ✅ 三層數據持久化
   • ✅ AI 智能對話
   • ✅ 實時性能監控

🚀 啟動完成！
`);

// 啟動服務器
Deno.serve(
	{
		port: port,
		onListen: ({ port, hostname }) => {
			console.log(`🌐 服務器運行在 http://${hostname}:${port}`);
			console.log(`🦕 Deno Chat 實驗開始！`);
		},
	},
	app.fetch
);

// 優雅關閉處理
Deno.addSignalListener('SIGINT', () => {
	console.log('\n🛑 收到關閉信號，正在優雅關閉服務器...');
	console.log('👋 HeartSync Deno Server 已關閉');
	Deno.exit(0);
});

Deno.addSignalListener('SIGTERM', () => {
	console.log('\n🛑 收到終止信號，正在關閉服務器...');
	console.log('👋 HeartSync Deno Server 已關閉');
	Deno.exit(0);
});
