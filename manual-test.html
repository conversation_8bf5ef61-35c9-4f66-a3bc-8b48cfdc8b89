<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HSN-50 手動測試</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #0a0a0a;
            color: #00ff00;
            margin: 20px;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: #111;
            border: 1px solid #333;
            padding: 20px;
        }
        .instructions {
            background: #222;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 3px solid #00ff00;
        }
        .test-link {
            display: block;
            background: #333;
            color: #00ffff;
            padding: 10px;
            text-decoration: none;
            margin: 10px 0;
            border: 1px solid #555;
        }
        .test-link:hover {
            background: #444;
        }
        .checklist {
            background: #1a1a1a;
            padding: 15px;
            margin-top: 20px;
        }
        .checklist h3 {
            color: #ffff00;
        }
        .checklist ul {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            margin: 5px 0;
            padding: 5px;
            background: #222;
        }
        .checklist li::before {
            content: "☐ ";
            color: #ff6600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 HSN-50 重複發送問題 - 手動測試</h1>

        <div class="instructions">
            <h2>📋 測試說明</h2>
            <p>我們已經實施了以下修復措施：</p>
            <ul>
                <li>✅ 移除了 sendMessage catch 塊中的重複錯誤訊息添加</li>
                <li>✅ 修復了 fallbackToSimulation 使用 addChatMessage 而不是直接 push</li>
                <li>✅ 添加了防重複發送機制（2秒內相同訊息會被忽略）</li>
                <li>✅ 添加了 sendingInProgress 狀態鎖</li>
            </ul>
        </div>

        <h2>🔗 測試頁面</h2>
        <a href="http://localhost:8787/CLI-preview" class="test-link" target="_blank">
            🖥️ CLI Preview - 實際聊天頁面 (原版)
        </a>
        <a href="http://localhost:8787/CLI-preview-B" class="test-link" target="_blank">
            🛡️ CLI Preview B - 修復版聊天頁面 (HSN-50)
        </a>
        <a href="http://localhost:8787/CLI-preview-A" class="test-link" target="_blank">
            🔬 CLI Preview A - 精密診斷版本 (HSN-50)
        </a>
        <a href="http://localhost:8787/duplicate-test" class="test-link" target="_blank">
            🧪 重複發送測試頁面
        </a>
        <a href="http://localhost:8787/ab-test" class="test-link" target="_blank">
            🔬 A/B 測試頁面 - 修復前後對比
        </a>

        <div class="checklist">
            <h3>🎯 手動測試檢查清單</h3>
            <ul>
                <li>在 CLI Preview 頁面發送一條訊息，檢查是否只有一個 AI 回應</li>
                <li>快速連續點擊發送按鈕，檢查是否被防重複機制阻止</li>
                <li>在 2 秒內發送相同訊息，檢查是否被忽略</li>
                <li>使用 Command+Enter 發送訊息，檢查是否正常工作</li>
                <li>檢查瀏覽器開發者工具的 Console，確認防重複日誌</li>
                <li>檢查 Network 面板，確認 API 請求數量正確</li>
            </ul>
        </div>

        <div class="checklist">
            <h3>🔍 預期結果</h3>
            <ul>
                <li>每條用戶訊息只應該產生一個 AI 回應</li>
                <li>快速連點應該被防護機制阻止</li>
                <li>相同訊息在 2 秒內重複發送應該被忽略</li>
                <li>Console 中應該看到 "🛡️ 防重複發送" 的日誌</li>
                <li>Network 面板中每條訊息只應該有一個 API 請求</li>
            </ul>
        </div>

        <div class="instructions">
            <h3>🚨 如果仍然有問題</h3>
            <p>如果測試後仍然發現重複發送問題，請檢查：</p>
            <ol>
                <li>瀏覽器 Console 中的錯誤訊息</li>
                <li>Network 面板中的 API 請求詳情</li>
                <li>是否有 JavaScript 錯誤導致狀態管理異常</li>
                <li>Alpine.js 的響應式更新是否正常</li>
            </ol>
        </div>
    </div>

    <script>
        // 自動檢查服務狀態
        fetch('http://localhost:8787/')
            .then(response => {
                if (response.ok) {
                    console.log('✅ HeartSync 服務運行正常');
                } else {
                    console.error('❌ HeartSync 服務異常');
                }
            })
            .catch(error => {
                console.error('❌ 無法連接到 HeartSync 服務:', error);
            });
    </script>
</body>
</html>
