#!/usr/bin/env -S deno run --allow-net --allow-read --allow-env --allow-write

/**
 * 🦕 HeartSync Deno 簡化服務器
 * 測試 Deno 基本功能
 */

import { Database } from 'https://deno.land/x/sqlite3@0.12.0/mod.ts';
import { Hono } from 'jsr:@hono/hono@^4.9.0';

// 創建主應用
const app = new Hono();

// 🏠 根路由
app.get('/', (c: any) => {
	return c.json({
		message: '🦕 HeartSync Deno Server (Simple)',
		status: 'running',
		timestamp: new Date().toISOString(),
		environment: {
			runtime: 'Deno',
			version: Deno.version.deno,
			typescript: Deno.version.typescript,
			v8: Deno.version.v8,
		},
		endpoints: {
			health: '/health',
			test: '/test',
		},
	});
});

// 🔧 健康檢查端點
app.get('/health', async (c: any) => {
	try {
		const startTime = Date.now();

		// 檢查系統資源
		const memoryUsage = Deno.memoryUsage?.() || { rss: 0, heapUsed: 0, heapTotal: 0 };

		// 檢查環境變數
		const hasClaudeKey = !!Deno.env.get('CLAUDE_API_KEY');

		const endTime = Date.now();

		return c.json({
			status: 'healthy',
			timestamp: new Date().toISOString(),
			service: '🦕 HeartSync Deno Server (Simple)',
			version: '1.0.0-test',
			environment: {
				runtime: 'Deno',
				version: Deno.version.deno,
			},
			memory: {
				rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
				heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
				heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
			},
			configuration: {
				claude_api: hasClaudeKey ? '✅ 已配置' : '❌ 未配置',
			},
			performance: {
				response_time_ms: endTime - startTime,
				uptime_seconds: Math.floor(performance.now() / 1000),
			},
		});
	} catch (error: any) {
		console.error('Health check error:', error);
		return c.json(
			{
				status: 'error',
				timestamp: new Date().toISOString(),
				error: error.message,
			},
			500
		);
	}
});

// 🧪 測試端點
app.get('/test', (c: any) => {
	return c.json({
		message: '🧪 Deno 測試端點',
		timestamp: new Date().toISOString(),
		tests: {
			deno_version: Deno.version.deno,
			permissions: 'OK',
			memory: 'OK',
			environment: 'OK',
		},
		status: 'success',
	});
});

// 🚀 啟動服務器
const port = parseInt(Deno.env.get('PORT') || '8000');

console.log(`
🦕 HeartSync Deno 簡化服務器啟動中...

📋 服務信息:
   • 運行時: Deno ${Deno.version.deno}
   • TypeScript: ${Deno.version.typescript}
   • V8: ${Deno.version.v8}
   • 端口: ${port}

🎯 測試端點:
   • 根路由: http://localhost:${port}/
   • 健康檢查: http://localhost:${port}/health
   • 測試端點: http://localhost:${port}/test

🚀 啟動完成！
`);

// 啟動服務器
Deno.serve(
	{
		port: port,
		onListen: ({ port, hostname }) => {
			console.log(`🌐 服務器運行在 http://${hostname}:${port}`);
			console.log(`🦕 Deno 測試開始！`);
		},
	},
	app.fetch
);

// 優雅關閉處理
Deno.addSignalListener('SIGINT', () => {
	console.log('\n🛑 收到關閉信號，正在優雅關閉服務器...');
	console.log('👋 HeartSync Deno Server 已關閉');
	Deno.exit(0);
});

Deno.addSignalListener('SIGTERM', () => {
	console.log('\n🛑 收到終止信號，正在關閉服務器...');
	console.log('👋 HeartSync Deno Server 已關閉');
	Deno.exit(0);
});
