// Docs: https://www.instantdb.com/docs/permissions

import type { InstantRules } from '@instantdb/react';

/**
 * Welcome to Instant's permission system!
 * These rules define what users can do with your data.
 * Check out the docs: https://www.instantdb.com/docs/permissions
 */

const rules = {
	// Posts permissions
	posts: {
		allow: {
			view: 'true', // Anyone can view posts
			create: 'auth.id != null', // Only authenticated users can create
			update: 'isOwner', // Only post owner can update
			delete: 'isOwner', // Only post owner can delete
		},
		bind: ['isOwner', 'auth.id != null && auth.id == data.authorId'],
	},

	// Comments permissions
	comments: {
		allow: {
			view: 'true', // Anyone can view comments
			create: 'auth.id != null', // Only authenticated users can comment
			update: 'isOwner', // Only comment owner can update
			delete: 'isOwner', // Only comment owner can delete
		},
		bind: ['isOwner', 'auth.id != null && auth.id == data.userId'],
	},

	// Likes permissions
	likes: {
		allow: {
			view: 'true', // Anyone can view likes
			create: 'auth.id != null', // Only authenticated users can like
			update: 'isOwner', // Only like owner can update
			delete: 'isOwner', // Only like owner can delete
		},
		bind: ['isOwner', 'auth.id != null && auth.id == data.userId'],
	},

	// Users permissions
	users: {
		allow: {
			view: 'true', // Anyone can view user profiles
			create: 'auth.id != null', // Only authenticated users can create profiles
			update: 'isOwner', // Only user can update their own profile
			delete: 'isOwner', // Only user can delete their own profile
		},
		bind: ['isOwner', 'auth.id != null && auth.id == data.id'],
	},
} satisfies InstantRules;

export default rules;
