# 🐋 HeartSync v2 Base Container
# Multi-stage build for optimized production image

# Stage 1: Dependencies
FROM node:18-alpine AS dependencies
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production --no-audit --no-fund

# Stage 2: Build
FROM node:18-alpine AS build
WORKDIR /app

# Copy package files and install all dependencies (including dev)
COPY package*.json ./
RUN npm ci --no-audit --no-fund

# Copy source code
COPY src/ ./src/
COPY tsconfig.json ./
COPY biome.json ./

# Run build tasks (linting, formatting, etc.)
RUN npm run check || true
RUN npm run format:check || true

# Stage 3: Production
FROM node:18-alpine AS production

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S heartsync -u 1001

# Set working directory
WORKDIR /app

# Copy dependencies from stage 1
COPY --from=dependencies /app/node_modules ./node_modules

# Copy application source
COPY --from=build /app/src ./src
COPY --from=build /app/package*.json ./

# Create directories for logs and cache
RUN mkdir -p /app/logs /app/cache
RUN chown -R heartsync:nodejs /app

# Switch to non-root user
USER heartsync

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "fetch('http://localhost:8787/health').then(r=>r.ok?process.exit(0):process.exit(1))" || exit 1

# Expose port
EXPOSE 8787

# Environment variables
ENV NODE_ENV=production
ENV PORT=8787

# Start application
CMD ["node", "src/index.js"]
