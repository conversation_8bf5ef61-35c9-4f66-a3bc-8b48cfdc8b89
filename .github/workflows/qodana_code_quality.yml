#-------------------------------------------------------------------------------#
#        Discover additional configuration options in our documentation         #
#               https://www.jetbrains.com/help/qodana/github.html               #
#-------------------------------------------------------------------------------#

name: Qodana
on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - main
      - develop
    # 只在代碼文件變更時觸發
    paths:
      - 'src/**'
      - 'server.js'
      - 'deno-server.ts'
      - 'deno-server-pure.ts'
      - 'package.json'
      - 'deno.json'

jobs:
  qodana:
    runs-on: ubuntu-latest
    # 添加條件執行：只在包含 [qodana] 或 [ci:full] 時運行，或者是 PR
    if: contains(github.event.head_commit.message, '[qodana]') || contains(github.event.head_commit.message, '[ci:full]') || github.event_name == 'pull_request' || github.event_name == 'workflow_dispatch'
    permissions:
      contents: write
      pull-requests: write
      checks: write
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0
      - name: 'Qodana Scan'
        uses: JetBrains/qodana-action@v2025.1
        env:
          QODANA_TOKEN: ${{ secrets.QODANA_TOKEN_1995316776 }}
        with:
          # 優化配置以提升速度
          pr-mode: false
          use-caches: true
          post-pr-comment: false  # 減少 PR 評論處理時間
          use-annotations: false  # 減少註解處理時間
          upload-result: false    # 跳過結果上傳以節省時間
          push-fixes: 'none'
          # 使用快速掃描模式
          args: '--baseline,.qodana/baseline.sarif.json --fail-threshold,0 --save-report --results-dir,.qodana/results'
