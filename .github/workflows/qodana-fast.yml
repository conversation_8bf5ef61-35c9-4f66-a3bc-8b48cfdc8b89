#-------------------------------------------------------------------------------#
#                    Qodana Fast - 快速代碼質量檢查                              #
#                   只檢查核心文件，大幅減少執行時間                              #
#-------------------------------------------------------------------------------#

name: "⚡ Qodana Fast"

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/**/*.js'
      - 'src/**/*.ts'
      - 'server.js'
      - 'deno-server*.ts'
    # 忽略文檔和配置文件變更
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.github/**'
      - '.qodana/**'

jobs:
  qodana-fast:
    name: "⚡ Fast Code Quality Check"
    runs-on: ubuntu-latest
    # 只在包含 [qodana:fast] 時運行，或者是小型變更
    if: contains(github.event.head_commit.message, '[qodana:fast]') || github.event_name == 'workflow_dispatch'
    
    permissions:
      contents: read
      pull-requests: write
      checks: write

    steps:
      - name: "📥 Checkout code"
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: "⚡ Fast Qodana Scan"
        uses: JetBrains/qodana-action@v2025.1
        env:
          QODANA_TOKEN: ${{ secrets.QODANA_TOKEN_1995316776 }}
        with:
          # 最小化配置以獲得最快速度
          pr-mode: false
          use-caches: true
          post-pr-comment: false
          use-annotations: false
          upload-result: false
          push-fixes: 'none'
          # 快速掃描參數
          args: '--fail-threshold,0 --disable-sanity --skip-pull-request-checks --property=idea.max.intellisense.filesize=50000'

      - name: "📊 Quick Summary"
        run: |
          echo "⚡ Fast Qodana scan completed"
          echo "🎯 Focus: Core source files only"
          echo "⏱️ Optimized for speed over completeness"
          if [ -f .qodana/results/qodana.sarif.json ]; then
            echo "✅ Results generated successfully"
          else
            echo "ℹ️ No issues found or results not generated"
          fi
