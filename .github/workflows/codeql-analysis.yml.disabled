name: "CodeQL Analysis"

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  analyze:
    name: Analyze JavaScript
    runs-on: ubuntu-latest
    
    permissions:
      security-events: write
      contents: read
      actions: read

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: javascript
        build-mode: none

    - name: Perform Analysis
      uses: github/codeql-action/analyze@v3
