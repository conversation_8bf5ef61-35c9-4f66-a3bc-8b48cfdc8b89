name: DeepSource

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    name: DeepSource
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        # Fetches entire repository history so we can analyze commits over time
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Report results to DeepSource
      run: |
        # Install deepsource CLI
        curl https://deepsource.io/cli | sh

        # Report test coverage to deepsource
        ./bin/deepsource report --analyzer test-coverage --key javascript --value-file ./coverage/lcov.info || echo "No coverage file found, skipping coverage report"
