name: "HeartSync v2 CodeQL Config"

# Disable default queries to focus on security
disable-default-queries: false

# Use security-focused query suites
queries:
  - name: security-extended
    uses: security-extended
  - name: security-and-quality
    uses: security-and-quality

# Path filters - analyze only important directories
paths:
  - src
  - scripts
  - "*.js"
  - "*.ts"

# Ignore certain paths that don't need security analysis
paths-ignore:
  - node_modules
  - "**/*.test.js"
  - "**/*.spec.js"
  - "**/*.backup"
  - "**/*.bak"
  - "src/index[*].js"  # Ignore backup files with brackets

# Custom query packs for Node.js specific vulnerabilities
packs:
  - codeql/javascript-queries

# Enable experimental features
experimental:
  - multi-language-repo
