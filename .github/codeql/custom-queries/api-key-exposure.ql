/**
 * @name API Key Exposure
 * @description Detects potential API key exposure in code
 * @kind problem
 * @problem.severity error
 * @security-severity 8.5
 * @precision high
 * @id js/api-key-exposure
 * @tags security
 *       external/cwe/cwe-798
 */

import javascript

from StringLiteral str
where
  // Check for common API key patterns
  str.getValue().regexpMatch("(?i).*(api[_-]?key|secret|token|password|auth).*[=:].*[a-zA-Z0-9]{20,}.*") and
  // Exclude common test/example patterns
  not str.getValue().regexpMatch("(?i).*(test|example|demo|placeholder|your[_-]?key|fake).*") and
  // Exclude environment variable references
  not str.getValue().regexpMatch(".*process\\.env.*") and
  // Exclude obvious placeholders
  not str.getValue().regexpMatch(".*[xX]{3,}.*") and
  not str.getValue().regexpMatch(".*[*]{3,}.*")
select str, "Potential API key or secret hardcoded in source code"
