/**
 * @name Unsafe HTTP Request Handling
 * @description Detects unsafe HTTP request handling that could lead to vulnerabilities
 * @kind problem
 * @problem.severity warning
 * @security-severity 7.0
 * @precision medium
 * @id js/unsafe-http-handling
 * @tags security
 *       external/cwe/cwe-20
 */

import javascript

from CallExpr call, string method
where
  // Check for HTTP request handlers
  (
    call.getCalleeName() = "get" or
    call.getCalleeName() = "post" or
    call.getCalleeName() = "put" or
    call.getCalleeName() = "delete" or
    call.getCalleeName() = "patch"
  ) and
  // Check if the handler function doesn't validate input
  exists(Function handler |
    handler = call.getAnArgument() and
    method = call.getCalleeName() and
    // Look for handlers that directly use request parameters without validation
    exists(PropAccess access |
      access.getBase().getName() = "req" and
      (
        access.getPropertyName() = "body" or
        access.getPropertyName() = "query" or
        access.getPropertyName() = "params"
      ) and
      access.getEnclosingFunction() = handler and
      // Check if there's no validation before usage
      not exists(CallExpr validation |
        validation.getCalleeName().regexpMatch(".*[Vv]alidat.*|.*[Ss]anitiz.*|.*[Cc]heck.*") and
        validation.getEnclosingFunction() = handler
      )
    )
  )
select call, "HTTP " + method.toUpperCase() + " handler may not properly validate input parameters"
