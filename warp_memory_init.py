#!/usr/bin/env python3
"""
Warp Memory Initialization Script
Ensures mem0 memory system is available and reminds about conversation tracking
"""

import os
import sys
from datetime import datetime

def check_mem0_setup():
    """Check if mem0 is properly set up and accessible"""
    try:
        # Check if we're in the right directory
        if not os.path.exists("venv/lib/python3.13/site-packages/mem0"):
            print("⚠️  mem0 virtual environment not found in current directory")
            print("💡 Navigate to /Users/<USER>/heartsync-v2 to access mem0")
            return False
        
        # Test mem0 import
        sys.path.insert(0, "venv/lib/python3.13/site-packages")
        from mem0 import MemoryClient
        
        # Test connection
        os.environ["MEM0_API_KEY"] = "m0-Yth3aS1H72Yipwi4ieeEw1UlwqfECVIvGOnYQ4Qj"
        client = MemoryClient()
        
        # Quick memory count check
        memories = client.get_all(user_id="Weiss@Warp")
        memory_count = len(memories) if memories else 0
        
        print("✅ mem0 Memory System Active")
        print(f"📚 {memory_count} memories stored for Weiss@Warp")
        print("🤖 Ready to track this conversation session")
        return True
        
    except Exception as e:
        print(f"❌ mem0 setup issue: {e}")
        print("💡 Run setup commands if needed:")
        print("   cd /Users/<USER>/heartsync-v2")
        print("   source venv/bin/activate")
        return False

def show_memory_commands():
    """Display available memory commands"""
    print("\n🛠️  Available Memory Commands:")
    print("   python conversation_tracker.py  # Record session highlights")
    print("   python mem0_utils.py           # Test memory operations")
    print("   python warp_memory_init.py     # Check memory system status")
    
def log_session_start():
    """Log that a new Warp session has started"""
    try:
        sys.path.insert(0, "venv/lib/python3.13/site-packages")
        from mem0 import MemoryClient
        
        os.environ["MEM0_API_KEY"] = "m0-Yth3aS1H72Yipwi4ieeEw1UlwqfECVIvGOnYQ4Qj"
        client = MemoryClient()
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
        session_info = f"Started new Warp AI session at {timestamp}"
        
        client.add(
            messages=[{"role": "user", "content": session_info}],
            user_id="Weiss@Warp"
        )
        print(f"📝 Session start logged: {timestamp}")
        
    except Exception as e:
        print(f"⚠️  Could not log session start: {e}")

def main():
    """Main initialization routine"""
    print("🚀 Warp Memory System Initializing...")
    print("=" * 50)
    
    # Check mem0 setup
    if check_mem0_setup():
        show_memory_commands()
        log_session_start()
        
        print("\n💡 Reminder: I can now use mem0 to:")
        print("   • Remember our conversation highlights")
        print("   • Track project progress")
        print("   • Store your preferences and discoveries")
        print("   • Build persistent context across sessions")
        
    print("\n🎯 mem0 Memory System Ready!")

if __name__ == "__main__":
    main()
