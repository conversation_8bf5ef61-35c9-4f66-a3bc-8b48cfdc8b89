# 擴展 Cohere API 測試覆蓋範圍

## 描述
擴展現有的 Cohere API 測試集合，添加更多功能性測試和錯誤處理測試，以確保與 Cohere API 的集成更加穩定可靠。

## 任務
- [x] 審查現有的 Cohere API 測試
- [ ] 添加 Embeddings API 測試
- [ ] 添加 Classify API 測試
- [ ] 添加 Summarize API 測試
- [ ] 添加錯誤處理測試（無效 API 密鑰、無效參數等）
- [ ] 更新測試文檔

## 優先級
中

## 估計時間
3-4 小時

## 相關資源
- [Cohere API 文檔](https://docs.cohere.com/reference/about)
- [<PERSON> 文檔](https://docs.usebruno.com/)
- 現有測試: `api-tests/collections/cohere-api/`