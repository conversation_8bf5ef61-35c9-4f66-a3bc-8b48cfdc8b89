# HeartSync v2 轉移腳本
# 用於將檔案從 heartsync-v2-fresh 轉移到目標環境

param(
    [Parameter(Mandatory=$true)]
    [string]$TargetPath,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun = $false
)

Write-Host "🚀 HeartSync v2 轉移腳本" -ForegroundColor Cyan
Write-Host "來源: $PWD" -ForegroundColor Yellow
Write-Host "目標: $TargetPath" -ForegroundColor Yellow

if ($DryRun) {
    Write-Host "⚠️  DRY RUN 模式 - 不會實際複製檔案" -ForegroundColor Magenta
}

# 檢查目標路徑
if (-not (Test-Path $TargetPath)) {
    Write-Host "❌ 目標路徑不存在: $TargetPath" -ForegroundColor Red
    exit 1
}

# 核心檔案列表
$CoreFiles = @(
    "server.js",
    "package.json", 
    "package-lock.json",
    ".env.example",
    ".gitignore",
    "instant.perms.ts",
    "instant.schema.ts"
)

# 路由檔案列表
$RouteFiles = @(
    "src/index.js",
    "src/routes/cli-preview.js",
    "src/routes/knowledge-preview.js", 
    "src/routes/instantdb.js",
    "src/routes/alpine-jazz-test.js",
    "src/routes/datastar-test.js"
)

# 文檔檔案列表
$DocFiles = @(
    "CHANGELOG.md",
    "PROJECT-FILES-AND-ROUTES.md",
    "README.md.backup",
    "TRANSFER_GUIDE.md",
    "TRANSFER_CHECKLIST.md",
    "UNTRACKED_FILES_ANALYSIS.md"
)

function Copy-FileWithBackup {
    param($SourceFile, $TargetFile)
    
    if (Test-Path $TargetFile) {
        $BackupFile = "$TargetFile.backup-$(Get-Date -Format 'yyyyMMdd-HHmm')"
        if (-not $DryRun) {
            Copy-Item $TargetFile $BackupFile
        }
        Write-Host "  📋 備份現有檔案: $(Split-Path $BackupFile -Leaf)" -ForegroundColor Yellow
    }
    
    if (-not $DryRun) {
        $TargetDir = Split-Path $TargetFile -Parent
        if (-not (Test-Path $TargetDir)) {
            New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
        }
        Copy-Item $SourceFile $TargetFile -Force
    }
    Write-Host "  ✅ 複製: $(Split-Path $SourceFile -Leaf)" -ForegroundColor Green
}

# 轉移核心檔案
Write-Host "`n🔧 轉移核心檔案..." -ForegroundColor Cyan
foreach ($file in $CoreFiles) {
    if (Test-Path $file) {
        $targetFile = Join-Path $TargetPath $file
        Copy-FileWithBackup $file $targetFile
    } else {
        Write-Host "  ⚠️  檔案不存在: $file" -ForegroundColor Yellow
    }
}

# 轉移路由檔案
Write-Host "`n🌐 轉移路由檔案..." -ForegroundColor Cyan
foreach ($file in $RouteFiles) {
    if (Test-Path $file) {
        $targetFile = Join-Path $TargetPath $file
        Copy-FileWithBackup $file $targetFile
    } else {
        Write-Host "  ⚠️  檔案不存在: $file" -ForegroundColor Yellow
    }
}

# 轉移文檔檔案
Write-Host "`n📚 轉移文檔檔案..." -ForegroundColor Cyan
foreach ($file in $DocFiles) {
    if (Test-Path $file) {
        $targetFile = Join-Path $TargetPath $file
        Copy-FileWithBackup $file $targetFile
    } else {
        Write-Host "  ⚠️  檔案不存在: $file" -ForegroundColor Yellow
    }
}

# 檢查目標環境
Write-Host "`n🔍 檢查目標環境..." -ForegroundColor Cyan

$targetPackageJson = Join-Path $TargetPath "package.json"
if (Test-Path $targetPackageJson) {
    Write-Host "  ✅ package.json 存在" -ForegroundColor Green
} else {
    Write-Host "  ❌ package.json 不存在" -ForegroundColor Red
}

$targetServerJs = Join-Path $TargetPath "server.js"
if (Test-Path $targetServerJs) {
    Write-Host "  ✅ server.js 存在" -ForegroundColor Green
} else {
    Write-Host "  ❌ server.js 不存在" -ForegroundColor Red
}

$targetSrcIndex = Join-Path $TargetPath "src/index.js"
if (Test-Path $targetSrcIndex) {
    Write-Host "  ✅ src/index.js 存在" -ForegroundColor Green
} else {
    Write-Host "  ❌ src/index.js 不存在" -ForegroundColor Red
}

# 提供下一步指示
Write-Host "`n🎯 轉移完成！下一步操作:" -ForegroundColor Green
Write-Host "1. cd `"$TargetPath`"" -ForegroundColor White
Write-Host "2. 複製 .env.example 為 .env 並填入 API Keys" -ForegroundColor White
Write-Host "3. npm install" -ForegroundColor White
Write-Host "4. npm start" -ForegroundColor White
Write-Host "5. 訪問 http://localhost:3000/CLI-preview 測試" -ForegroundColor White

Write-Host "`n📋 使用轉移檢查清單驗證所有功能:" -ForegroundColor Cyan
Write-Host "   查看 TRANSFER_CHECKLIST.md" -ForegroundColor White

if ($DryRun) {
    Write-Host "`n⚠️  這是 DRY RUN - 沒有實際複製檔案" -ForegroundColor Magenta
    Write-Host "   移除 -DryRun 參數來執行實際轉移" -ForegroundColor White
}
