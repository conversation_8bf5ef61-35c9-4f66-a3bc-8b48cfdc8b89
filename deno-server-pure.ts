/**
 * 🦕 HeartSync v2 - Pure Deno Server
 * 純 Deno 版本，避免與 Node.js 版本的類型衝突
 * 
 * 技術棧:
 * - Deno 2.x
 * - <PERSON><PERSON> (JSR 版本)
 * - TypeScript
 */

import { Hono } from 'jsr:@hono/hono@^4';
import { cors } from 'jsr:@hono/hono@^4/cors';

// 創建主應用
const app = new Hono();

// 全局 CORS 設置
app.use('*', cors());

// 健康檢查端點
app.get('/health', (c) => {
	try {
		return c.json({
			status: 'healthy',
			timestamp: new Date().toISOString(),
			runtime: 'deno',
			version: Deno.version.deno,
		});
	} catch (error) {
		console.error('Health check error:', error);
		return c.json(
			{
				status: 'error',
				timestamp: new Date().toISOString(),
				error: error instanceof Error ? error.message : 'Unknown error',
			},
			500
		);
	}
});

// 簡單的聊天 API 端點
app.post('/api/deno/chat/chat', async (c) => {
	try {
		const body = await c.req.json();
		const { message, model = 'claude-3-5-sonnet-20241022' } = body;

		// 模擬 AI 回應 (實際項目中會調用真實的 AI API)
		const response = {
			success: true,
			response: `這是來自 ${model} 的回應：${message}`,
			timestamp: Date.now(),
			model: model,
		};

		return c.json(response);
	} catch (error) {
		console.error('Chat API error:', error);
		return c.json(
			{
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
			},
			500
		);
	}
});

// 模型列表端點
app.get('/api/deno/chat/models', (c) => {
	return c.json({
		success: true,
		data: {
			models: {
				'claude-3-5-sonnet-20241022': {
					name: 'Claude 3.5 Sonnet',
					status: 'available',
				},
				'claude-3-haiku-20240307': {
					name: 'Claude 3 Haiku',
					status: 'available',
				},
			},
		},
	});
});

// 健康檢查端點
app.get('/api/deno/chat/health', (c) => {
	return c.json({
		status: 'healthy',
		timestamp: new Date().toISOString(),
		service: 'deno-chat-api',
	});
});

// 簡單的聊天界面
app.get('/chat-test', (c) => {
	const html = `
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deno Chat Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .chat-container { border: 1px solid #ccc; height: 400px; overflow-y: auto; padding: 10px; margin-bottom: 10px; }
        .input-container { display: flex; gap: 10px; }
        input { flex: 1; padding: 10px; }
        button { padding: 10px 20px; }
        .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .user { background: #e3f2fd; text-align: right; }
        .ai { background: #f3e5f5; }
    </style>
</head>
<body>
    <h1>🦕 Deno Chat Test</h1>
    <div id="chat" class="chat-container"></div>
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="輸入訊息..." />
        <button onclick="sendMessage()">發送</button>
    </div>
    
    <script>
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;
            
            addMessage('user', message);
            input.value = '';
            
            try {
                const response = await fetch('/api/deno/chat/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message })
                });
                
                const data = await response.json();
                addMessage('ai', data.response || 'No response');
            } catch (error) {
                addMessage('ai', 'Error: ' + error.message);
            }
        }
        
        function addMessage(type, content) {
            const chat = document.getElementById('chat');
            const div = document.createElement('div');
            div.className = 'message ' + type;
            div.textContent = content;
            chat.appendChild(div);
            chat.scrollTop = chat.scrollHeight;
        }
        
        document.getElementById('messageInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });
    </script>
</body>
</html>`;
	
	return c.html(html);
});

// 根路由
app.get('/', (c) => {
	return c.json({
		message: '🦕 HeartSync v2 - Pure Deno Server',
		endpoints: [
			'/health',
			'/chat-test',
			'/api/deno/chat/chat',
			'/api/deno/chat/models',
			'/api/deno/chat/health',
		],
	});
});

// 啟動服務器
const port = parseInt(Deno.env.get('PORT') || '8001');

console.log(`🦕 Pure Deno Server starting on port ${port}`);
console.log(`🌐 Health check: http://localhost:${port}/health`);
console.log(`💬 Chat test: http://localhost:${port}/chat-test`);

Deno.serve({ port }, app.fetch);
