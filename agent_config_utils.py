#!/usr/bin/env python3
"""
Agent Configuration Utilities
Handles persistent App ID and Agent ID for mem0 and other services
"""

import json
import os
from datetime import datetime
from typing import Dict, Optional
import uuid

class AgentConfig:
    def __init__(self, config_path: str = ".agent_config.json"):
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            # Create default config if file doesn't exist
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict:
        """Create default configuration with new UUIDs"""
        # Generate a more descriptive App ID that includes Warp and system info
        base_uuid = str(uuid.uuid4())
        enhanced_app_id = self._generate_enhanced_app_id(base_uuid)
        
        config = {
            "agent_info": {
                "app_id": enhanced_app_id,
                "base_app_uuid": base_uuid,
                "agent_id": str(uuid.uuid4()),
                "user_id": "Weiss@Warp",
                "project_name": "heartsync-v2",
                "created_at": datetime.utcnow().isoformat() + "Z",
                "system_info": self._get_system_info()
            },
            "mem0_config": {
                "api_key_env_var": "MEM0_API_KEY",
                "base_url": "https://api.mem0.ai",
                "include_metadata": True
            },
            "session_info": {
                "session_id": None,
                "last_updated": datetime.utcnow().isoformat() + "Z"
            }
        }
        self._save_config(config)
        return config
    
    def _save_config(self, config: Dict):
        """Save configuration to JSON file"""
        with open(self.config_path, 'w') as f:
            json.dump(config, f, indent=2)
    
    @property
    def app_id(self) -> str:
        """Get App ID"""
        return self.config["agent_info"]["app_id"]
    
    @property
    def agent_id(self) -> str:
        """Get Agent ID"""
        return self.config["agent_info"]["agent_id"]
    
    @property
    def user_id(self) -> str:
        """Get User ID"""
        return self.config["agent_info"]["user_id"]
    
    def get_mem0_metadata(self) -> Dict:
        """Get metadata dict for mem0 operations"""
        return {
            "app_id": self.app_id,
            "agent_id": self.agent_id,
            "user_id": self.user_id,
            "project": self.config["agent_info"]["project_name"],
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
    
    def update_session_info(self, session_id: Optional[str] = None):
        """Update session information"""
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        self.config["session_info"]["session_id"] = session_id
        self.config["session_info"]["last_updated"] = datetime.utcnow().isoformat() + "Z"
        self._save_config(self.config)
    
    def _get_system_info(self) -> Dict:
        """Get system information for enhanced App ID"""
        import platform
        import subprocess
        
        system_info = {
            "platform": platform.system(),
            "platform_version": platform.release(),
            "shell": os.environ.get('SHELL', 'unknown').split('/')[-1],
            "terminal": "warp" if os.environ.get('TERM_PROGRAM') == 'Warp' else os.environ.get('TERM_PROGRAM', 'unknown')
        }
        
        # Try to get macOS version
        if system_info["platform"] == "Darwin":
            try:
                result = subprocess.run(['sw_vers', '-productVersion'], capture_output=True, text=True)
                if result.returncode == 0:
                    system_info["macos_version"] = result.stdout.strip()
            except:
                pass
                
        return system_info
    
    def _generate_enhanced_app_id(self, base_uuid: str) -> str:
        """Generate enhanced App ID with Warp and system information"""
        system_info = self._get_system_info()
        
        # Create a descriptive prefix based on system info
        terminal = system_info.get('terminal', 'unknown')
        platform = system_info.get('platform', 'unknown')
        macos_ver = system_info.get('macos_version', '').replace('.', '_')
        
        # Format: warp-macos15_5-project-uuid
        if terminal.lower() == 'warp' and platform == 'Darwin' and macos_ver:
            prefix = f"warp-macos{macos_ver}-heartsync"
        elif terminal.lower() == 'warp':
            prefix = f"warp-{platform.lower()}-heartsync"
        else:
            prefix = f"{terminal.lower()}-{platform.lower()}-heartsync"
            
        # Take first 8 chars of UUID to keep it manageable
        uuid_short = base_uuid.split('-')[0]
        return f"{prefix}-{uuid_short}"
    
    def display_config(self):
        """Display current configuration"""
        print("🤖 Agent Configuration")
        print(f"├── App ID: {self.app_id}")
        if "base_app_uuid" in self.config["agent_info"]:
            print(f"├── Base UUID: {self.config['agent_info']['base_app_uuid']}")
        print(f"├── Agent ID: {self.agent_id}")
        print(f"├── User ID: {self.user_id}")
        print(f"├── Project: {self.config['agent_info']['project_name']}")
        if "system_info" in self.config["agent_info"]:
            sys_info = self.config["agent_info"]["system_info"]
            print(f"├── Terminal: {sys_info.get('terminal', 'unknown')}")
            print(f"├── Platform: {sys_info.get('platform', 'unknown')} {sys_info.get('macos_version', '')}")
        print(f"└── Last Updated: {self.config['session_info']['last_updated']}")

if __name__ == "__main__":
    import sys
    
    config = AgentConfig()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "display":
            config.display_config()
        elif sys.argv[1] == "metadata":
            print(json.dumps(config.get_mem0_metadata(), indent=2))
        elif sys.argv[1] == "session":
            config.update_session_info()
            print(f"Updated session ID: {config.config['session_info']['session_id']}")
    else:
        config.display_config()
