# 💖 HeartSync - VR-Ready Social Platform

> 在這裡，用分享遇見真心

一個女性友善的社交平台，專注於真誠互動與內容分享，具備未來 VR/XR 擴展能力。

## ✨ 主要特色

- 🌸 **女性友善設計** - 安全舒適的社交環境
- 💬 **內容驅動互動** - 透過分享建立真實連結
- 📱 **響應式設計** - 完美支援桌面和行動裝置
- 🚀 **現代化技術棧** - 使用最新 Web 技術
- 🥽 **VR/XR 準備** - 為未來 3D 社交體驗做準備

## 🛠️ 技術棧

### 前端
- **框架**: htmx + Alpine.js / Datastar
- **樣式**: Tailwind CSS (CDN)
- **圖標**: Lucide Icons
- **狀態管理**: Alpine.js reactive data

### 後端
- **框架**: Hono (JavaScript)
- **運行環境**: Node.js 18+ / Cloudflare Workers
- **資料庫**: InstantDB (已整合)
- **部署**: Cloudflare Workers / Railway

### AI 整合
- **主要 API**: Claude 3.5 Sonnet (Anthropic)
- **多模型支援**: OpenRouter API
- **知識搜尋**: Tavily API
- **聊天人格**: 咖啡愛好者、技術達人、書蟲

### 開發工具
- **編輯器**: VS Code + Augment Agent (AI 輔助)
- **程式碼品質**: Biome + 自動格式化
- **版本控制**: Git + GitHub
- **專案管理**: Linear
- **測試工具**: Playwright + 自定義測試套件

## 🚀 快速開始

### 環境需求
- **Node.js**: >= 18.0.0
- **npm**: 最新版本
- **現代瀏覽器**: Chrome, Firefox, Safari
- **API Keys**: Claude API (必需)

### 安裝與運行
```bash
# 克隆專案
git clone https://github.com/QutritSystems/heartsync-v2.git
cd heartsync-v2

# 安裝依賴
npm install

# 設置環境變數
cp .env.example .env
# 編輯 .env 文件，添加您的 API Keys

# 啟動開發服務器
npm install

# 啟動開發服務器
bun run dev
# 或
npm run dev

# 開啟瀏覽器訪問
open http://localhost:8787
```

### 🔗 應用入口
- **主應用**: http://localhost:8787
- **CLI Preview**: http://localhost:8787/CLI-preview
- **聊天界面**: http://localhost:8787/chat-alpine
- **🧪 資料庫測試工具**: http://localhost:8787/database-test
- **知識原型**: http://localhost:8787/knowledge-prototype

## 🧪 測試工具

```bash
# 快速 Claude API 檢查
npm run check:claude

# 詳細 Claude API 診斷
npm run diagnose:claude

# 聊天風格測試
npm run test:style

# Claude API 功能測試
npm run test:claude

# 代碼品質檢查
npm run check
```

## 🎮 主要功能

### 已實現功能 ✅
- **貼文系統** - 發布、瀏覽、互動
- **即時按讚** - 流暢的互動體驗
- **留言系統** - 深度交流功能
- **好感度系統** - 追蹤用戶互動進展
- **響應式設計** - 多設備完美適配
- **主題切換** - 淺色/陰天/深色模式
- **🧪 資料庫測試工具** - InstantDB vs Jazz 對比測試平台
  - 暗夜極簡風 UI 設計
  - 終端機風格命令行
  - 即時系統監控
  - 測試配置面板
  - 快捷鍵支援

### 開發中功能 🚧
- **InstantDB 整合** - 真實即時資料庫
- **用戶認證** - 完整的會員系統
- **即時通知** - 互動即時提醒
- **圖片上傳** - 豐富的內容分享

### 未來規劃 🔮
- **VR/XR 支援** - 3D 虛擬社交空間
- **AI 智能配對** - 基於興趣的推薦
- **空間音訊** - 沉浸式通訊體驗
- **跨平台 Avatar** - 個人化虛擬形象

## 📸 預覽截圖

<!-- 這裡可以加入應用程式截圖 -->
*截圖即將更新...*

## 🤝 參與貢獻

我們歡迎所有形式的貢獻！

### 開發流程
1. Fork 專案到你的 GitHub 帳號
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'feat: add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 建立 Pull Request

### 程式碼規範
- 使用 [Conventional Commits](https://conventionalcommits.org/) 格式
- 遵循專案的 ESLint 和 Prettier 設定
- 確保所有測試通過
- 為新功能撰寫相應的測試

## 📋 專案管理

- **Linear**: [HeartSync 專案看板](https://linear.app/heartsync)
- **GitHub Issues**: [問題追蹤](https://github.com/QutritSystems/heartsync-v2/issues)
- **GitHub Projects**: [開發進度](https://github.com/QutritSystems/heartsync-v2/projects)

## 📚 文檔

- [API 文檔](./docs/api.md) *(規劃中)*
- [部署指南](./docs/deployment.md) *(規劃中)*
- [貢獻指南](./CONTRIBUTING.md) *(規劃中)*

## 🐛 問題回報

發現問題？請使用我們的 [Issue 模板](https://github.com/yourusername/heartsync-v2/issues/new/choose) 回報：

- 🐛 [錯誤回報](https://github.com/yourusername/heartsync-v2/issues/new?template=bug_report.md)
- 🚀 [功能請求](https://github.com/yourusername/heartsync-v2/issues/new?template=feature_request.md)

## 📄 授權條款

本專案採用 [MIT License](./LICENSE) 授權。

## 🙏 致謝

- [htmx](https://htmx.org/) - 現代化的 HTML 增強
- [Alpine.js](https://alpinejs.dev/) - 輕量級 JavaScript 框架
- [Tailwind CSS](https://tailwindcss.com/) - 實用至上的 CSS 框架
- [Hono](https://hono.dev/) - 快速、輕量的 Web 框架
- [Continue.dev](https://continue.dev/) - AI 輔助程式開發

---

**用心建造，用愛分享 💖**

[![GitHub Stars](https://img.shields.io/github/stars/QutritSystems/heartsync-v2?style=social)](https://github.com/QutritSystems/heartsync-v2/stargazers)
[![GitHub Forks](https://img.shields.io/github/forks/QutritSystems/heartsync-v2?style=social)](https://github.com/QutritSystems/heartsync-v2/network/members)
[![GitHub Issues](https://img.shields.io/github/issues/QutritSystems/heartsync-v2)](https://github.com/QutritSystems/heartsync-v2/issues)
