# 🔧 Qodana Configuration for HeartSync v2
# 代碼品質分析配置 - 專為 JavaScript/TypeScript 項目優化

version: '1.0'
linter: 'jetbrains/qodana-js:2025.1'

# 🎛️ 分析配置
profile:
  name: qodana.recommended

# 🚫 排除文件和目錄
exclude:
  # 🗂️ 目錄排除
  - name: "backups"
    paths:
      - "backups/**"

  - name: "playwright-report"
    paths:
      - "playwright-report/**"

  - name: "scripts"
    paths:
      - "scripts/**"

  - name: "static"
    paths:
      - "static/**"

  - name: "streamline-landing-page"
    paths:
      - "streamline-landing-page/**"

  - name: "node_modules"
    paths:
      - "node_modules/**"

  - name: "build-output"
    paths:
      - "dist/**"
      - "build/**"
      - ".wrangler/**"

  # 📄 單個文件排除
  - name: "test-files"
    paths:
      - "finlight_test_page.html"
      - "multi_api_test_page.html"
      - "hsn-terminal-chat.js"
      - "verify-fix.js"
      - "playwright-debug-cli-test-b.js"

  # 🔧 配置和臨時文件
  - name: "config-and-temp"
    paths:
      - "*.log"
      - "*.tmp"
      - "*.temp"
      - ".env*"
      - ".DS_Store"
      - "Thumbs.db"
      - "data/**"

# ✅ 包含的文件和目錄（明確指定要分析的內容）
include:
  - name: "source-code"
    paths:
      - "src/**/*.js"
      - "src/**/*.ts"
      - "server.js"

  - name: CheckDependencyLicenses

# 🔍 檢查配置
inspections:
  # 禁用某些可能過於嚴格的檢查
  - inspection: "JSUnusedGlobalSymbols"
    enabled: false

  - inspection: "JSUnusedLocalSymbols"
    enabled: false

  - inspection: "ES6UnusedImports"
    enabled: false

# 📊 報告配置
report:
  # 設置失敗閾值 - 只有嚴重錯誤才導致失敗
  fail-threshold: 10

# � Baseline 配置 - 將所有現有問題設為基準線
baseline:
  # 啟用 baseline 模式
  include-absent: false
  # 基準線文件路徑
  path: ".qodana/baseline.sarif.json"

# �🔧 其他配置
bootstrap: |
  # 確保依賴已安裝
  npm ci --only=production || npm install --only=production || true
