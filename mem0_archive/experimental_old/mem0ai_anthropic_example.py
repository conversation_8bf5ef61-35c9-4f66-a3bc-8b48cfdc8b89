#!/usr/bin/env python3
"""
Example mem0ai usage with Anthropic Claude API.
Make sure to set your ANTHROPIC_API_KEY in your environment before running.
"""

import os
from mem0 import Memory

def setup_mem0_with_anthropic():
    """Set up mem0ai with Anthropic Claude configuration."""
    
    # Check if API key is set
    api_key = os.getenv('ANTHROPIC_API_KEY')
    if not api_key or api_key == "your-anthropic-api-key-here":
        print("❌ Please set your ANTHROPIC_API_KEY environment variable first!")
        print("   1. Get your API key from: https://console.anthropic.com/")
        print("   2. Edit ~/.zshrc and replace 'your-anthropic-api-key-here' with your actual key")
        print("   3. Run: source ~/.zshrc")
        print("   4. Or set it temporarily: export ANTHROPIC_API_KEY='your-key'")
        return None
    
    # Configure mem0ai to use Anthropic Claude
    config = {
        "llm": {
            "provider": "anthropic",
            "config": {
                "api_key": api_key,
                "model": "claude-3-sonnet-20240229",
                "temperature": 0.1,
                "max_tokens": 2048,
            }
        },
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "mem0_anthropic",
                "path": "./mem0_db"
            }
        }
    }
    
    try:
        memory = Memory.from_config(config)
        print("✅ Successfully initialized mem0ai with Anthropic Claude!")
        return memory
    except Exception as e:
        print(f"❌ Error initializing mem0ai: {e}")
        return None

def demo_memory_operations(memory):
    """Demonstrate basic memory operations."""
    if not memory:
        return
    
    print("\n🧠 Testing memory operations...")
    
    # Add some memories
    try:
        result1 = memory.add("I love working on AI projects in Python", user_id="alice")
        print(f"✅ Added memory 1: {result1}")
        
        result2 = memory.add("I prefer using Claude for creative writing tasks", user_id="alice")
        print(f"✅ Added memory 2: {result2}")
        
        result3 = memory.add("I usually work best in the morning with coffee", user_id="alice")
        print(f"✅ Added memory 3: {result3}")
        
    except Exception as e:
        print(f"❌ Error adding memories: {e}")
        return
    
    # Search memories
    try:
        query = "What does Alice like to work on?"
        results = memory.search(query, user_id="alice")
        print(f"\n🔍 Search results for '{query}':")
        for i, result in enumerate(results, 1):
            print(f"   {i}. {result}")
            
    except Exception as e:
        print(f"❌ Error searching memories: {e}")
        return
    
    # Get all memories for user
    try:
        all_memories = memory.get_all(user_id="alice")
        print(f"\n📋 All memories for Alice ({len(all_memories)} total):")
        for i, mem in enumerate(all_memories, 1):
            print(f"   {i}. {mem}")
            
    except Exception as e:
        print(f"❌ Error getting all memories: {e}")

def main():
    """Main function to run the demo."""
    print("🚀 mem0ai + Anthropic Claude Demo")
    print("=" * 40)
    
    # Set up mem0ai
    memory = setup_mem0_with_anthropic()
    
    if memory:
        # Run demo
        demo_memory_operations(memory)
        
        print("\n🎉 Demo completed successfully!")
        print("\n💡 You can now use mem0ai with Anthropic Claude in your projects!")
    else:
        print("\n❌ Demo failed. Please check your API key configuration.")

if __name__ == "__main__":
    main()
