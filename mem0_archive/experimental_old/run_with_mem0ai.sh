#!/bin/bash

# Wrapper script to run Python with mem0ai installed via pipx
# Usage: ./run_with_mem0ai.sh your_script.py
# Or: ./run_with_mem0ai.sh  (for interactive Python session)

PIPX_MEM0AI_PYTHON="/Users/<USER>/.local/pipx/venvs/mem0ai/bin/python"

if [ $# -eq 0 ]; then
    echo "🐍 Starting Python with mem0ai available..."
    echo "   Try: from mem0 import Memory"
    $PIPX_MEM0AI_PYTHON
else
    echo "🚀 Running $1 with mem0ai available..."
    $PIPX_MEM0AI_PYTHON "$@"
fi
