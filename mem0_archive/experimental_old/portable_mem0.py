#!/usr/bin/env python3
"""
Portable Mem0 Utility
Can be run from any directory - automatically finds the heartsync-v2 config
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

# Add the heartsync-v2 directory to Python path
HEARTSYNC_PATH = "/Users/<USER>/heartsync-v2"
sys.path.insert(0, HEARTSYNC_PATH)

try:
    from agent_config_utils import AgentConfig
except ImportError:
    print(f"❌ Error: Could not import agent_config_utils from {HEARTSYNC_PATH}")
    print("Make sure the heartsync-v2 project contains agent_config_utils.py")
    sys.exit(1)

class PortableMem0Client:
    def __init__(self):
        # Always use the config from heartsync-v2
        config_path = os.path.join(HEARTSYNC_PATH, ".agent_config.json")
        self.config = AgentConfig(config_path)
        self.api_key = os.getenv("MEM0_API_KEY")
        
        if not self.api_key:
            print("❌ MEM0_API_KEY environment variable not set")
            print("Set it with: export MEM0_API_KEY='your-key-here'")
            sys.exit(1)
        
        try:
            from mem0 import MemoryClient
            self.client = MemoryClient(api_key=self.api_key)
        except ImportError:
            print("❌ mem0 package not installed")
            print("Install it with: pip install mem0ai")
            sys.exit(1)
    
    def add_memory(self, text: str, metadata: Optional[Dict] = None) -> str:
        """Add a memory with location context"""
        enhanced_metadata = self.config.get_mem0_metadata()
        enhanced_metadata["current_directory"] = os.getcwd()
        
        if metadata:
            enhanced_metadata.update(metadata)
        
        result = self.client.add(
            messages=[{"role": "user", "content": text}],
            user_id=self.config.user_id,
            metadata=enhanced_metadata
        )
        return result
    
    def search_memories(self, query: str, limit: int = 10) -> List[Dict]:
        """Search memories"""
        return self.client.search(
            query=query,
            user_id=self.config.user_id,
            limit=limit
        )
    
    def get_all_memories(self) -> List[Dict]:
        """Get all memories"""
        return self.client.get_all(user_id=self.config.user_id)
    
    def display_info(self):
        """Display current info"""
        print("🌐 Portable Mem0 Client")
        print(f"├── Current Directory: {os.getcwd()}")
        print(f"├── Config Source: {HEARTSYNC_PATH}")
        print(f"├── App ID: {self.config.app_id[:8]}...")
        print(f"├── Agent ID: {self.config.agent_id[:8]}...")
        print(f"└── User ID: {self.config.user_id}")

def main():
    import sys
    
    try:
        client = PortableMem0Client()
        
        if len(sys.argv) > 1:
            command = sys.argv[1]
            
            if command == "info":
                client.display_info()
            elif command == "add" and len(sys.argv) > 2:
                text = " ".join(sys.argv[2:])
                result = client.add_memory(text)
                print(f"✅ Memory added from {os.getcwd()}")
            elif command == "search" and len(sys.argv) > 2:
                query = " ".join(sys.argv[2:])
                results = client.search_memories(query)
                print(f"🔍 Found {len(results)} memories for '{query}':")
                for i, memory in enumerate(results[:3]):
                    print(f"   {i+1}. {memory['text'][:80]}...")
            elif command == "count":
                memories = client.get_all_memories()
                print(f"📊 Total memories: {len(memories)}")
            else:
                print("Usage:")
                print("  python portable_mem0.py info           - Show client info")
                print("  python portable_mem0.py add <text>     - Add memory")
                print("  python portable_mem0.py search <query> - Search memories")
                print("  python portable_mem0.py count          - Count memories")
        else:
            client.display_info()
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
