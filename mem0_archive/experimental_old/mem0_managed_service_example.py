#!/usr/bin/env python3
"""
Example using mem0 managed service with API key.
This is much simpler than self-hosting and should work perfectly.
"""

import os
from mem0 import MemoryClient

def setup_mem0_client():
    """Set up mem0 client with managed service."""
    
    api_key = os.getenv('MEM0_API_KEY')
    if not api_key or api_key == "your-mem0-api-key-here":
        print("❌ Please set your MEM0_API_KEY environment variable!")
        print("   1. Get your API key from: https://app.mem0.ai/")
        print("   2. Edit ~/.zshrc and replace 'your-mem0-api-key-here' with your actual key")
        print("   3. Run: source ~/.zshrc")
        return None
    
    try:
        client = MemoryClient(api_key=api_key)
        print("✅ Successfully connected to mem0 managed service!")
        return client
    except Exception as e:
        print(f"❌ Error connecting to mem0: {e}")
        return None

def demo_personal_memories():
    """Demo: Personal memories with managed service."""
    
    print("🤖 Personal Memories Demo (Managed Service)")
    print("=" * 45)
    
    client = setup_mem0_client()
    if not client:
        return False
    
    user_id = "databird"
    
    # Add personal memories
    memories = [
        "I'm a developer working on AI projects",
        "I prefer using Python for most programming tasks", 
        "I'm currently working on a project called heartsync-v2",
        "I like to use Claude for creative writing and complex reasoning",
        "I usually work better in the morning with coffee",
        "I'm interested in memory systems and AI agents",
        "I just set up mem0ai with Anthropic Claude integration"
    ]
    
    print("📝 Adding memories...")
    for memory in memories:
        try:
            result = client.add(memory, user_id=user_id)
            print(f"✅ Added: {memory}")
        except Exception as e:
            print(f"⚠️  Error adding '{memory}': {e}")
    
    print("\n🔍 Testing memory search...")
    
    # Test searches
    queries = [
        "What programming language do I prefer?",
        "What project am I working on?",
        "When do I work best?",
        "What AI tools do I use?",
        "What did I just set up?"
    ]
    
    for query in queries:
        try:
            results = client.search(query, user_id=user_id)
            print(f"\n❓ Query: {query}")
            if results:
                for i, result in enumerate(results[:2], 1):
                    print(f"   {i}. {result['memory']}")
            else:
                print("   No results found")
        except Exception as e:
            print(f"   Error: {e}")
    
    print("\n📋 All memories:")
    try:
        all_memories = client.get_all(user_id=user_id)
        for i, mem in enumerate(all_memories, 1):
            print(f"   {i}. {mem['memory']}")
    except Exception as e:
        print(f"   Error retrieving memories: {e}")
    
    return True

def demo_conversation_context():
    """Demo: Conversation with context."""
    
    print("\n\n💬 Conversation Context Demo")
    print("=" * 35)
    
    client = setup_mem0_client()
    if not client:
        return False
    
    user_id = "conversation_demo"
    
    # Simulate a conversation about a trip
    conversation = [
        "I'm planning a trip to Japan in March",
        "I want to visit Tokyo, Kyoto, and Osaka",
        "I'm interested in visiting temples and trying authentic ramen",
        "My budget is around $4000 for 10 days",
        "I'm learning basic Japanese phrases using Duolingo",
        "I prefer staying in traditional ryokans when possible"
    ]
    
    print("🗨️  Adding conversation context...")
    for message in conversation:
        try:
            result = client.add(message, user_id=user_id)
            print(f"💭 Remembered: {message}")
        except Exception as e:
            print(f"⚠️  Error: {e}")
    
    print("\n🤔 Testing context recall...")
    
    # Test context queries
    queries = [
        "Where am I planning to travel?",
        "What's my budget for the trip?",
        "What cities do I want to visit?",
        "What kind of accommodation do I prefer?",
        "How am I learning Japanese?"
    ]
    
    for query in queries:
        try:
            results = client.search(query, user_id=user_id)
            print(f"\n❓ {query}")
            if results:
                print(f"   💡 {results[0]['memory']}")
            else:
                print("   🤷 No relevant memories found")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return True

def demo_memory_management():
    """Demo: Memory management operations."""
    
    print("\n\n🔧 Memory Management Demo")
    print("=" * 30)
    
    client = setup_mem0_client()
    if not client:
        return False
    
    user_id = "management_demo"
    
    # Add some test memories
    test_memories = [
        "I like pizza for lunch",
        "I don't like spicy food",
        "I prefer working from home on Fridays"
    ]
    
    print("📝 Adding test memories...")
    memory_ids = []
    for memory in test_memories:
        try:
            result = client.add(memory, user_id=user_id)
            memory_ids.append(result['id'])
            print(f"✅ Added: {memory} (ID: {result['id']})")
        except Exception as e:
            print(f"⚠️  Error: {e}")
    
    print(f"\n📊 Total memories added: {len(memory_ids)}")
    
    # Update a memory
    if memory_ids:
        try:
            print("\n🔄 Updating a memory...")
            updated_memory = "I actually love spicy food now"
            result = client.update(memory_ids[1], updated_memory)
            print(f"✅ Updated memory: {updated_memory}")
        except Exception as e:
            print(f"⚠️  Error updating memory: {e}")
    
    # Show final state
    print("\n📋 Final memory state:")
    try:
        all_memories = client.get_all(user_id=user_id)
        for i, mem in enumerate(all_memories, 1):
            print(f"   {i}. {mem['memory']} (ID: {mem['id']})")
    except Exception as e:
        print(f"   Error: {e}")
    
    return True

def main():
    """Run all demos."""
    print("🚀 mem0 Managed Service - Complete Demo")
    print("=" * 40)
    
    # Load environment
    success_count = 0
    
    try:
        # Demo 1: Personal memories
        if demo_personal_memories():
            success_count += 1
        
        # Demo 2: Conversation context
        if demo_conversation_context():
            success_count += 1
        
        # Demo 3: Memory management
        if demo_memory_management():
            success_count += 1
        
        print(f"\n\n🎉 Completed {success_count}/3 demos successfully!")
        
        if success_count == 3:
            print("\n💡 mem0 managed service is working perfectly!")
            print("   You can now build AI applications with persistent memory!")
            print("   No need to manage your own vector databases or embeddings!")
        else:
            print("\n⚠️  Some demos had issues. Check your API key and connection.")
            
    except Exception as e:
        print(f"\n❌ Error running demos: {e}")

if __name__ == "__main__":
    main()
