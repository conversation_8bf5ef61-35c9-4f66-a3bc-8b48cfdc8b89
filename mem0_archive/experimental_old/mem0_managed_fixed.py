#!/usr/bin/env python3
"""
Fixed mem0 managed service example with correct API format.
This works with the free plan limitations.
"""

import os
from mem0 import MemoryClient

def setup_mem0_client():
    """Set up mem0 client with managed service."""
    
    api_key = os.getenv('MEM0_API_KEY')
    if not api_key or api_key == "your-mem0-api-key-here":
        print("❌ Please set your MEM0_API_KEY environment variable!")
        return None
    
    try:
        client = MemoryClient(api_key=api_key)
        print("✅ Successfully connected to mem0 managed service!")
        return client
    except Exception as e:
        print(f"❌ Error connecting to mem0: {e}")
        return None

def demo_basic_memories():
    """Demo: Basic memory operations with correct format."""
    
    print("🤖 Basic Memory Demo (Free Plan Compatible)")
    print("=" * 45)
    
    client = setup_mem0_client()
    if not client:
        return False
    
    user_id = "databird"
    
    # Format memories as list of messages (correct API format)
    memories = [
        {
            "role": "user",
            "content": "I'm a developer working on AI projects"
        },
        {
            "role": "user", 
            "content": "I prefer using Python for most programming tasks"
        },
        {
            "role": "user",
            "content": "I'm currently working on a project called heartsync-v2"
        },
        {
            "role": "user",
            "content": "I like to use Claude for creative writing and complex reasoning"
        },
        {
            "role": "user",
            "content": "I usually work better in the morning with coffee"
        }
    ]
    
    print("📝 Adding memories (correct format)...")
    for memory in memories:
        try:
            # Use the messages format that the API expects
            result = client.add([memory], user_id=user_id)
            print(f"✅ Added: {memory['content']}")
            print(f"   Result: {result}")
        except Exception as e:
            print(f"⚠️  Error adding '{memory['content']}': {e}")
    
    # Try to get all memories (this should work on free plan)
    print("\n📋 Retrieving all memories...")
    try:
        all_memories = client.get_all(user_id=user_id)
        if all_memories:
            print(f"Found {len(all_memories)} memories:")
            for i, mem in enumerate(all_memories, 1):
                print(f"   {i}. {mem}")
        else:
            print("   No memories found")
    except Exception as e:
        print(f"   Error retrieving memories: {e}")
    
    return True

def demo_conversation_format():
    """Demo: Conversation format memory."""
    
    print("\n\n💬 Conversation Format Demo")
    print("=" * 35)
    
    client = setup_mem0_client()
    if not client:
        return False
    
    user_id = "conversation_user"
    
    # Format as a conversation (this is the expected format)
    conversation = [
        {"role": "user", "content": "I'm planning a trip to Japan in March"},
        {"role": "assistant", "content": "That sounds exciting! Japan in March is a great time to visit, especially for cherry blossoms."},
        {"role": "user", "content": "I want to visit Tokyo, Kyoto, and Osaka"},
        {"role": "assistant", "content": "Excellent choices! Those three cities will give you a great mix of modern and traditional Japan."},
        {"role": "user", "content": "My budget is around $4000 for 10 days"}
    ]
    
    print("🗨️  Adding conversation...")
    try:
        result = client.add(conversation, user_id=user_id)
        print("✅ Successfully added conversation!")
        print(f"   Result: {result}")
    except Exception as e:
        print(f"⚠️  Error adding conversation: {e}")
    
    print("\n📋 Retrieving conversation memories...")
    try:
        all_memories = client.get_all(user_id=user_id)
        if all_memories:
            print(f"Found {len(all_memories)} conversation memories:")
            for i, mem in enumerate(all_memories, 1):
                print(f"   {i}. {mem}")
        else:
            print("   No memories found")
    except Exception as e:
        print(f"   Error: {e}")
    
    return True

def demo_memory_history():
    """Demo: Memory history (if available)."""
    
    print("\n\n📚 Memory History Demo")
    print("=" * 25)
    
    client = setup_mem0_client()
    if not client:
        return False
    
    user_id = "history_user"
    
    # Add a simple memory first
    try:
        simple_memory = [{"role": "user", "content": "I love programming in Python"}]
        result = client.add(simple_memory, user_id=user_id)
        print(f"✅ Added test memory: {result}")
        
        # Try to get the history if we have a memory ID
        if result and isinstance(result, dict) and 'id' in result:
            memory_id = result['id']
            print(f"\n📖 Getting history for memory ID: {memory_id}")
            
            try:
                history = client.history(memory_id=memory_id)
                print(f"✅ History retrieved: {history}")
            except Exception as e:
                print(f"⚠️  History not available: {e}")
        else:
            print("⚠️  Could not get memory ID for history lookup")
            
    except Exception as e:
        print(f"⚠️  Error in history demo: {e}")
    
    return True

def main():
    """Run all demos with free plan compatibility."""
    print("🚀 mem0 Managed Service - Free Plan Compatible")
    print("=" * 50)
    
    print("ℹ️  Note: This demo works with the FREE plan")
    print("   Some features (like advanced search) require PRO plan")
    print("   But basic memory storage and retrieval should work!\n")
    
    success_count = 0
    
    try:
        # Demo 1: Basic memories with correct format
        if demo_basic_memories():
            success_count += 1
        
        # Demo 2: Conversation format
        if demo_conversation_format():
            success_count += 1
        
        # Demo 3: Memory history
        if demo_memory_history():
            success_count += 1
        
        print(f"\n\n🎉 Completed {success_count}/3 demos!")
        
        if success_count > 0:
            print("\n💡 mem0 managed service basic functionality is working!")
            print("   You can store and retrieve memories with the free plan!")
            print("   For advanced features like search, consider upgrading to PRO.")
        else:
            print("\n⚠️  All demos had issues. Check your API key and connection.")
            
    except Exception as e:
        print(f"\n❌ Error running demos: {e}")

if __name__ == "__main__":
    main()
