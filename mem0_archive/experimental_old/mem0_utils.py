import os
from mem0 import MemoryClient

# Set up the mem0 client
os.environ["MEM0_API_KEY"] = "m0-Yth3aS1H72Yipwi4ieeEw1UlwqfECVIvGOnYQ4Qj"
client = MemoryClient()

USER_ID = "Weiss@Warp"

def add_memory(content, role="user"):
    """Add a memory for Weiss@Warp"""
    try:
        result = client.add(
            messages=[
                {"role": role, "content": content}
            ],
            user_id=USER_ID
        )
        print(f"✅ Memory added: {content}")
        return result
    except Exception as e:
        print(f"❌ Failed to add memory: {e}")
        return None

def get_memories():
    """Get all memories for Weiss@Warp"""
    try:
        memories = client.get_all(user_id=USER_ID)
        print(f"📚 Found {len(memories) if memories else 0} memories for {USER_ID}")
        return memories
    except Exception as e:
        print(f"❌ Failed to get memories: {e}")
        return None

def search_memories(query):
    """Search memories for Weiss@Warp"""
    try:
        results = client.search(query=query, user_id=USER_ID)
        print(f"🔍 Search results for '{query}':")
        return results
    except Exception as e:
        print(f"❌ Search failed: {e}")
        return None

def chat_with_memory(message):
    """Chat using mem0 with memory context"""
    try:
        response = client.chat(
            message=message,
            user_id=USER_ID
        )
        print(f"💬 Chat response: {response}")
        return response
    except Exception as e:
        print(f"❌ Chat failed: {e}")
        return None

def demo():
    """Demo the mem0 utilities"""
    print(f"🚀 Mem0 Demo for {USER_ID}")
    print("=" * 40)
    
    # Try to add multiple memories
    add_memory("I prefer using Python for data analysis and AI projects")
    add_memory("I enjoy working with terminal tools like Warp")
    add_memory("I like to use AI assistants for coding tasks")
    
    print("\n" + "-" * 40)
    
    # Try to get memories
    memories = get_memories()
    
    if memories:
        print("\n📝 Memory contents:")
        for i, memory in enumerate(memories, 1):
            print(f"{i}. {memory}")
    
    print("\n" + "-" * 40)
    
    # Try search (may fail on free plan)
    search_memories("Python")
    
    # Skip chat for now due to parameter issues
    print("\n💬 Chat functionality skipped (parameter issues with free plan)")

if __name__ == "__main__":
    demo()
