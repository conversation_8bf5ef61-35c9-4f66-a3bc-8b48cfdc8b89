#!/usr/bin/env python3
"""
Working mem0ai example with Anthropic Claude.
This demonstrates proper configuration and usage.
"""

import os
from mem0 import Memory

def create_anthropic_memory():
    """Create a Memory instance configured for Anthropic Claude."""
    
    api_key = os.getenv('ANTHROPIC_API_KEY')
    if not api_key or 'your-anthropic-api-key-here' in api_key:
        raise ValueError("Please set your ANTHROPIC_API_KEY environment variable")
    
    config = {
        "llm": {
            "provider": "anthropic",
            "config": {
                "api_key": api_key,
                "model": "claude-3-haiku-20240307",  # Fast and cost-effective
                "temperature": 0.1,
                "max_tokens": 1000,
            }
        },
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "anthropic_memories",
                "path": "./anthropic_mem0_db"
            }
        }
    }
    
    return Memory.from_config(config)

def demo_personal_assistant():
    """Demo: Personal AI Assistant with Memory."""
    
    print("🤖 Personal AI Assistant with Memory Demo")
    print("=" * 45)
    
    memory = create_anthropic_memory()
    user_id = "databird"
    
    # Add some personal preferences and information
    memories_to_add = [
        "I'm a developer working on AI projects",
        "I prefer using Python for most programming tasks",
        "I'm currently working on a project called heartsync-v2",
        "I like to use Claude for creative writing and complex reasoning",
        "I usually work better in the morning with coffee",
        "I'm interested in memory systems and AI agents"
    ]
    
    print("📝 Adding personal memories...")
    for memory_text in memories_to_add:
        try:
            result = memory.add(memory_text, user_id=user_id)
            print(f"✅ Added: {memory_text}")
        except Exception as e:
            print(f"⚠️  Error adding '{memory_text}': {e}")
    
    print("\n🔍 Testing memory search...")
    
    # Test various searches
    queries = [
        "What programming language do I prefer?",
        "What project am I working on?",
        "When do I work best?",
        "What AI model do I like for writing?"
    ]
    
    for query in queries:
        try:
            results = memory.search(query, user_id=user_id)
            print(f"\n❓ Query: {query}")
            if results:
                for i, result in enumerate(results[:2], 1):  # Show top 2 results
                    print(f"   {i}. {result}")
            else:
                print("   No results found")
        except Exception as e:
            print(f"   Error: {e}")
    
    print("\n📋 All stored memories:")
    try:
        all_memories = memory.get_all(user_id=user_id)
        for i, mem in enumerate(all_memories, 1):
            print(f"   {i}. {mem}")
    except Exception as e:
        print(f"   Error retrieving memories: {e}")

def demo_conversation_memory():
    """Demo: Conversation with Memory Context."""
    
    print("\n\n💬 Conversation Memory Demo")
    print("=" * 30)
    
    memory = create_anthropic_memory()
    user_id = "conversation_user"
    
    # Simulate a conversation
    conversation_parts = [
        "I'm planning a trip to Japan next month",
        "I'm particularly interested in visiting Tokyo and Kyoto",
        "I love Japanese food, especially sushi and ramen",
        "I'm learning basic Japanese phrases for the trip",
        "My budget is around $3000 for the whole trip"
    ]
    
    print("🗨️  Simulating conversation...")
    for part in conversation_parts:
        try:
            result = memory.add(part, user_id=user_id)
            print(f"💭 Remembered: {part}")
        except Exception as e:
            print(f"⚠️  Error: {e}")
    
    print("\n🤔 Later conversation - testing memory recall...")
    
    # Test context recall
    queries = [
        "Where am I planning to travel?",
        "What's my budget?",
        "What kind of food do I like?",
        "What cities do I want to visit?"
    ]
    
    for query in queries:
        try:
            results = memory.search(query, user_id=user_id)
            print(f"\n❓ {query}")
            if results:
                print(f"   💡 {results[0]}")
            else:
                print("   🤷 No relevant memories found")
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    """Run all demos."""
    print("🚀 mem0ai + Anthropic Claude - Working Examples")
    print("=" * 50)
    
    try:
        # Demo 1: Personal Assistant
        demo_personal_assistant()
        
        # Demo 2: Conversation Memory
        demo_conversation_memory()
        
        print("\n\n🎉 All demos completed successfully!")
        print("\n💡 mem0ai with Anthropic Claude is working perfectly!")
        print("   You can now build AI applications with persistent memory!")
        
    except Exception as e:
        print(f"\n❌ Error running demos: {e}")
        print("   Check your API key and configuration")

if __name__ == "__main__":
    main()
