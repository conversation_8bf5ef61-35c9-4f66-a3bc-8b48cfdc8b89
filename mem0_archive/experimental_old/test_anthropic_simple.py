#!/usr/bin/env python3
"""
Simple test to check Anthropic configuration with mem0ai.
"""

import os

def test_anthropic_setup():
    """Test Anthropic API key and basic setup."""
    
    print("🔑 Checking Anthropic API Key...")
    api_key = os.getenv('ANTHROPIC_API_KEY')
    
    if not api_key:
        print("❌ ANTHROPIC_API_KEY not found in environment")
        return False
    
    if api_key.startswith('sk-ant-'):
        print(f"✅ API Key found: {api_key[:15]}...")
    else:
        print("⚠️  API Key found but format seems unusual")
    
    print("\n🧪 Testing mem0ai import...")
    try:
        from mem0 import Memory
        print("✅ mem0ai imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import mem0ai: {e}")
        return False
    
    print("\n🔧 Testing basic Memory configuration...")
    try:
        # Try with minimal config first
        config = {
            "llm": {
                "provider": "anthropic",
                "config": {
                    "api_key": api_key,
                    "model": "claude-3-haiku-20240307"  # Using haiku for faster/cheaper testing
                }
            }
        }
        
        memory = Memory.from_config(config)
        print("✅ Memory initialized with Anthropic configuration")
        return memory
        
    except Exception as e:
        print(f"❌ Error initializing Memory: {e}")
        return False

def test_simple_memory_operation(memory):
    """Test a simple memory operation."""
    if not memory:
        return False
        
    print("\n🧠 Testing simple memory operation...")
    try:
        result = memory.add("I like Python programming", user_id="test_user")
        print(f"✅ Successfully added memory: {result}")
        return True
    except Exception as e:
        print(f"❌ Error adding memory: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Simple Anthropic + mem0ai Test")
    print("=" * 35)
    
    memory = test_anthropic_setup()
    
    if memory:
        success = test_simple_memory_operation(memory)
        if success:
            print("\n🎉 All tests passed! Anthropic + mem0ai is working!")
        else:
            print("\n⚠️  Setup works but memory operations failed")
    else:
        print("\n❌ Setup failed")

if __name__ == "__main__":
    main()
