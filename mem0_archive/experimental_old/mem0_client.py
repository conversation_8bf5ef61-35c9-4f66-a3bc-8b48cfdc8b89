import os
from mem0 import MemoryClient

os.environ["MEM0_API_KEY"] = "m0-Yth3aS1H72Yipwi4ieeEw1UlwqfECVIvGOnYQ4Qj"

client = MemoryClient()

def test_mem0_client():
    """Test the mem0 client with basic operations"""
    print("Testing mem0 client...")
    print("Note: Some features may be limited on the FREE plan")
    
    try:
        # Test 1: Try adding a simple memory (without user_id for free plan)
        print("\n1. Testing basic memory addition...")
        try:
            result = client.add(
                messages=[
                    {"role": "user", "content": "I love playing basketball on weekends"}
                ]
            )
            print(f"✅ Memory added successfully: {result}")
        except Exception as e:
            print(f"⚠️  Memory addition failed (may be plan limitation): {e}")
        
        # Test 2: Test client connection and basic info
        print("\n2. Testing client connection...")
        try:
            # Try to get memories (this might work even if add doesn't)
            memories = client.get_all()
            print(f"✅ Client connected successfully. Found {len(memories) if memories else 0} memories")
        except Exception as e:
            print(f"⚠️  Get all memories failed: {e}")
        
        # Test 3: Test search functionality
        print("\n3. Testing search functionality...")
        try:
            search_result = client.search(query="test")
            print(f"✅ Search completed: {search_result}")
        except Exception as e:
            print(f"⚠️  Search failed: {e}")
            
        # Test 4: Show available client methods
        print("\n4. Available client methods:")
        methods = [method for method in dir(client) if not method.startswith('_')]
        print(f"Available methods: {methods}")
        
        # Test 5: Try with user_id (as required by API)
        print("\n5. Testing with user_id='Weiss@Warp'...")
        try:
            result = client.add(
                messages=[
                    {"role": "user", "content": "I enjoy coding in Python and working with AI tools"}
                ],
                user_id="Weiss@Warp"
            )
            print(f"✅ Memory with user_id added successfully: {result}")
            
            # Try to get memories for this user
            user_memories = client.get_all(user_id="Weiss@Warp")
            print(f"✅ Retrieved {len(user_memories) if user_memories else 0} memories for Weiss@Warp")
            
        except Exception as e:
            print(f"⚠️  User-specific operations failed: {e}")
        
        print("\n🔍 Client testing completed (some features may require PRO plan)")
        
    except Exception as e:
        print(f"❌ Critical error during testing: {e}")
        print(f"Error type: {type(e).__name__}")
        print("\n💡 This might be due to API key issues or plan limitations")

if __name__ == "__main__":
    test_mem0_client()
