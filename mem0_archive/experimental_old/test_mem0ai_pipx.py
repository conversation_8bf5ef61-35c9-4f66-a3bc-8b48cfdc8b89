#!/usr/bin/env python3
"""
Test script to verify mem0ai installation via pipx and demonstrate basic usage.
"""

def test_mem0ai_comprehensive():
    """Comprehensive test of mem0ai functionality."""
    
    print("🧪 Testing mem0ai installation (via pipx)...\n")
    
    # Test 1: Import test
    try:
        from mem0 import Memory
        print("✅ Successfully imported Memory from mem0")
        import_success = True
    except ImportError as e:
        print(f"❌ Failed to import Memory from mem0: {e}")
        return False
    
    # Test 2: Check available classes and methods
    try:
        print("✅ Available Memory class methods:")
        methods = [method for method in dir(Memory) if not method.startswith('_')]
        for method in methods[:5]:  # Show first 5 methods
            print(f"   - {method}")
        if len(methods) > 5:
            print(f"   ... and {len(methods) - 5} more methods")
        print()
    except Exception as e:
        print(f"⚠️  Error checking methods: {e}")
    
    # Test 3: Try to create Memory instance (will likely fail without LLM config)
    try:
        memory = Memory()
        print("✅ Successfully created Memory instance with default config")
        
        # Test basic operations if successful
        try:
            # This will likely fail without proper LLM configuration
            result = memory.add("This is a test memory", user_id="test_user")
            print(f"✅ Successfully added memory: {result}")
        except Exception as e:
            print(f"⚠️  Expected: Memory add operation needs LLM configuration: {e}")
            
    except Exception as e:
        print(f"⚠️  Expected: Memory initialization needs configuration: {e}")
        print("   (This is normal - mem0ai needs LLM provider configuration)")
    
    print("\n📋 Installation Verification:")
    print("✅ mem0ai is properly installed via pipx")
    print("✅ Library can be imported successfully")
    print("✅ Core classes and methods are available")
    
    print("\n💡 Next Steps to Use mem0ai:")
    print("   1. Set up an LLM provider (OpenAI, Groq, Anthropic, etc.)")
    print("   2. Set API keys in environment variables")
    print("   3. Configure vector store (optional - defaults to Chroma)")
    print("   4. Initialize Memory with proper configuration")
    
    print("\n🔧 Example usage:")
    print("   import os")
    print("   from mem0 import Memory")
    print("   ")
    print("   # Set your API key")
    print("   os.environ['OPENAI_API_KEY'] = 'your-api-key'")
    print("   ")
    print("   # Initialize Memory")
    print("   memory = Memory()")
    print("   ")
    print("   # Add memories")
    print("   memory.add('I like coffee in the morning', user_id='alice')")
    print("   ")
    print("   # Search memories")
    print("   results = memory.search('morning preferences', user_id='alice')")
    
    return True

if __name__ == "__main__":
    test_mem0ai_comprehensive()
