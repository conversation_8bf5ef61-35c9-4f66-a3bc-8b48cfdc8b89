# HSN Health Monitor PowerShell Wrapper
# This script provides easy management of the health monitoring system

param(
    [Parameter(Position=0)]
    [ValidateSet("start", "stop", "restart", "status", "check", "dashboard", "logs", "install", "help")]
    [string]$Action = "help",
    
    [switch]$Background,
    [switch]$Force
)

$ErrorActionPreference = "Stop"
$ProjectRoot = Split-Path $PSScriptRoot -Parent
$MonitorScript = Join-Path $PSScriptRoot "health-monitor.js"
$PidFile = Join-Path $PSScriptRoot "health-monitor.pid"

function Write-ColoredOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Test-NodeJS {
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-ColoredOutput "✅ Node.js detected: $nodeVersion" "Green"
            return $true
        }
    } catch {
        Write-ColoredOutput "❌ Node.js not found. Please install Node.js first." "Red"
        return $false
    }
    return $false
}

function Get-MonitorPid {
    if (Test-Path $PidFile) {
        try {
            $pid = Get-Content $PidFile -ErrorAction SilentlyContinue
            if ($pid -and (Get-Process -Id $pid -ErrorAction SilentlyContinue)) {
                return $pid
            }
        } catch {
            # PID file exists but process is not running
            Remove-Item $PidFile -Force -ErrorAction SilentlyContinue
        }
    }
    return $null
}

function Start-HealthMonitor {
    Write-ColoredOutput "Starting HSN Health Monitor..." "Cyan"
    
    # Check if already running
    $existingPid = Get-MonitorPid
    if ($existingPid) {
        Write-ColoredOutput "⚠️  Health Monitor is already running (PID: $existingPid)" "Yellow"
        return
    }
    
    # Verify Node.js is available
    if (-not (Test-NodeJS)) {
        return
    }
    
    try {
        Push-Location $ProjectRoot
        
        if ($Background) {
            # Start in background
            $process = Start-Process -FilePath "node" -ArgumentList @($MonitorScript, "start") -NoNewWindow -PassThru -RedirectStandardOutput (Join-Path $PSScriptRoot "monitor-output.log") -RedirectStandardError (Join-Path $PSScriptRoot "monitor-error.log")
            $process.Id | Out-File $PidFile -Encoding ASCII
            Write-ColoredOutput "✅ Health Monitor started in background (PID: $($process.Id))" "Green"
            Write-ColoredOutput "📊 View logs with: ./health-monitor.ps1 logs" "Cyan"
        } else {
            # Start in foreground
            Write-ColoredOutput "📊 Starting in foreground mode (Ctrl+C to stop)..." "Yellow"
            & node $MonitorScript start
        }
    } catch {
        Write-ColoredOutput "❌ Failed to start Health Monitor: $($_.Exception.Message)" "Red"
    } finally {
        Pop-Location
    }
}

function Stop-HealthMonitor {
    Write-ColoredOutput "🛑 Stopping HSN Health Monitor..." "Cyan"
    
    $pid = Get-MonitorPid
    if (-not $pid) {
        Write-ColoredOutput "⚠️  Health Monitor is not running" "Yellow"
        return
    }
    
    try {
        Stop-Process -Id $pid -Force:$Force
        Remove-Item $PidFile -Force -ErrorAction SilentlyContinue
        Write-ColoredOutput "✅ Health Monitor stopped (PID: $pid)" "Green"
    } catch {
        Write-ColoredOutput "❌ Failed to stop Health Monitor: $($_.Exception.Message)" "Red"
    }
}

function Get-HealthStatus {
    $pid = Get-MonitorPid
    if ($pid) {
        $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
        if ($process) {
            Write-ColoredOutput "✅ Health Monitor is running (PID: $pid)" "Green"
            Write-ColoredOutput "📊 CPU Usage: $($process.CPU)%" "Cyan"
            Write-ColoredOutput "🧠 Memory Usage: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB" "Cyan"
            Write-ColoredOutput "⏱️  Start Time: $($process.StartTime)" "Cyan"
        } else {
            Write-ColoredOutput "❌ Health Monitor PID file exists but process is not running" "Red"
            Remove-Item $PidFile -Force -ErrorAction SilentlyContinue
        }
    } else {
        Write-ColoredOutput "⚠️  Health Monitor is not running" "Yellow"
    }
}

function Invoke-HealthCheck {
    Write-ColoredOutput "🏥 Performing health check..." "Cyan"
    
    if (-not (Test-NodeJS)) {
        return
    }
    
    try {
        Push-Location $ProjectRoot
        & node $MonitorScript check
    } catch {
        Write-ColoredOutput "❌ Health check failed: $($_.Exception.Message)" "Red"
    } finally {
        Pop-Location
    }
}

function Show-Dashboard {
    Write-ColoredOutput "📊 Fetching health dashboard..." "Cyan"
    
    if (-not (Test-NodeJS)) {
        return
    }
    
    try {
        Push-Location $ProjectRoot
        & node $MonitorScript dashboard
    } catch {
        Write-ColoredOutput "❌ Failed to fetch dashboard: $($_.Exception.Message)" "Red"
    } finally {
        Pop-Location
    }
}

function Show-Logs {
    Write-ColoredOutput "📄 Showing recent logs..." "Cyan"
    
    $logFile = Join-Path $PSScriptRoot "health-monitor.log"
    $outputLog = Join-Path $PSScriptRoot "monitor-output.log"
    $errorLog = Join-Path $PSScriptRoot "monitor-error.log"
    
    if (Test-Path $logFile) {
        Write-ColoredOutput "=== Health Monitor Logs ===" "Yellow"
        Get-Content $logFile -Tail 50 | ForEach-Object {
            if ($_ -match "\[ERROR\]") {
                Write-ColoredOutput $_ "Red"
            } elseif ($_ -match "\[WARN\]") {
                Write-ColoredOutput $_ "Yellow"
            } elseif ($_ -match "\[ALERT\]") {
                Write-ColoredOutput $_ "Magenta"
            } else {
                Write-ColoredOutput $_ "White"
            }
        }
    } else {
        Write-ColoredOutput "⚠️  No health monitor logs found" "Yellow"
    }
    
    if (Test-Path $outputLog) {
        Write-ColoredOutput "`n=== Process Output Logs ===" "Yellow"
        Get-Content $outputLog -Tail 20
    }
    
    if (Test-Path $errorLog) {
        Write-ColoredOutput "`n=== Process Error Logs ===" "Yellow"
        Get-Content $errorLog -Tail 20
    }
}

function Install-Dependencies {
    Write-ColoredOutput "📦 Installing Health Monitor dependencies..." "Cyan"
    
    if (-not (Test-NodeJS)) {
        return
    }
    
    try {
        Push-Location $ProjectRoot
        
        # Check if package.json exists
        if (-not (Test-Path "package.json")) {
            Write-ColoredOutput "❌ package.json not found in project root" "Red"
            return
        }
        
        # Install dependencies
        Write-ColoredOutput "📦 Running npm install..." "Cyan"
        & npm install
        
        # Add health monitor script to package.json if not exists
        $packageJson = Get-Content "package.json" | ConvertFrom-Json
        if (-not $packageJson.scripts."health:monitor") {
            Write-ColoredOutput "📝 Adding health monitor scripts to package.json..." "Cyan"
            $packageJson.scripts | Add-Member -NotePropertyName "health:monitor" -NotePropertyValue "node scripts/health-monitor.js start" -Force
            $packageJson.scripts | Add-Member -NotePropertyName "health:check" -NotePropertyValue "node scripts/health-monitor.js check" -Force
            $packageJson.scripts | Add-Member -NotePropertyName "health:dashboard" -NotePropertyValue "node scripts/health-monitor.js dashboard" -Force
            $packageJson | ConvertTo-Json -Depth 10 | Set-Content "package.json"
        }
        
        Write-ColoredOutput "✅ Installation completed successfully" "Green"
    } catch {
        Write-ColoredOutput "❌ Installation failed: $($_.Exception.Message)" "Red"
    } finally {
        Pop-Location
    }
}

function Show-Help {
    Write-ColoredOutput "🏥 HSN Health Monitor Management Script" "Cyan"
    Write-ColoredOutput ""
    Write-ColoredOutput "USAGE:" "Yellow"
    Write-ColoredOutput "  .\health-monitor.ps1 <action> [options]" "White"
    Write-ColoredOutput ""
    Write-ColoredOutput "ACTIONS:" "Yellow"
    Write-ColoredOutput "  start      Start the health monitoring system" "White"
    Write-ColoredOutput "  stop       Stop the health monitoring system" "White"
    Write-ColoredOutput "  restart    Restart the health monitoring system" "White"
    Write-ColoredOutput "  status     Show current status of the monitor" "White"
    Write-ColoredOutput "  check      Perform a single health check" "White"
    Write-ColoredOutput "  dashboard  Show the health dashboard" "White"
    Write-ColoredOutput "  logs       Show recent logs" "White"
    Write-ColoredOutput "  install    Install dependencies and setup" "White"
    Write-ColoredOutput "  help       Show this help message" "White"
    Write-ColoredOutput ""
    Write-ColoredOutput "OPTIONS:" "Yellow"
    Write-ColoredOutput "  -Background    Start the monitor in background mode" "White"
    Write-ColoredOutput "  -Force         Force stop the monitor process" "White"
    Write-ColoredOutput ""
    Write-ColoredOutput "EXAMPLES:" "Yellow"
    Write-ColoredOutput "  .\health-monitor.ps1 start -Background" "Cyan"
    Write-ColoredOutput "  .\health-monitor.ps1 stop -Force" "Cyan"
    Write-ColoredOutput "  .\health-monitor.ps1 logs" "Cyan"
    Write-ColoredOutput ""
    Write-ColoredOutput "MONITORING FEATURES:" "Yellow"
    Write-ColoredOutput "  • Automatic server startup and restart" "Green"
    Write-ColoredOutput "  • System resource monitoring (CPU, Memory, Disk)" "Green"
    Write-ColoredOutput "  • API endpoint health checks" "Green"
    Write-ColoredOutput "  • Dependency validation (Claude API, Tavily API)" "Green"
    Write-ColoredOutput "  • Alert system with thresholds" "Green"
    Write-ColoredOutput "  • Detailed logging and metrics collection" "Green"
    Write-ColoredOutput "  • Web dashboard for monitoring status" "Green"
}

# Main script logic
switch ($Action.ToLower()) {
    "start" {
        Start-HealthMonitor
    }
    "stop" {
        Stop-HealthMonitor
    }
    "restart" {
        Stop-HealthMonitor
        Start-Sleep -Seconds 2
        Start-HealthMonitor
    }
    "status" {
        Get-HealthStatus
    }
    "check" {
        Invoke-HealthCheck
    }
    "dashboard" {
        Show-Dashboard
    }
    "logs" {
        Show-Logs
    }
    "install" {
        Install-Dependencies
    }
    "help" {
        Show-Help
    }
    default {
        Write-ColoredOutput "❌ Unknown action: $Action" "Red"
        Show-Help
    }
}
