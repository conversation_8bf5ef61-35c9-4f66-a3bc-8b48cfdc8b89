#!/bin/bash

# Daily Development Environment Check Script
# This script checks the health and availability of your development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}✓${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}⚠${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}✗${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ${NC} $message"
            ;;
    esac
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Start of checks
echo -e "${BLUE}🔍 Daily Development Environment Check${NC}"
echo -e "${BLUE}Date: $(date)${NC}\n"

# System Information
print_header "System Information"
print_status "INFO" "OS: $(uname -s) $(uname -r)"
print_status "INFO" "Architecture: $(uname -m)"
print_status "INFO" "Shell: $SHELL"
print_status "INFO" "Current Directory: $(pwd)"

# Disk Space Check
print_header "Disk Space"
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 80 ]; then
    print_status "OK" "Disk usage: ${DISK_USAGE}%"
elif [ "$DISK_USAGE" -lt 90 ]; then
    print_status "WARN" "Disk usage: ${DISK_USAGE}% (Consider cleaning up)"
else
    print_status "ERROR" "Disk usage: ${DISK_USAGE}% (Critical - clean up now!)"
fi
# Memory Check
print_header "Memory"
if command -v free > /dev/null 2>&1; then
    MEMORY_INFO=$(free -h | grep '^Mem:' | awk '{print "Used: " $3 " / Total: " $2}')
    print_status "INFO" "Memory: $MEMORY_INFO"
else
    # macOS alternative
    MEMORY_PRESSURE=$(memory_pressure 2>/dev/null | head -1 || echo "Memory pressure: unknown")
    print_status "INFO" "$MEMORY_PRESSURE"
fi

# Node.js and NVM Check
print_header "Node.js Environment"
if command -v nvm > /dev/null 2>&1; then
    print_status "OK" "nvm is available"
    NVM_VERSION=$(nvm --version 2>/dev/null || echo "unknown")
    print_status "INFO" "nvm version: $NVM_VERSION"
else
    # Check if nvm is sourced properly
    if [ -f "$HOME/.nvm/nvm.sh" ]; then
        print_status "WARN" "nvm script found but not sourced. Try: source ~/.nvm/nvm.sh"
    else
        print_status "ERROR" "nvm not found or not properly installed"
    fi
fi

if command -v node > /dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    print_status "OK" "Node.js version: $NODE_VERSION"
else
    print_status "ERROR" "Node.js not found"
fi

if command -v npm > /dev/null 2>&1; then
    NPM_VERSION=$(npm --version)
    print_status "OK" "npm version: $NPM_VERSION"
else
    print_status "ERROR" "npm not found"
fi

# Package Manager Alternatives
if command -v yarn >/dev/null 2>&1; then
    YARN_VERSION=$(yarn --version)
    print_status "OK" "Yarn version: $YARN_VERSION"
fi

if command -v pnpm >/dev/null 2>&1; then
    PNPM_VERSION=$(pnpm --version)
    print_status "OK" "pnpm version: $PNPM_VERSION"
fi

# Git Check
print_header "Git Configuration"
if command -v git >/dev/null 2>&1; then
    GIT_VERSION=$(git --version)
    print_status "OK" "$GIT_VERSION"
    
    GIT_USER=$(git config --global user.name 2>/dev/null || echo "Not set")
    GIT_EMAIL=$(git config --global user.email 2>/dev/null || echo "Not set")
    
    if [ "$GIT_USER" != "Not set" ] && [ "$GIT_EMAIL" != "Not set" ]; then
        print_status "OK" "Git user: $GIT_USER <$GIT_EMAIL>"
    else
        print_status "WARN" "Git user configuration incomplete"
    fi
    
    # Check if we're in a git repository
    if git rev-parse --git-dir >/dev/null 2>&1; then
        CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
        print_status "INFO" "Current branch: $CURRENT_BRANCH"
        
        # Check for uncommitted changes
        if ! git diff --quiet 2>/dev/null; then
            print_status "WARN" "You have uncommitted changes"
        fi
        
        # Check for unpushed commits
        if git log @{u}.. --oneline 2>/dev/null | grep -q .; then
            UNPUSHED_COUNT=$(git log @{u}.. --oneline 2>/dev/null | wc -l | tr -d ' ')
            print_status "INFO" "You have $UNPUSHED_COUNT unpushed commits"
        fi
    else
        print_status "INFO" "Not in a git repository"
    fi
else
    print_status "ERROR" "Git not found"
fi

# Project Dependencies Check
print_header "Project Dependencies"
if [ -f "package.json" ]; then
    print_status "OK" "package.json found"
    
    if [ -d "node_modules" ]; then
        print_status "OK" "node_modules directory exists"
        
        # Check if package-lock.json exists and if dependencies are in sync
        if [ -f "package-lock.json" ]; then
            if npm ls >/dev/null 2>&1; then
                print_status "OK" "Dependencies are in sync"
            else
                print_status "WARN" "Dependencies may be out of sync - consider running 'npm install'"
            fi
        elif [ -f "yarn.lock" ]; then
            print_status "INFO" "Using Yarn lock file"
        elif [ -f "pnpm-lock.yaml" ]; then
            print_status "INFO" "Using pnpm lock file"
        else
            print_status "WARN" "No lock file found - consider running 'npm install'"
        fi
    else
        print_status "WARN" "node_modules not found - run 'npm install'"
    fi
    
    # Check for security vulnerabilities
    if command -v npm >/dev/null 2>&1; then
        print_status "INFO" "Checking for security vulnerabilities..."
        if npm audit --audit-level=high >/dev/null 2>&1; then
            print_status "OK" "No high-severity vulnerabilities found"
        else
            print_status "WARN" "Security vulnerabilities found - run 'npm audit' for details"
        fi
    fi
else
    print_status "INFO" "No package.json found in current directory"
fi


# Network Connectivity Check
print_header "Network Connectivity"
if ping -c 1 google.com >/dev/null 2>&1; then
    print_status "OK" "Internet connectivity available"
else
    print_status "ERROR" "No internet connectivity"
fi

# Check common development ports
PORTS=("3000" "8000" "8080" "5000" "9000")
for port in "${PORTS[@]}"; do
    if lsof -i :$port >/dev/null 2>&1; then
        PROCESS=$(lsof -i :$port | tail -1 | awk '{print $1}')
        print_status "INFO" "Port $port is in use by $PROCESS"
    fi
done

# Environment Variables Check
print_header "Environment Variables"
ENV_VARS=("NODE_ENV" "PATH" "HOME" "USER")

for var in "${ENV_VARS[@]}"; do
    if [ -n "${!var}" ]; then
        if [ "$var" = "PATH" ]; then
            print_status "OK" "$var is set (${#PATH} characters)"
        else
            print_status "OK" "$var is set to: ${!var}"
        fi
    else
        print_status "WARN" "$var is not set"
    fi
done

# Summary
print_header "Summary"
echo -e "Daily environment check completed at $(date)"
echo -e "Review any ${YELLOW}warnings${NC} or ${RED}errors${NC} above and take appropriate action.\n"

# Optional: Save log to file
LOG_FILE="$HOME/.dev-check-$(date +%Y%m%d).log"
echo "Log saved to: $LOG_FILE"
