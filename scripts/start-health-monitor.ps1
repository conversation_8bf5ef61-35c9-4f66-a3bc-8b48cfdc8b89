# HSN Health Monitor Simple Launcher
param(
    [string]$Action = "start",
    [switch]$Background
)

$ProjectRoot = Split-Path $PSScriptRoot -Parent
$MonitorScript = Join-Path $PSScriptRoot "health-monitor.js"

function Write-Status {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

# Check Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Status "Node.js detected: $nodeVersion" "Green"
    } else {
        Write-Status "Node.js not found. Please install Node.js first." "Red"
        exit 1
    }
} catch {
    Write-Status "Node.js not found. Please install Node.js first." "Red"
    exit 1
}

Push-Location $ProjectRoot

switch ($Action.ToLower()) {
    "start" {
        Write-Status "Starting HSN Health Monitor..." "Cyan"
        if ($Background) {
            Start-Process -FilePath "node" -ArgumentList @($MonitorScript, "start") -NoNewWindow
            Write-Status "Health Monitor started in background" "Green"
        } else {
            & node $MonitorScript start
        }
    }
    "check" {
        Write-Status "Performing health check..." "Cyan"
        & node $MonitorScript check
    }
    "dashboard" {
        Write-Status "Showing health dashboard..." "Cyan"
        & node $MonitorScript dashboard
    }
    "logs" {
        Write-Status "Showing logs..." "Cyan"
        & node $MonitorScript logs
    }
    default {
        Write-Status "HSN Health Monitor"
        Write-Status "Usage: .\start-health-monitor.ps1 [start|check|dashboard|logs] [-Background]"
        Write-Status "Examples:"
        Write-Status "  .\start-health-monitor.ps1 start"
        Write-Status "  .\start-health-monitor.ps1 start -Background"
        Write-Status "  .\start-health-monitor.ps1 check"
    }
}

Pop-Location
