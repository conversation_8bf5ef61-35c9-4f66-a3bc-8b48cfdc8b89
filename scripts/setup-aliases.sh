#!/bin/bash

# Setup script for dev check aliases
# This script adds convenient aliases to your shell profile

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Setting up dev check aliases...${NC}\n"

# Detect shell and profile file
if [[ "$SHELL" == *"zsh"* ]]; then
    PROFILE_FILE="$HOME/.zshrc"
    SHELL_NAME="zsh"
elif [[ "$SHELL" == *"bash"* ]]; then
    PROFILE_FILE="$HOME/.bashrc"
    SHELL_NAME="bash"
else
    echo -e "${YELLOW}Warning: Unsupported shell. Please manually add aliases to your profile.${NC}"
    exit 1
fi

# Get the current directory (project root)
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Aliases to add
ALIASES="
# Development environment check aliases (added by setup-aliases.sh)
alias devcheck=\"$PROJECT_ROOT/scripts/daily-dev-check.sh\"
alias quickcheck=\"$PROJECT_ROOT/scripts/quick-dev-check.sh\"
alias devsetup=\"$PROJECT_ROOT/scripts/setup-aliases.sh\"
"

# Check if aliases already exist
if grep -q "devcheck=" "$PROFILE_FILE" 2>/dev/null; then
    echo -e "${YELLOW}Aliases already exist in $PROFILE_FILE${NC}"
    echo -e "${YELLOW}To update them, please remove the existing ones first.${NC}"
    exit 0
fi

# Add aliases to profile
echo "$ALIASES" >> "$PROFILE_FILE"

echo -e "${GREEN}✓ Aliases added to $PROFILE_FILE${NC}"
echo -e "${GREEN}✓ Available commands:${NC}"
echo -e "  ${BLUE}devcheck${NC}    - Run comprehensive daily check"
echo -e "  ${BLUE}quickcheck${NC}  - Run quick environment check"
echo -e "  ${BLUE}devsetup${NC}    - Re-run this setup script"

echo -e "\n${YELLOW}To use the aliases immediately, run:${NC}"
echo -e "${BLUE}source $PROFILE_FILE${NC}"
echo -e "\n${YELLOW}Or start a new terminal session.${NC}"

# Optional: Source the profile immediately
read -p "Would you like to source the profile now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    source "$PROFILE_FILE"
    echo -e "${GREEN}✓ Profile sourced successfully!${NC}"
    echo -e "${GREEN}✓ You can now use the aliases in this terminal.${NC}"
fi
