# Daily Development Environment Check Scripts

This directory contains scripts to help you maintain a healthy development environment by performing regular checks on your system, tools, and project dependencies.

## Scripts Overview

### 1. `daily-dev-check.sh` - Comprehensive Check
A thorough daily health check that examines:
- System information (OS, architecture, disk space, memory)
- Node.js environment (nvm, Node.js, npm, yarn, pnpm)
- Git configuration and repository status
- Project dependencies and security vulnerabilities
- Development tools availability
- Network connectivity
- Environment variables
- Port usage

### 2. `quick-dev-check.sh` - Fast Check
A lightweight version for quick daily checks that covers:
- Node.js and npm versions
- Git status and branch information
- Disk space usage
- Project dependencies
- Internet connectivity

## Usage

### Running the Scripts

```bash
# Comprehensive check (recommended for daily use)
./scripts/daily-dev-check.sh

# Quick check (for frequent use throughout the day)
./scripts/quick-dev-check.sh
```

### Setting Up Daily Automation

You can automate these checks by adding them to your shell profile or using cron:

#### Option 1: Shell Profile Integration
Add to your `~/.zshrc` or `~/.bashrc`:

```bash
# Optional: Run quick check when starting a new terminal session
# ./scripts/quick-dev-check.sh
```

#### Option 2: Cron Job (Daily at 9 AM)
```bash
# Edit your crontab
crontab -e

# Add this line for daily checks at 9 AM
0 9 * * * cd /Users/<USER>/heartsync-v2 && ./scripts/daily-dev-check.sh
```

#### Option 3: Create an Alias
Add to your shell profile:
```bash
alias devcheck="./scripts/daily-dev-check.sh"
alias quickcheck="./scripts/quick-dev-check.sh"
```

## Output Interpretation

The scripts use color-coded output:
- 🟢 **Green ✓**: Everything is working correctly
- 🟡 **Yellow ⚠**: Warning - attention may be needed
- 🔴 **Red ✗**: Error - immediate attention required
- 🔵 **Blue ℹ**: Information - for your awareness

## What Each Check Does

### System Information
- Reports OS version, architecture, and current shell
- Shows current working directory

### Disk Space
- Monitors disk usage and alerts when space is low
- Thresholds: OK (<80%), Warning (80-90%), Critical (>90%)

### Memory
- Reports memory usage (Linux) or memory pressure (macOS)

### Node.js Environment
- Verifies nvm installation and sourcing
- Checks Node.js and npm versions
- Detects alternative package managers (yarn, pnpm)

### Git Configuration
- Verifies git installation and user configuration
- Shows current repository status, branch, and uncommitted changes
- Reports unpushed commits

### Project Dependencies
- Checks for package.json and node_modules
- Verifies dependency synchronization
- Scans for security vulnerabilities using npm audit

### Development Tools
- Checks availability of common development tools
- Verifies Docker daemon status
- Reports on editor availability

### Network Connectivity
- Tests internet connectivity
- Reports on ports in use by development servers

### Environment Variables
- Verifies important environment variables are set
- Reports on PATH configuration

## Troubleshooting Common Issues

### nvm not found
If you see "nvm not found", ensure nvm is properly sourced:
```bash
source ~/.nvm/nvm.sh
```

### Dependencies out of sync
If dependencies are out of sync, run:
```bash
npm install
```

### Security vulnerabilities
If vulnerabilities are found, run:
```bash
npm audit
npm audit fix
```

### Git configuration incomplete
Set up your git user information:
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

## Customization

You can customize these scripts by:
1. Modifying the `TOOLS` array to include/exclude specific tools
2. Adjusting disk space thresholds
3. Adding project-specific checks
4. Modifying the list of ports to check
5. Adding environment variables to monitor

## Log Files

The comprehensive script saves logs to:
```
~/.dev-check-YYYYMMDD.log
```

These logs can be useful for tracking environment changes over time.

## Requirements

- macOS or Linux
- Bash shell
- Standard Unix utilities (df, ping, lsof, etc.)
- Git (for repository checks)
- Node.js/npm (for Node.js checks)

## Contributing

Feel free to modify these scripts to better suit your development workflow. Common additions might include:
- Database connectivity checks
- API endpoint health checks
- Cloud service authentication verification
- IDE/editor configuration checks
- Specific project build tool verification
