#!/bin/bash

# 🎯 Download and View Qodana Report Script
# 下載並查看 Qodana 報告的腳本

set -e

echo "🔍 正在下載最新的 Qodana 報告..."

# 創建報告目錄
mkdir -p .qodana/reports
cd .qodana/reports

# 獲取最新的 Qodana 工作流運行 ID
echo "📊 獲取最新的 GitHub Actions 運行..."

# 使用 GitHub CLI 下載最新的 Qodana 報告
if command -v gh &> /dev/null; then
    echo "✅ 使用 GitHub CLI 下載報告..."
    
    # 獲取最新的工作流運行
    RUN_ID=$(gh run list --workflow="qodana_code_quality.yml" --limit=1 --json databaseId --jq '.[0].databaseId')
    
    if [ -n "$RUN_ID" ]; then
        echo "📥 下載運行 ID: $RUN_ID 的報告..."
        gh run download $RUN_ID --name qodana-report
        
        # 解壓報告
        if [ -f "qodana-report.zip" ]; then
            unzip -o qodana-report.zip
            echo "✅ 報告已解壓到 .qodana/reports/"
        fi
        
        # 查找 SARIF 文件
        SARIF_FILE=$(find . -name "*.sarif.json" | head -1)
        
        if [ -n "$SARIF_FILE" ]; then
            echo "🎯 找到 SARIF 報告: $SARIF_FILE"
            echo "📖 在 VS Code 中打開 SARIF 文件..."
            code "$SARIF_FILE"
            echo ""
            echo "💡 提示："
            echo "1. VS Code 會自動使用 SARIF Viewer 擴展打開報告"
            echo "2. 你可以在 Problems 面板中查看所有問題"
            echo "3. 點擊問題可以直接跳轉到相關代碼"
        else
            echo "❌ 未找到 SARIF 報告文件"
        fi
    else
        echo "❌ 未找到 Qodana 工作流運行"
    fi
else
    echo "❌ 請先安裝 GitHub CLI: brew install gh"
    echo "💡 或者手動從 GitHub Actions 下載 qodana-report.zip"
fi

echo ""
echo "🔗 你也可以在以下位置查看報告："
echo "   - GitHub Actions: https://github.com/QutritSystems/heartsync-v2/actions"
echo "   - Qodana Cloud: https://qodana.cloud"
