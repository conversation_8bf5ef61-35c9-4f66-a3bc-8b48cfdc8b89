#!/bin/bash

# HeartSync v2 - Local CodeQL Security Analysis Script
# This script sets up and runs CodeQL analysis locally for development

set -e

echo "🔍 HeartSync v2 - Local CodeQL Security Analysis"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
CODEQL_VERSION="2.21.0"
CODEQL_DIR="$HOME/.codeql"
CODEQL_CLI="$CODEQL_DIR/codeql/codeql"
PROJECT_DIR="$(pwd)"
RESULTS_DIR="$PROJECT_DIR/codeql-results"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to download and setup CodeQL CLI
setup_codeql() {
    echo -e "${YELLOW}Setting up CodeQL CLI...${NC}"
    
    if [ ! -d "$CODEQL_DIR" ]; then
        mkdir -p "$CODEQL_DIR"
    fi
    
    if [ ! -f "$CODEQL_CLI" ]; then
        echo "Downloading CodeQL CLI v$CODEQL_VERSION..."
        
        # Determine OS and architecture
        OS=$(uname -s | tr '[:upper:]' '[:lower:]')
        ARCH=$(uname -m)
        
        case "$ARCH" in
            x86_64) ARCH="64" ;;
            arm64|aarch64) ARCH="64" ;;  # CodeQL uses same naming for both x64 and arm64 on macOS
            *) echo "Unsupported architecture: $ARCH" && exit 1 ;;
        esac
        
        case "$OS" in
            darwin) OS="osx" ;;
            linux) OS="linux" ;;
            *) echo "Unsupported OS: $OS" && exit 1 ;;
        esac
        
        DOWNLOAD_URL="https://github.com/github/codeql-cli-binaries/releases/download/v$CODEQL_VERSION/codeql-$OS$ARCH.zip"
        
        echo "Download URL: $DOWNLOAD_URL"
        
        # Download and extract with better error handling
        cd "$CODEQL_DIR"
        echo "Downloading to $(pwd)/codeql.zip..."
        
        # Download with better curl options
        if curl -L --fail --show-error --progress-bar "$DOWNLOAD_URL" -o codeql.zip; then
            echo "Download completed successfully"
            
            # Verify the download
            if [ ! -f "codeql.zip" ] || [ ! -s "codeql.zip" ]; then
                echo -e "${RED}❌ Download failed - file is missing or empty${NC}"
                exit 1
            fi
            
            # Check if it's actually a zip file
            if ! file codeql.zip | grep -q "Zip archive"; then
                echo -e "${RED}❌ Downloaded file is not a valid ZIP archive${NC}"
                echo "File type: $(file codeql.zip)"
                echo "File size: $(ls -lh codeql.zip | awk '{print $5}')"
                echo "First few bytes:"
                head -c 100 codeql.zip | hexdump -C
                rm -f codeql.zip
                exit 1
            fi
            
            # Extract the zip file
            echo "Extracting CodeQL CLI..."
            if unzip -q codeql.zip; then
                rm codeql.zip
                
                # Make executable (should already be executable)
                chmod +x "$CODEQL_CLI"
                
                echo -e "${GREEN}✅ CodeQL CLI installed successfully${NC}"
            else
                echo -e "${RED}❌ Failed to extract CodeQL CLI${NC}"
                rm -f codeql.zip
                exit 1
            fi
        else
            echo -e "${RED}❌ Failed to download CodeQL CLI${NC}"
            echo "Please check your internet connection and try again."
            echo "You can also manually download from: $DOWNLOAD_URL"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ CodeQL CLI already installed${NC}"
    fi
}

# Function to download CodeQL standard library
setup_codeql_library() {
    echo -e "${YELLOW}Setting up CodeQL standard library...${NC}"
    
    if [ ! -d "$CODEQL_DIR/codeql-repo" ]; then
        cd "$CODEQL_DIR"
        
        # Check if git-lfs is available
        if ! command_exists git-lfs; then
            echo -e "${RED}❌ git-lfs is required but not installed${NC}"
            echo "Please install git-lfs first:"
            echo "  macOS: brew install git-lfs && git lfs install"
            echo "  Ubuntu: sudo apt-get install git-lfs && git lfs install"
            exit 1
        fi
        
        echo "Cloning CodeQL standard library (this may take a few minutes)..."
        if git clone --branch "codeql-cli/v$CODEQL_VERSION" https://github.com/github/codeql.git codeql-repo; then
            echo -e "${GREEN}✅ CodeQL standard library installed${NC}"
        else
            echo -e "${RED}❌ Failed to clone CodeQL standard library${NC}"
            echo "Trying to clone main branch instead..."
            if git clone https://github.com/github/codeql.git codeql-repo; then
                echo -e "${GREEN}✅ CodeQL standard library installed (main branch)${NC}"
            else
                echo -e "${RED}❌ Failed to clone CodeQL standard library${NC}"
                echo "You can try running 'git lfs install' and retrying"
                exit 1
            fi
        fi
    else
        echo -e "${GREEN}✅ CodeQL standard library already installed${NC}"
    fi
}

# Function to create CodeQL database
create_database() {
    echo -e "${YELLOW}Creating CodeQL database...${NC}"
    
    # Remove existing database if it exists
    if [ -d "$RESULTS_DIR/heartsync-db" ]; then
        rm -rf "$RESULTS_DIR/heartsync-db"
    fi
    
    mkdir -p "$RESULTS_DIR"
    
    # Create database for JavaScript/TypeScript
    "$CODEQL_CLI" database create \
        "$RESULTS_DIR/heartsync-db" \
        --language=javascript \
        --source-root="$PROJECT_DIR" \
        --command="npm install"
    
    echo -e "${GREEN}✅ CodeQL database created${NC}"
}

# Function to run CodeQL analysis
run_analysis() {
    echo -e "${YELLOW}Running CodeQL security analysis...${NC}"
    
    # Run selected security queries to avoid metadata issues
    echo "Running core security queries..."
    
    # Create a list of important security queries
    SECURITY_QUERIES=(
        "$CODEQL_DIR/codeql-repo/javascript/ql/src/Security/CWE-079/Xss.ql"
        "$CODEQL_DIR/codeql-repo/javascript/ql/src/Security/CWE-089/SqlInjection.ql"
        "$CODEQL_DIR/codeql-repo/javascript/ql/src/Security/CWE-078/CommandInjection.ql"
        "$CODEQL_DIR/codeql-repo/javascript/ql/src/Security/CWE-022/TaintedPath.ql"
        "$CODEQL_DIR/codeql-repo/javascript/ql/src/Security/CWE-094/CodeInjection.ql"
        "$CODEQL_DIR/codeql-repo/javascript/ql/src/Security/CWE-798/HardcodedCredentials.ql"
        "$CODEQL_DIR/codeql-repo/javascript/ql/src/Security/CWE-020/UntrustedDataToExternalAPI.ql"
    )
    
    # Run each query individually
    for query in "${SECURITY_QUERIES[@]}"; do
        if [ -f "$query" ]; then
            query_name=$(basename "$query" .ql)
            echo "Running $query_name..."
            "$CODEQL_CLI" database analyze \
                "$RESULTS_DIR/heartsync-db" \
                "$query" \
                --format=csv \
                --output="$RESULTS_DIR/${query_name}-results.csv" || true
        fi
    done
    
    # Custom queries
    if [ -d "$PROJECT_DIR/.github/codeql/custom-queries" ]; then
        echo "Running custom security queries..."
        for custom_query in "$PROJECT_DIR/.github/codeql/custom-queries"/*.ql; do
            if [ -f "$custom_query" ]; then
                query_name=$(basename "$custom_query" .ql)
                echo "Running custom query: $query_name..."
                "$CODEQL_CLI" database analyze \
                    "$RESULTS_DIR/heartsync-db" \
                    "$custom_query" \
                    --format=csv \
                    --output="$RESULTS_DIR/custom-${query_name}-results.csv" || true
            fi
        done
    fi
    
    echo -e "${GREEN}✅ Analysis completed${NC}"
}

# Function to display results
display_results() {
    echo -e "${YELLOW}Analysis Results:${NC}"
    echo "=================="
    
    # Count total issues across all result files
    TOTAL_ISSUES=0
    CRITICAL_ISSUES=0
    
    echo "Security query results:"
    for result_file in "$RESULTS_DIR"/*-results.csv; do
        if [ -f "$result_file" ]; then
            query_name=$(basename "$result_file" -results.csv)
            issue_count=$(tail -n +2 "$result_file" 2>/dev/null | wc -l | tr -d ' ' || echo "0")
            if [ "$issue_count" -gt 0 ]; then
                echo "  🔍 $query_name: $issue_count issues found"
                TOTAL_ISSUES=$((TOTAL_ISSUES + issue_count))
                
                # Show some details for critical queries
                if [[ "$query_name" == *"Xss"* ]] || [[ "$query_name" == *"SqlInjection"* ]] || [[ "$query_name" == *"CommandInjection"* ]]; then
                    CRITICAL_ISSUES=$((CRITICAL_ISSUES + issue_count))
                fi
            else
                echo "  ✅ $query_name: No issues found"
            fi
        fi
    done
    
    echo ""
    echo "Summary:"
    echo -e "🔴 Critical Issues: $CRITICAL_ISSUES"
    echo -e "📊 Total Issues: $TOTAL_ISSUES"
    echo ""
    
    echo "Results saved to:"
    for result_file in "$RESULTS_DIR"/*-results.csv; do
        if [ -f "$result_file" ]; then
            echo "  - $(basename "$result_file")"
        fi
    done
    
    if [ "$TOTAL_ISSUES" -gt 0 ]; then
        echo ""
        echo "⚠️  Review the CSV files in $RESULTS_DIR for detailed findings."
        echo "📖 For more information about each vulnerability type, see: https://codeql.github.com/"
    else
        echo ""
        echo "🎉 No security issues found! Your code looks good."
    fi
}

# Function to clean up
cleanup() {
    echo -e "${YELLOW}Cleaning up...${NC}"
    if [ -d "$RESULTS_DIR/heartsync-db" ]; then
        rm -rf "$RESULTS_DIR/heartsync-db"
    fi
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Main execution
main() {
    echo "Starting CodeQL security analysis for HeartSync v2..."
    echo ""
    
    # Check prerequisites
    if ! command_exists curl; then
        echo -e "${RED}❌ curl is required but not installed${NC}"
        exit 1
    fi
    
    if ! command_exists unzip; then
        echo -e "${RED}❌ unzip is required but not installed${NC}"
        exit 1
    fi
    
    if ! command_exists git; then
        echo -e "${RED}❌ git is required but not installed${NC}"
        exit 1
    fi
    
    # Setup CodeQL
    setup_codeql
    setup_codeql_library
    
    # Run analysis
    create_database
    run_analysis
    display_results
    
    # Optional cleanup
    read -p "Do you want to clean up the database? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup
    fi
    
    echo -e "${GREEN}🎉 CodeQL analysis completed successfully!${NC}"
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "Usage: $0 [--help|--cleanup]"
        echo "  --help     Show this help message"
        echo "  --cleanup  Clean up previous results and database"
        exit 0
        ;;
    "--cleanup")
        cleanup
        exit 0
        ;;
    *)
        main
        ;;
esac
