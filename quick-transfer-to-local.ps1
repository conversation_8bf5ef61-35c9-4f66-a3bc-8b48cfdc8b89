# 快速轉移到本機 /heartsync-v2 腳本

Write-Host "🚀 HeartSync v2 快速轉移腳本" -ForegroundColor Cyan
Write-Host "目標: 本機 /heartsync-v2" -ForegroundColor Yellow

# 檢查本機 heartsync-v2 目錄
$LocalPath = "C:\heartsync-v2"
if (-not (Test-Path $LocalPath)) {
    $LocalPath = "C:\Users\<USER>\heartsync-v2"
    if (-not (Test-Path $LocalPath)) {
        Write-Host "❌ 找不到本機 heartsync-v2 目錄" -ForegroundColor Red
        Write-Host "請手動指定路徑或創建目錄" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "✅ 找到目標目錄: $LocalPath" -ForegroundColor Green

# 核心檔案列表
$CoreFiles = @(
    "server.js",
    "package.json", 
    "package-lock.json",
    ".env.example",
    ".gitignore",
    "instant.perms.ts",
    "instant.schema.ts",
    "src/index.js",
    "src/routes/cli-preview.js",
    "src/routes/knowledge-preview.js", 
    "src/routes/instantdb.js",
    "src/routes/alpine-jazz-test.js",
    "src/routes/datastar-test.js"
)

Write-Host "`n📋 準備轉移的核心檔案:" -ForegroundColor Cyan
foreach ($file in $CoreFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (不存在)" -ForegroundColor Red
    }
}

Write-Host "`n🎯 建議的轉移步驟:" -ForegroundColor Cyan
Write-Host "1. cd `"$LocalPath`"" -ForegroundColor White
Write-Host "2. git status (檢查當前狀態)" -ForegroundColor White
Write-Host "3. git stash (如果有未提交變更)" -ForegroundColor White
Write-Host "4. 手動複製核心檔案或使用 Git 同步" -ForegroundColor White
Write-Host "5. npm install" -ForegroundColor White
Write-Host "6. 複製 .env.example 為 .env 並配置 API Keys" -ForegroundColor White
Write-Host "7. npm start" -ForegroundColor White
Write-Host "8. 測試路由: /database-test, /chat-alpine" -ForegroundColor White

Write-Host "`n🔍 路由測試清單:" -ForegroundColor Cyan
Write-Host "- http://localhost:3000/database-test" -ForegroundColor White
Write-Host "- http://localhost:3000/chat-alpine" -ForegroundColor White
Write-Host "- http://localhost:3000/CLI-preview" -ForegroundColor White
Write-Host "- http://localhost:3000/knowledge-preview" -ForegroundColor White
Write-Host "- http://localhost:3000/alpine-jazz-test" -ForegroundColor White

Write-Host "`n📞 如果遇到 404 錯誤:" -ForegroundColor Yellow
Write-Host "1. 檢查 src/index.js 中的路由註冊" -ForegroundColor White
Write-Host "2. 確認路由檔案存在於 src/routes/ 目錄" -ForegroundColor White
Write-Host "3. 檢查服務器控制台錯誤訊息" -ForegroundColor White
Write-Host "4. 對比兩個環境的檔案差異" -ForegroundColor White
