# HeartSync 備份與恢復指南

## 📦 本地備份系統

HeartSync 現在配備了完整的本地備份系統，讓你可以安全地清理 repository 同時保留重要文件的本地副本。

## 🗂️ 備份目錄結構

```
.local-backups/
├── documentation-reports/     # 文檔報告備份
├── maintenance-files/         # 維護文件備份
├── historical-backups/        # 歷史代碼備份
├── debug-output/             # 調試輸出備份
└── backup-manifest-*.json    # 備份清單文件
```

## 🛠️ 使用方法

### 創建備份
```bash
# 創建當前文件的備份
node scripts/backup-manager.js backup
```

### 查看可用備份
```bash
# 列出所有可用的備份
node scripts/backup-manager.js list
```

### 恢復文件
```bash
# 從特定時間戳恢復文件
node scripts/backup-manager.js restore 2025-07-05T20-30-00
```

### 帶備份的清理
```bash
# 清理前自動創建備份
node scripts/repository-cleanup.js --backup
# 或使用短參數
node scripts/repository-cleanup.js -b
```

## 📋 備份類別

### 1. 文檔報告 (documentation-reports)
- 各種項目報告文件
- 故障排除指南
- 部署文檔
- 傳輸指南

### 2. 維護文件 (maintenance-files)
- JSON 診斷文件
- 性能報告
- 維護報告
- API 診斷數據

### 3. 歷史備份 (historical-backups)
- 代碼的歷史版本
- 路由備份文件
- 組件備份
- 配置文件備份

### 4. 調試輸出 (debug-output)
- 調試截圖
- 測試報告
- 錯誤日誌
- 分析結果

## 🔒 安全特性

### 本地存儲
- 所有備份僅存儲在本地
- 不會同步到 Git repository
- `.gitignore` 自動排除備份目錄

### 時間戳管理
- 每個備份都有唯一的時間戳
- 支持多個備份版本並存
- 清單文件記錄備份詳情

### 目錄結構保持
- 保持原始文件的目錄結構
- `src/` 文件保持相對路徑
- 便於理解和恢復

## 📊 備份清單

每次備份都會生成一個清單文件，包含：
- 備份時間戳
- 備份文件數量
- 總大小
- 備份類別
- 備份說明

## 🔄 恢復流程

1. **查看可用備份**
   ```bash
   node scripts/backup-manager.js list
   ```

2. **選擇要恢復的時間戳**
   ```bash
   node scripts/backup-manager.js restore <timestamp>
   ```

3. **確認恢復結果**
   - 檢查文件是否正確恢復
   - 驗證目錄結構
   - 測試功能完整性

## ⚠️ 注意事項

### 備份前
- 確保有足夠的磁盤空間
- 檢查要備份的文件列表
- 考慮是否需要自定義備份類別

### 恢復前
- 確認當前工作已保存
- 檢查是否會覆蓋現有文件
- 建議先備份當前狀態

### 維護建議
- 定期清理舊備份
- 監控備份目錄大小
- 更新備份類別配置

## 🚀 高級用法

### 自定義備份類別
編輯 `scripts/backup-manager.js` 中的 `BACKUP_CATEGORIES` 來添加新的備份類別。

### 自動化備份
可以將備份命令集成到你的開發工作流程中：
```bash
# 在重要操作前自動備份
npm run backup && npm run cleanup
```

### 備份驗證
```bash
# 檢查備份完整性
ls -la .local-backups/
cat .local-backups/backup-manifest-*.json
```

## 📞 支援

如果遇到備份或恢復問題：
1. 檢查 `.local-backups/` 目錄權限
2. 確認磁盤空間充足
3. 查看備份清單文件
4. 檢查控制台錯誤信息

## 🔄 Git 歷史恢復功能

### 從 Git 歷史恢復文件
```bash
# 從 Git 歷史恢復所有已清理的文件
node scripts/git-recovery-backup.js recover

# 查看恢復的文件
node scripts/git-recovery-backup.js list
```

### 恢復管理器
```bash
# 查看所有可用的恢復備份
node scripts/recovery-manager.js list

# 查看特定恢復中的文件
node scripts/recovery-manager.js files 2025-07-05T12-46-06

# 恢復特定文件
node scripts/recovery-manager.js restore 2025-07-05T12-46-06 HONO_DEPENDENCY_FIX_REPORT.md

# 恢復整個類別
node scripts/recovery-manager.js category 2025-07-05T12-46-06 documentation-reports
```

## 📊 恢復統計

### 最新恢復 (2025-07-05T12-46-06)
- **來源提交**: f976503
- **恢復文件**: 29 個
- **總大小**: 1.33 MB
- **類別**: documentation-reports, maintenance-files, historical-backups

### 成功恢復的文件
- ✅ 15 個文檔報告文件
- ✅ 1 個維護文件
- ✅ 13 個歷史備份文件

---
*這個備份系統確保你可以安全地進行 repository 清理，同時保留所有重要文件的本地副本。通過 Git 歷史恢復功能，即使文件已被清理，也可以輕鬆恢復。*
