<!doctype html><html lang="en"><head><meta charset="utf-8"/><link href="./versions/VERSION_ID/favicon.ico" rel="shortcut icon"/><meta content="width=device-width,initial-scale=1" name="viewport"/><meta name="theme-color" media="(prefers-color-scheme: light)" content="white"><meta name="theme-color" media="(prefers-color-scheme: dark)" content="#24272c"><link href="./versions/VERSION_ID/manifest.json" rel="manifest"/><title>Qodana</title><script defer="defer" src="./versions/VERSION_ID/js/QD-vendors.js"></script><script defer="defer" src="./versions/VERSION_ID/js/QD-main.js"></script><link href="./versions/VERSION_ID/css/QD-vendors.css" rel="stylesheet"><link href="./versions/VERSION_ID/css/QD-main.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="root"></div><script>document.addEventListener("DOMContentLoaded",(()=>{QodanaUI.renderApp("root",{environment:"idea",isStagingContext:!1,paths:{prefix:"../api/qodana/resources/?file=",projectStructure:"../api/qodana/resources/?file="+encodeURIComponent("projectStructure/Code_Inspection.json"),uploadFiles:"../api/qodana/file/?path=",licenseAuditGetFile:function(e){return"../api/qodana/resources/?file="+encodeURIComponent("projectStructure/"+e)},getFile:function(e){return"../api/qodana/resources/?file="+encodeURIComponent(e)}}})}))</script></body></html>