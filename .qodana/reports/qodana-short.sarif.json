{"$schema": "https://raw.githubusercontent.com/schemastore/schemastore/master/src/schemas/json/sarif-2.1.0-rtm.5.json", "version": "2.1.0", "runs": [{"tool": {"driver": {"name": "QDJS", "fullName": "Qodana for JS", "version": "251.25781", "rules": [], "taxa": [], "language": "en-US", "contents": ["localizedData", "nonLocalizedData"], "isComprehensive": false}, "extensions": []}, "invocations": [{"startTimeUtc": "2025-08-15T15:26:10.664315603Z", "exitCode": 0, "executionSuccessful": true}], "language": "en-US", "versionControlProvenance": [{"repositoryUri": "https://github.com/QutritSystems/heartsync-v2.git", "revisionId": "d840c87511740d2340f76dddd85137ed80d95b3f", "branch": "main", "properties": {"repoUrl": "https://github.com/QutritSystems/heartsync-v2.git", "lastAuthorName": "Weiss-C", "vcsType": "Git", "lastAuthorEmail": "<EMAIL>"}}], "results": [], "automationDetails": {"id": "project/qodana/2025-08-15", "guid": "ae64d476-bbe4-4530-8230-ae8ab98226f2", "properties": {"jobUrl": "https://github.com/QutritSystems/heartsync-v2/actions/runs/16993255581", "analysisKind": "regular"}}, "newlineSequences": ["\r\n", "\n"], "properties": {"configProfile": "recommended", "deviceId": "200820300000000-9611-5095-4fc9-72f239af914e", "qodanaNewResultSummary": {"moderate": 38, "high": 1941, "total": 1979}}}]}