Directory;Absolute Time spent on Inspection Group 'all', s;Percent of Time spent on Inspection Group 'all';Inspections Problem Count in Group 'all';Times Inspection from Group 'all' was Performed
/data/project;123.37;100.00%;1983;22174
/data/project/playwright-report;44.45;36.03%;1821;127
/data/project/src;39.01;31.62%;72;5408
/data/project/src/routes;11.16;9.05%;7;2400
/data/project/src/components;1.62;1.32%;2;254
/data/project/src/services;0.67;0.54%;0;120
/data/project/src/database;0.46;0.38%;0;120
/data/project/src/config;0.01;0.01%;0;120
/data/project/src/templates;0.00;0.00%;0;452
/data/project/src/styles;0.00;0.00%;0;8
/data/project/scripts;8.96;7.26%;14;1746
/data/project/streamline-landing-page;8.89;7.21%;7;7765
/data/project/streamline-landing-page/components;7.96;6.45%;4;6460
/data/project/streamline-landing-page/components/ui;7.95;6.44%;4;6333
/data/project/streamline-landing-page/app;0.61;0.49%;1;509
/data/project/streamline-landing-page/app/components;0.48;0.39%;1;127
/data/project/streamline-landing-page/hooks;0.14;0.11%;2;240
/data/project/streamline-landing-page/lib;0.01;0.01%;0;120
/data/project/streamline-landing-page/public;0.01;0.00%;0;30
/data/project/streamline-landing-page/styles;0.00;0.00%;0;8
/data/project/static;2.23;1.81%;9;120
/data/project/api-tests;0.72;0.59%;0;418
/data/project/api-tests/reports;0.67;0.54%;0;240
/data/project/api-tests/scripts;0.00;0.00%;0;8
/data/project/api-tests/environments;0.00;0.00%;0;24
/data/project/api-tests/collections;0.00;0.00%;0;128
/data/project/api-tests/collections/openrouter-api;0.00;0.00%;0;8
/data/project/api-tests/collections/instantdb-api;0.00;0.00%;0;8
/data/project/api-tests/collections/finlight-api;0.00;0.00%;0;16
/data/project/api-tests/collections/tavily-api;0.00;0.00%;0;8
/data/project/api-tests/collections/hsn-health;0.00;0.00%;0;8
/data/project/api-tests/collections/cohere-api;0.00;0.00%;0;72
/data/project/api-tests/collections/claude-api;0.00;0.00%;0;8
/data/project/backups;0.43;0.35%;0;128
/data/project/backups/hsn-51-fix-20250706-010911;0.43;0.35%;0;120
/data/project/backups/hsn-51-20250705-235148;0.00;0.00%;0;8
/data/project/.taskmaster;0.17;0.14%;0;28
/data/project/.taskmaster/templates;0.00;0.00%;0;8
/data/project/.github;0.09;0.07%;0;56
/data/project/.github/workflows;0.08;0.06%;0;32
/data/project/.github/workflows/.github/workflows;0.05;0.04%;0;8
/data/project/.github/codeql;0.01;0.01%;0;24
/data/project/.github/codeql/custom-queries;0.00;0.00%;0;16
/data/project/Support/Claude;0.00;0.00%;0;10
/data/project/mem0_archive;0.00;0.00%;0;96
/data/project/mem0_archive/experimental_old;0.00;0.00%;0;88
/data/project/mem0_archive/database_backup/anthropic_mem0_db/a706b956-e99d-4825-bcc1-cd80ebe1cfd6;0.00;0.00%;0;8