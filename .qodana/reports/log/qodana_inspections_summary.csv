Inspection ID;Inspection Group;Inspection Name;Inspection Language;Inspection Type;Absolute Time spent on Inspection, s;Percent of Time spent on Inspection;Problems Count;Times Inspection was Performed
JSPotentiallyInvalidConstructorUsage;JavaScript and TypeScript/Probable bugs;Potentially invalid constructor usage;JavaScript;LOCAL;15.91;12.90%;25;157
JSAccessibilityCheck;JavaScript and TypeScript/General;Inaccessible @private and @protected members referenced;JavaScript;LOCAL;15.52;12.58%;0;157
JSUnusedAssignment;JavaScript and TypeScript/Unused symbols;Unused assignment;JavaScript;LOCAL;13.98;11.33%;10;157
JSDeprecatedSymbols;JavaScript and TypeScript/General;Deprecated symbol used;JavaScript;LOCAL;9.04;7.33%;16;157
JSIncompatibleTypesComparison;JavaScript and TypeScript/Probable bugs;Comparison of expressions having incompatible types;JavaScript;LOCAL;8.29;6.72%;0;157
TypeScriptUMDGlobal;JavaScript and TypeScript/TypeScript;Referenced UMD global variable;JavaScript;LOCAL;8.22;6.67%;0;157
ES6MissingAwait;JavaScript and TypeScript/Async code and promises;Missing await for an async function call;JavaScript;LOCAL;6.19;5.02%;1;157
JSUnusedLocalSymbols;JavaScript and TypeScript/Unused symbols;Unused local symbol;JavaScript;LOCAL;6.18;5.01%;25;157
KarmaConfigFile;JavaScript and TypeScript/Unit testing;Invalid Karma configuration file;JavaScript;LOCAL;5.28;4.28%;0;157
JSConstantReassignment;JavaScript and TypeScript/Validity issues;Attempt to assign to const or readonly variable;JavaScript;LOCAL;3.31;2.69%;0;157
UnnecessaryLocalVariableJS;JavaScript and TypeScript/Data flow;Redundant local variable;JavaScript;LOCAL;3.30;2.68%;7;157
JSObjectNullOrUndefined;JavaScript and TypeScript/Control flow issues;Object is 'null' or 'undefined';JavaScript;LOCAL;3.08;2.49%;1;157
JSPrimitiveTypeWrapperUsage;JavaScript and TypeScript/General;Primitive type object wrapper used;JavaScript;LOCAL;2.49;2.01%;1;157
InjectedReferences;General;Injected references;;LOCAL;2.27;1.84%;0;436
JSMismatchedCollectionQueryUpdate;JavaScript and TypeScript/General;Mismatched query and update of collection;JavaScript;LOCAL;2.12;1.72%;2;157
JSIgnoredPromiseFromCall;JavaScript and TypeScript/Async code and promises;Result of method call returning a promise is ignored;JavaScript;LOCAL;2.10;1.70%;11;157
HardcodedPasswords;Security;Hardcoded passwords;;LOCAL;1.98;1.61%;0;436
NpmVulnerableApiCode;JavaScript and TypeScript/Security;Vulnerable API usage;JavaScript;LOCAL;1.14;0.92%;0;151
JSVoidFunctionReturnValueUsed;JavaScript and TypeScript/Probable bugs;Void function return value used;JavaScript;LOCAL;0.96;0.78%;16;157
JSClosureCompilerSyntax;JavaScript and TypeScript/General;Incorrect usage of JSDoc tags;JavaScript;LOCAL;0.92;0.74%;0;157
JSSuspiciousNameCombination;JavaScript and TypeScript/Probable bugs;Suspicious variable/parameter name combination;JavaScript;LOCAL;0.86;0.70%;1;157
CheckDependencyLicenses;Dependency analysis;Check dependency licenses;;GLOBAL;0.81;0.66%;0;1
ES6RedundantAwait;JavaScript and TypeScript/Async code and promises;Redundant 'await' expression;JavaScript;LOCAL;0.76;0.62%;4;157
TypeScriptRedundantGenericType;JavaScript and TypeScript/TypeScript;Redundant type arguments;TypeScript;LOCAL;0.75;0.61%;0;157
PointlessBooleanExpressionJS;JavaScript and TypeScript/Control flow issues;Pointless statement or boolean expression;JavaScript;LOCAL;0.52;0.42%;2;157
ES6UnusedImports;JavaScript and TypeScript/Imports and dependencies;Unused import;JavaScript;LOCAL;0.50;0.40%;1;157
BadExpressionStatementJS;JavaScript and TypeScript/Validity issues;Expression statement which is not assignment or call;JavaScript;LOCAL;0.40;0.33%;2;157
TypeScriptUnresolvedReference;JavaScript and TypeScript/TypeScript;Unresolved TypeScript reference;TypeScript;LOCAL;0.34;0.27%;0;157
TypeScriptCheckImport;JavaScript and TypeScript/TypeScript;Unresolved imported name;TypeScript;LOCAL;0.31;0.25%;0;157
JSDuplicatedDeclaration;JavaScript and TypeScript/General;Duplicate declaration;JavaScript;LOCAL;0.27;0.22%;0;157
TypeScriptLibrary;JavaScript and TypeScript/TypeScript;Missing global library;TypeScript;LOCAL;0.27;0.22%;0;157
TypeScriptValidateTypes;JavaScript and TypeScript/TypeScript;Type mismatch;TypeScript;LOCAL;0.26;0.21%;0;157
JSPotentiallyInvalidUsageOfThis;JavaScript and TypeScript/Probable bugs;Potentially invalid reference to 'this' from closure;JavaScript;LOCAL;0.26;0.21%;0;157
AngularInaccessibleSymbol;Angular;Inaccessible component member or directive input;none;LOCAL;0.25;0.21%;0;436
DuplicatedCode;General;Duplicated code fragment;;LOCAL;0.24;0.20%;8;436
AngularUnsupportedSyntax;Angular;Unsupported Angular expression syntax;Angular2;LOCAL;0.23;0.19%;0;153
MongoJSSideEffects;MongoJS;Statement with side effects;MongoJSExt;LOCAL;0.22;0.18%;0;153
TypeScriptValidateGenericTypes;JavaScript and TypeScript/TypeScript;Incorrect generic type argument;TypeScript;LOCAL;0.22;0.17%;0;157
SuspiciousTypeOfGuard;JavaScript and TypeScript/Control flow issues;Unsound type guard check;JavaScript;LOCAL;0.21;0.17%;0;157
JSReferencingMutableVariableFromClosure;JavaScript and TypeScript/General;Referencing mutable variable from closure;JavaScript;LOCAL;0.21;0.17%;0;157
ReservedWordAsName;JavaScript and TypeScript/Validity issues;Reserved word used as name;JavaScript;LOCAL;0.18;0.15%;0;157
JSBitwiseOperatorUsage;JavaScript and TypeScript/Bitwise operation issues;Bitwise operator usage;JavaScript;LOCAL;0.18;0.15%;14;157
TypeScriptMissingConfigOption;JavaScript and TypeScript/TypeScript;Missing tsconfig.json option ;TypeScript;LOCAL;0.17;0.13%;0;157
JSPotentiallyInvalidTargetOfIndexedPropertyAccess;JavaScript and TypeScript/Probable bugs;Possibly incorrect target of indexed property access;JavaScript;LOCAL;0.16;0.13%;0;157
TypeScriptJSXUnresolvedComponent;JavaScript and TypeScript/TypeScript;Unresolved JSX component;TypeScript;LOCAL;0.16;0.13%;0;158
ES6PreferShortImport;JavaScript and TypeScript/General;Import can be shortened;JavaScript;LOCAL;0.13;0.11%;0;157
UnreachableCodeJS;JavaScript and TypeScript/Control flow issues;Unreachable code;JavaScript;LOCAL;0.12;0.10%;0;157
JSNonASCIINames;JavaScript and TypeScript/Naming conventions;Identifiers with non-ASCII symbols;JavaScript;LOCAL;0.10;0.08%;1;158
InfiniteRecursionJS;JavaScript and TypeScript/Probable bugs;Infinite recursion;JavaScript;LOCAL;0.09;0.07%;0;157
JSUnreachableSwitchBranches;JavaScript and TypeScript/Switch statement issues;Unreachable 'case' branch of a 'switch' statement;JavaScript;LOCAL;0.09;0.07%;0;157
JSUndefinedPropertyAssignment;JavaScript and TypeScript/Code style issues;Undefined property assignment;JavaScript;LOCAL;0.08;0.07%;0;157
JSSwitchVariableDeclarationIssue;JavaScript and TypeScript/Switch statement issues;Variable is declared and being used in different 'case' clauses;JavaScript;LOCAL;0.08;0.07%;70;157
JSTypeOfValues;JavaScript and TypeScript/Probable bugs;'typeof' comparison with non-standard value;JavaScript;LOCAL;0.08;0.06%;2;157
MongoJSResolve;MongoJS;Resolution problems;MongoJSExt;LOCAL;0.08;0.06%;0;153
TypeScriptAbstractClassConstructorCanBeMadeProtected;JavaScript and TypeScript/TypeScript;Abstract class constructor can be made protected;TypeScript;LOCAL;0.05;0.04%;0;157
FlowJSConfig;JavaScript and TypeScript/Flow type checker;Missing .flowconfig;Flow JS;LOCAL;0.05;0.04%;0;154
JSXDomNesting;JavaScript and TypeScript/React;Invalid DOM element nesting;JavaScript;LOCAL;0.05;0.04%;0;157
JSDuplicateCaseLabel;JavaScript and TypeScript/Switch statement issues;Duplicate 'case' label;JavaScript;LOCAL;0.04;0.03%;0;157
AngularIncorrectLetUsage;Angular;Incorrect usage of @let declaration;Angular2;LOCAL;0.04;0.03%;0;153
ES6PossiblyAsyncFunction;JavaScript and TypeScript/Async code and promises;'await' in non-async function;JavaScript;LOCAL;0.04;0.03%;0;157
CommaExpressionJS;JavaScript and TypeScript/Potentially undesirable code constructs;Comma expression;JavaScript;LOCAL;0.04;0.03%;1518;157
JSUndeclaredVariable;JavaScript and TypeScript/General;Implicitly declared global JavaScript variable;JavaScript;LOCAL;0.04;0.03%;0;157
InfiniteLoopJS;JavaScript and TypeScript/Probable bugs;Infinite loop statement;JavaScript;LOCAL;0.04;0.03%;0;157
JSXUnresolvedComponent;JavaScript and TypeScript/General;Unresolved JSX component;JavaScript;LOCAL;0.03;0.03%;0;157
PointlessArithmeticExpressionJS;JavaScript and TypeScript/Potentially confusing code constructs;Pointless arithmetic expression;JavaScript;LOCAL;0.03;0.03%;1;157
VulnerableLibrariesLocal;Security;Vulnerable declared dependency;;LOCAL;0.03;0.03%;0;436
IncompatibleBitwiseMaskOperation;JavaScript and TypeScript/Bitwise operation issues;Incompatible bitwise mask operation;JavaScript;LOCAL;0.03;0.02%;0;157
ConstantConditionalExpressionJS;JavaScript and TypeScript/Control flow issues;Constant conditional expression;JavaScript;LOCAL;0.03;0.02%;0;158
JSComparisonWithNaN;JavaScript and TypeScript/Probable bugs;Comparison with NaN;JavaScript;LOCAL;0.03;0.02%;0;157
LoopStatementThatDoesntLoopJS;JavaScript and TypeScript/Control flow issues;Loop statement that doesn't loop;JavaScript;LOCAL;0.03;0.02%;0;158
SillyAssignmentJS;JavaScript and TypeScript/Assignment issues;Variable is assigned to itself;JavaScript;LOCAL;0.03;0.02%;0;157
JSJQueryEfficiency;JavaScript and TypeScript/General;JQuery selector can be optimized;JavaScript;LOCAL;0.03;0.02%;0;157
JSLastCommaInArrayLiteral;JavaScript and TypeScript/General;Unneeded last comma in array literal;JavaScript;LOCAL;0.03;0.02%;0;157
ShiftOutOfRangeJS;JavaScript and TypeScript/Bitwise operation issues;Shift operation by possibly wrong constant;JavaScript;LOCAL;0.03;0.02%;0;157
UnnecessaryReturnStatementJS;JavaScript and TypeScript/Control flow issues;Unnecessary 'return' statement;JavaScript;LOCAL;0.02;0.02%;0;157
JSUnresolvedExtXType;JavaScript and TypeScript/General;Unresolved Ext JS xtype;JavaScript;LOCAL;0.02;0.02%;0;157
MaliciousLibrariesLocal;Security;Malicious dependency;;LOCAL;0.02;0.02%;0;436
FallThroughInSwitchStatementJS;JavaScript and TypeScript/Switch statement issues;Fallthrough in 'switch' statement;JavaScript;LOCAL;0.02;0.02%;20;157
JSValidateJSDoc;JavaScript and TypeScript/General;Syntax errors and unresolved references in JSDoc;JavaScript;LOCAL;0.02;0.02%;0;158
WithStatementJS;JavaScript and TypeScript/Potentially undesirable code constructs;'with' statement;JavaScript;LOCAL;0.02;0.02%;0;157
UnnecessaryContinueJS;JavaScript and TypeScript/Control flow issues;Unnecessary 'continue' statement;JavaScript;LOCAL;0.02;0.02%;0;157
CallerJS;JavaScript and TypeScript/Potentially confusing code constructs;Use of 'caller' property;JavaScript;LOCAL;0.02;0.02%;0;157
ES6RedundantNestingInTemplateLiteral;JavaScript and TypeScript/General;Redundant nesting in template literal;JavaScript;LOCAL;0.02;0.02%;0;157
RedundantIfStatementJS;JavaScript and TypeScript/Control flow issues;Redundant 'if' statement;JavaScript;LOCAL;0.02;0.02%;0;157
JSSuspiciousEqPlus;JavaScript and TypeScript/Probable bugs;Suspicious '=+' assignment;JavaScript;LOCAL;0.02;0.02%;0;157
RedundantConditionalExpressionJS;JavaScript and TypeScript/Control flow issues;Redundant conditional expression;JavaScript;LOCAL;0.02;0.02%;0;157
StatementWithEmptyBodyJS;JavaScript and TypeScript/Potentially confusing code constructs;Statement with empty body;JavaScript;LOCAL;0.02;0.02%;5;157
UnnecessaryLabelOnBreakStatementJS;JavaScript and TypeScript/Control flow issues;Unnecessary label on 'break' statement;JavaScript;LOCAL;0.02;0.02%;8;157
ThisExpressionReferencesGlobalObjectJS;JavaScript and TypeScript/Validity issues;'this' expression which references the global object;JavaScript;LOCAL;0.02;0.02%;0;157
UnnecessaryLabelOnContinueStatementJS;JavaScript and TypeScript/Control flow issues;Unnecessary label on 'continue' statement;JavaScript;LOCAL;0.02;0.02%;0;157
EqualityComparisonWithCoercionJS;JavaScript and TypeScript/Probable bugs;Equality operator may cause type coercion;JavaScript;LOCAL;0.02;0.02%;133;157
ExceptionCaughtLocallyJS;JavaScript and TypeScript/Try statement issues;Exception used for local control-flow;JavaScript;LOCAL;0.02;0.02%;62;157
ContinueOrBreakFromFinallyBlockJS;JavaScript and TypeScript/Try statement issues;'continue' or 'break' inside 'finally' block;JavaScript;LOCAL;0.02;0.02%;0;157
ES6DestructuringVariablesMerge;JavaScript and TypeScript/General;Destructuring properties with the same key;JavaScript;LOCAL;0.02;0.02%;0;158
JSConsecutiveCommasInArrayLiteral;JavaScript and TypeScript/Probable bugs;Consecutive commas in array literal;JavaScript;LOCAL;0.02;0.02%;0;157
JSOctalInteger;JavaScript and TypeScript/Validity issues;Octal integer;JavaScript;LOCAL;0.02;0.02%;0;157
ReturnInsideFinallyBlockJS;JavaScript and TypeScript/Try statement issues;'return' inside 'finally' block;JavaScript;LOCAL;0.02;0.02%;0;157
JSAssignmentUsedAsCondition;JavaScript and TypeScript/Assignment issues;Assignment used as condition;JavaScript;LOCAL;0.02;0.02%;5;157
TypeScriptSuspiciousConstructorParameterAssignment;JavaScript and TypeScript/TypeScript;Assigned constructor field parameter;TypeScript;LOCAL;0.02;0.01%;0;157
CustomRegExpInspection;RegExp;Custom RegExp inspection;;LOCAL;0.02;0.01%;0;436
JSLastCommaInObjectLiteral;JavaScript and TypeScript/General;Unneeded last comma in object literal;JavaScript;LOCAL;0.02;0.01%;3;157
ES6ClassMemberInitializationOrder;JavaScript and TypeScript/General;Use of possibly unassigned property in a static initializer;ECMAScript 6;LOCAL;0.02;0.01%;0;153
FlowJSFlagCommentPlacement;JavaScript and TypeScript/Flow type checker;Misplaced @flow flag;Flow JS;LOCAL;0.02;0.01%;0;153
JSPotentiallyInvalidUsageOfClassThis;JavaScript and TypeScript/Probable bugs;Potentially invalid reference to 'this' of a class from closure;JavaScript;LOCAL;0.02;0.01%;0;158
VueDataFunction;Vue;Data function;Vue;LOCAL;0.01;0.01%;0;60
AngularUnresolvedPipe;Angular;Unresolved pipe;HtmlCompatible;LOCAL;0.01;0.01%;0;83
TypeScriptFieldCanBeMadeReadonly;JavaScript and TypeScript/TypeScript;Field can be readonly;TypeScript;LOCAL;0.01;0.01%;0;158
ES6BindWithArrowFunction;JavaScript and TypeScript/Probable bugs;Suspicious usage of 'bind' with arrow function;JavaScript;LOCAL;0.01;0.01%;0;157
ThrowInsideFinallyBlockJS;JavaScript and TypeScript/Try statement issues;'throw' inside 'finally' block;JavaScript;LOCAL;0.01;0.01%;0;157
AngularIllegalForLoopTrackAccess;Angular;Illegal @for loop access;HtmlCompatible;LOCAL;0.01;0.01%;0;83
UnnecessaryLabelJS;JavaScript and TypeScript/Control flow issues;Unnecessary label;JavaScript;LOCAL;0.01;0.01%;1;157
JSCommentMatchesSignature;JavaScript and TypeScript/General;Mismatched JSDoc and function signature;JavaScript;LOCAL;0.01;0.01%;0;157
VueDuplicateTag;Vue;Duplicate template/script tag;Vue;LOCAL;0.01;0.01%;0;60
JSUnnecessarySemicolon;JavaScript and TypeScript/General;Unnecessary semicolon;JavaScript;LOCAL;0.01;0.01%;0;157
RegExpRedundantEscape;RegExp;Redundant character escape;RegExp;LOCAL;0.01;0.01%;4;62
AngularIncorrectBlockUsage;Angular;Incorrect usage of Angular block;Angular2Html;LOCAL;0.01;0.01%;0;61
TypeScriptConfig;JavaScript and TypeScript/TypeScript;Inconsistent tsconfig.json properties;JSON;LOCAL;0.01;0.00%;0;41
TypeScriptDuplicateUnionOrIntersectionType;JavaScript and TypeScript/TypeScript;Duplicate union or intersection type component;TypeScript;LOCAL;0.00;0.00%;0;157
AngularInvalidSelector;Angular;Missing or invalid selector;none;LOCAL;0.00;0.00%;0;6
AngularMissingOrInvalidDeclarationInModule;Angular;Missing or invalid component, directive or pipe declaration in a module;TypeScript;LOCAL;0.00;0.00%;0;157
AngularIncorrectTemplateDefinition;Angular;Incorrect component template definition;TypeScript;LOCAL;0.00;0.00%;0;157
AngularUnusedComponentImport;Angular;Unused import in an Angular component declaration;TypeScript;LOCAL;0.00;0.00%;0;157
JsCoverageInspection;Code Coverage;Check JavaScript and TypeScript source code coverage;JavaScript;GLOBAL_SIMPLE;0.00;0.00%;0;262
RegExpRedundantClassElement;RegExp;Redundant '\d', '[:digit:]', or '\D' class elements;RegExp;LOCAL;0.00;0.00%;0;62
RegExpDuplicateAlternationBranch;RegExp;Duplicate branch in alternation;RegExp;LOCAL;0.00;0.00%;0;62
AngularRecursiveModuleImportExport;Angular;Recursive import or export of an Angular module or a standalone component;TypeScript;LOCAL;0.00;0.00%;0;157
AngularUndefinedModuleExport;Angular;Undefined export from Angular module;TypeScript;LOCAL;0.00;0.00%;0;158
RegExpUnexpectedAnchor;RegExp;Begin or end anchor in unexpected position;RegExp;LOCAL;0.00;0.00%;0;62
AngularForBlockNonIterableVar;Angular;Non-iterable type in @for block;Angular2Html;LOCAL;0.00;0.00%;0;60
AngularInvalidImportedOrDeclaredSymbol;Angular;Invalid imported or declared symbol;TypeScript;LOCAL;0.00;0.00%;0;157
RegExpUnnecessaryNonCapturingGroup;RegExp;Unnecessary non-capturing group;RegExp;LOCAL;0.00;0.00%;2;62
AngularCliAddDependency;Angular;Angular CLI add dependency;JSON;LOCAL;0.00;0.00%;0;41
AngularInvalidEntryComponent;Angular;Invalid entry component;TypeScript;LOCAL;0.00;0.00%;0;157
RegExpSimplifiable;RegExp;Regular expression can be simplified;RegExp;LOCAL;0.00;0.00%;0;62
RegExpRepeatedSpace;RegExp;Consecutive spaces;RegExp;LOCAL;0.00;0.00%;0;62
RegExpSingleCharAlternation;RegExp;Single character alternation;RegExp;LOCAL;0.00;0.00%;1;62
RegExpRedundantNestedCharacterClass;RegExp;Redundant nested character class;RegExp;LOCAL;0.00;0.00%;0;62
RegExpDuplicateCharacterInClass;RegExp;Duplicate character in character class;RegExp;LOCAL;0.00;0.00%;0;62
AngularDeferBlockOnTrigger;Angular;Problems with @defer `on` triggers;Angular2Html;LOCAL;0.00;0.00%;0;60
RegExpEmptyAlternationBranch;RegExp;Empty branch in alternation;RegExp;LOCAL;0.00;0.00%;0;62
RegExpSuspiciousBackref;RegExp;Suspicious back reference;RegExp;LOCAL;0.00;0.00%;0;62
AngularNonStandaloneComponentImports;Angular;Invalid usage of imports in non-standalone components;TypeScript;LOCAL;0.00;0.00%;0;157
NodeCoreCodingAssistance;JavaScript and TypeScript/Node.js;Unresolved Node.js APIs;JavaScript;LOCAL;0.00;0.00%;0;157