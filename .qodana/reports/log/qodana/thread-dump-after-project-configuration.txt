"AWT-EventQueue-0" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@793923a4
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4013)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3961)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.desktop/java.awt.EventQueue.getNextEvent(EventQueue.java:573)
	at com.intellij.ide.IdeEventQueue.getNextEvent(IdeEventQueue.kt:373)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:194)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)

"Netty Builtin Server 1" prio=0 tid=0x0 nid=0x0 runnable
     java.lang.Thread.State: RUNNABLE
 (in native)
	at java.base@21.0.7/sun.nio.ch.EPoll.wait(Native Method)
	at java.base@21.0.7/sun.nio.ch.EPollSelectorImpl.doSelect(EPollSelectorImpl.java:121)
	at java.base@21.0.7/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
	at java.base@21.0.7/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:62)
	at io.netty.channel.nio.NioIoHandler.select(NioIoHandler.java:646)
	at io.netty.channel.nio.NioIoHandler.run(NioIoHandler.java:432)
	at io.netty.channel.SingleThreadIoEventLoop.runIo(SingleThreadIoEventLoop.java:203)
	at io.netty.channel.SingleThreadIoEventLoop.run(SingleThreadIoEventLoop.java:174)
	at io.netty.util.concurrent.SingleThreadEventExecutor$5.run(SingleThreadEventExecutor.java:1123)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"DefaultDispatcher-worker-48" prio=0 tid=0x0 nid=0x0 runnable
     java.lang.Thread.State: RUNNABLE

	at java.management@21.0.7/sun.management.ThreadImpl.dumpThreads0(Native Method)
	at java.management@21.0.7/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:518)
	at java.management@21.0.7/sun.management.ThreadImpl.dumpAllThreads(ThreadImpl.java:506)
	at com.intellij.diagnostic.ThreadDumper.getThreadInfos(ThreadDumper.java:72)
	at com.intellij.diagnostic.ThreadDumper.getThreadInfos(ThreadDumper.java:43)
	at org.jetbrains.qodana.staticAnalysis.inspections.runner.startup.QodanaProjectLoader.dumpThreadsAfterConfiguration(QodanaProjectLoader.kt:186)
	at org.jetbrains.qodana.staticAnalysis.inspections.runner.startup.QodanaProjectLoader$doConfigure$2.invokeSuspend(QodanaProjectLoader.kt:130)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:608)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:873)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:763)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:750)

"External Command Listener" prio=0 tid=0x0 nid=0x0 runnable
     java.lang.Thread.State: RUNNABLE
 (in native)
	at java.base@21.0.7/sun.nio.ch.UnixDomainSockets.accept0(Native Method)
	at java.base@21.0.7/sun.nio.ch.UnixDomainSockets.accept(UnixDomainSockets.java:173)
	at java.base@21.0.7/sun.nio.ch.ServerSocketChannelImpl.implAccept(ServerSocketChannelImpl.java:427)
	at java.base@21.0.7/sun.nio.ch.ServerSocketChannelImpl.accept(ServerSocketChannelImpl.java:399)
	at com.intellij.platform.ide.bootstrap.DirectoryLock.acceptConnections(DirectoryLock.java:362)
	at com.intellij.platform.ide.bootstrap.DirectoryLock$$Lambda/0x00007f37cd096878.run(Unknown Source)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"HttpClient-2-SelectorManager" prio=0 tid=0x0 nid=0x0 runnable
     java.lang.Thread.State: RUNNABLE
 (in native)
	at java.base@21.0.7/sun.nio.ch.EPoll.wait(Native Method)
	at java.base@21.0.7/sun.nio.ch.EPollSelectorImpl.doSelect(EPollSelectorImpl.java:121)
	at java.base@21.0.7/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
	at java.base@21.0.7/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
	at platform/java.net.http@21.0.7/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1369)

"Reference Handler" prio=0 tid=0x0 nid=0x0 runnable
     java.lang.Thread.State: RUNNABLE

	at java.base@21.0.7/java.lang.ref.Reference.waitForReferencePendingList(Native Method)
	at java.base@21.0.7/java.lang.ref.Reference.processPendingReferences(Reference.java:246)
	at java.base@21.0.7/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:208)

"Notification Thread" prio=0 tid=0x0 nid=0x0 runnable
     java.lang.Thread.State: RUNNABLE


"Signal Dispatcher" prio=0 tid=0x0 nid=0x0 runnable
     java.lang.Thread.State: RUNNABLE


"ApplicationImpl pooled thread 1" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@1ea3610
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"ApplicationImpl pooled thread 2" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@1ea3610
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"ApplicationImpl pooled thread 3" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@1ea3610
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"ApplicationImpl pooled thread 4" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@1ea3610
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"ApplicationImpl pooled thread 5" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@1ea3610
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"I/O pool 1" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@ef72b57
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"I/O pool 2" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@ef72b57
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"I/O pool 3" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@ef72b57
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"I/O pool 4" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@ef72b57
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"I/O pool 5" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@ef72b57
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"I/O pool 6" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@ef72b57
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:735)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1$1.run(Executors.java:732)
	at java.base@21.0.7/java.security.AccessController.executePrivileged(AccessController.java:778)
	at java.base@21.0.7/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.base@21.0.7/java.util.concurrent.Executors$PrivilegedThreadFactory$1.run(Executors.java:732)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"Coroutines Debugger Cleaner" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@f6becb8
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4013)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3961)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:67)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:158)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:234)
	at kotlinx.coroutines.debug.internal.ConcurrentWeakMap.runWeakRefQueueCleaningLoopUntilInterrupted(ConcurrentWeakMap.kt:69)
	at kotlinx.coroutines.debug.internal.DebugProbesImpl$startWeakRefCleanerThread$1.invoke(DebugProbesImpl.kt:92)
	at kotlinx.coroutines.debug.internal.DebugProbesImpl$startWeakRefCleanerThread$1.invoke(DebugProbesImpl.kt:91)
	at kotlin.concurrent.ThreadsKt$thread$thread$1.run(Thread.kt:30)

"Aux Index Writer #1" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@2a43eb41
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4013)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3961)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"DirectBufferWrapper allocation thread" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@5def39e0
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4013)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3961)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"IdIndex Writer #0" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@5d5afb1e
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4013)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3961)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"Java2D Disposer" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@5abca1a8
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4013)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3961)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:67)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:158)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:234)
	at java.desktop/sun.java2d.Disposer.run(Disposer.java:145)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"Kernel event loop thread 3346pk9b9tohudv85lc1" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@145ae63a
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4013)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3961)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"Stubs Writer #2" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@739d2e45
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4013)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3961)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"Trigram.Index Writer #1" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@4c93679e
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4013)
	at java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3961)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
	at java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"process reaper" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@5e7dd62f
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)
	at java.base@21.0.7/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)

"process reaper" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@5e7dd62f
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)
	at java.base@21.0.7/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)

"HttpClient-2-Worker-0" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@2b6ff48c
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"HttpClient-2-Worker-1" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.SynchronousQueue$Transferer@2b6ff48c
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410)
	at java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235)
	at java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"Common-Cleaner" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@4bbcec5c
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218)
	at java.base@21.0.7/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)
	at java.base@21.0.7/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)

"PeriodicMetricReader" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@24254550
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
	at java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
	at java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"main" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on kotlinx.coroutines.BlockingCoroutine@59628801
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:121)
	at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$BuildersKt__BuildersKt(Builders.kt:85)
	at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:53)
	at kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:49)
	at kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at com.intellij.idea.Main.mainImpl(Main.kt:72)
	at com.intellij.idea.Main.main(Main.kt:47)

"Finalizer" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.lang.ref.NativeReferenceQueue$Lock@2bed57a3
	at java.base@21.0.7/java.lang.Object.wait0(Native Method)
	at java.base@21.0.7/java.lang.Object.wait(Object.java:366)
	at java.base@21.0.7/java.lang.Object.wait(Object.java:339)
	at java.base@21.0.7/java.lang.ref.NativeReferenceQueue.await(NativeReferenceQueue.java:48)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:158)
	at java.base@21.0.7/java.lang.ref.NativeReferenceQueue.remove(NativeReferenceQueue.java:89)
	at java.base@21.0.7/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:173)

"JNA Cleaner" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@e23ff74
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143)
	at java.base@21.0.7/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218)
	at com.sun.jna.internal.Cleaner$CleanerThread.run(Cleaner.java:154)

"TimerQueue" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@6d26943b
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
	at java.base@21.0.7/java.util.concurrent.DelayQueue.take(DelayQueue.java:255)
	at java.desktop/javax.swing.TimerQueue.run(TimerQueue.java:165)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"AWT-Shutdown" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: WAITING
 on java.lang.Object@34d687bf
	at java.base@21.0.7/java.lang.Object.wait0(Native Method)
	at java.base@21.0.7/java.lang.Object.wait(Object.java:366)
	at java.base@21.0.7/java.lang.Object.wait(Object.java:339)
	at java.desktop/sun.awt.AWTAutoShutdown.run(AWTAutoShutdown.java:291)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"Periodic tasks thread" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject@1e476f51
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
	at java.base@21.0.7/java.util.concurrent.DelayQueue.take(DelayQueue.java:255)
	at com.intellij.util.concurrency.AppDelayQueue$TransferThread.run(AppDelayQueue.java:83)

"kotlinx.coroutines.DefaultExecutor" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on kotlinx.coroutines.DefaultExecutor@14317071
	at java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method)
	at java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
	at kotlinx.coroutines.DefaultExecutor.run(DefaultExecutor.kt:118)
	at java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596)
	at java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)

"Timer-0" prio=0 tid=0x0 nid=0x0 waiting on condition
     java.lang.Thread.State: TIMED_WAITING
 on java.util.TaskQueue@3828236f
	at java.base@21.0.7/java.lang.Object.wait0(Native Method)
	at java.base@21.0.7/java.lang.Object.wait(Object.java:366)
	at java.base@21.0.7/java.util.TimerThread.mainLoop(Timer.java:563)
	at java.base@21.0.7/java.util.TimerThread.run(Timer.java:516)


---------- Coroutine dump ----------

- BlockingCoroutine{Active}@59628801 [BlockingEventLoop]
	- "startApplication":StandaloneCoroutine{Active} [StartupAbortedExceptionHandler, startApplication, kotlinx.coroutines.UndispatchedMarker, Dispatchers.Default]
		- "QodanaApplicationStarter.start":StandaloneCoroutine{Active}, state: SUSPENDED [StartupAbortedExceptionHandler, QodanaApplicationStarter.start, kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), io.opentelemetry.extension.kotlin.KotlinContextElement, Dispatchers.IO]
			at org.jetbrains.qodana.staticAnalysis.inspections.runner.Utils_time_loggerKt$runTaskAndLogTime$2$loggingJob$1.invokeSuspend(utils-time-logger.kt:41)
			- "QodanaApplicationStarter.start":StandaloneCoroutine{Active}, state: SUSPENDED [StartupAbortedExceptionHandler, QodanaApplicationStarter.start, kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), io.opentelemetry.extension.kotlin.KotlinContextElement, Dispatchers.IO]
				at org.jetbrains.qodana.staticAnalysis.inspections.runner.Utils_time_loggerKt$runTaskAndLogTime$2$loggingJob$1$1.invokeSuspend(utils-time-logger.kt:37)
			- "QodanaApplicationStarter.start":StandaloneCoroutine{Active}, state: SUSPENDED [StartupAbortedExceptionHandler, QodanaApplicationStarter.start, kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), io.opentelemetry.extension.kotlin.KotlinContextElement, Dispatchers.IO]
				at org.jetbrains.qodana.staticAnalysis.inspections.runner.log.QodanaThreadDumpLoggingActivity.executeActivity(QodanaThreadDumpLoggingActivity.kt:22)
				at org.jetbrains.qodana.staticAnalysis.inspections.runner.Utils_time_loggerKt$runTaskAndLogTime$2$loggingJob$1$2$1$1.invokeSuspend(utils-time-logger.kt:44)
		- "QodanaApplicationStarter.start":StandaloneCoroutine{Completing} [StartupAbortedExceptionHandler, QodanaApplicationStarter.start, kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), io.opentelemetry.extension.kotlin.KotlinContextElement, Dispatchers.Default]
			- "QodanaApplicationStarter.start":StandaloneCoroutine{Active}, state: SUSPENDED [StartupAbortedExceptionHandler, QodanaApplicationStarter.start, kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), io.opentelemetry.extension.kotlin.KotlinContextElement, Dispatchers.Default]
				at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
				at org.jetbrains.qodana.staticAnalysis.inspections.runner.startup.QodanaProjectLoader$installLogger$1$1.invokeSuspend(QodanaProjectLoader.kt:148)
			- "QodanaApplicationStarter.start":StandaloneCoroutine{Active}, state: SUSPENDED [StartupAbortedExceptionHandler, QodanaApplicationStarter.start, kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), io.opentelemetry.extension.kotlin.KotlinContextElement, Dispatchers.Default]
				at org.jetbrains.qodana.staticAnalysis.inspections.runner.startup.QodanaProjectLoader$installLogger$1$2.invokeSuspend(QodanaProjectLoader.kt:162)
	- StandaloneCoroutine{Active} [BlockingEventLoop]
		- "Changes processing job for Kernel@3346pk9b9tohudv85lc1":StandaloneCoroutine{Active} [ThreadLocal(value=fleet.tracing.runtime.Span$Noop@74108d26, threadLocal = java.lang.ThreadLocal@6d18a5bd), kotlinx.coroutines.UndispatchedMarker, java.util.concurrent.Executors$AutoShutdownDelegatedExecutorService]
		- "withKernel":StandaloneCoroutine{Active} [kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ThreadLocal(value=fleet.tracing.runtime.Span$Noop@74108d26, threadLocal = java.lang.ThreadLocal@6d18a5bd), Dispatchers.Unconfined]
		- "withKernel":StandaloneCoroutine{Active} [kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ThreadLocal(value=fleet.tracing.runtime.Span$Noop@74108d26, threadLocal = java.lang.ThreadLocal@6d18a5bd), BlockingEventLoop]
			- "withKernel":ProducerCoroutine{Active} [kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ThreadLocal(value=fleet.tracing.runtime.Span$Noop@74108d26, threadLocal = java.lang.ThreadLocal@6d18a5bd), BlockingEventLoop]
				- "withKernel":ProducerCoroutine{Active} [kotlinx.coroutines.UndispatchedMarker, Kernel@3346pk9b9tohudv85lc1, DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ThreadLocal(value=fleet.tracing.runtime.Span$Noop@74108d26, threadLocal = java.lang.ThreadLocal@6d18a5bd), BlockingEventLoop]
	- "Application":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1)]
		- "ApplicationImpl@942166693 container":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x org.jetbrains.plugins.textmate)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "org.jetbrains.plugins.textmate.TextMateServiceImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij.platform.images)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij.css)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij.modules.json)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x org.jetbrains.plugins.yaml)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x org.intellij.intelliLang)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			-[x12 of] "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.impl.VcsInitialization$StartUpActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.changes.shelf.ShelveChangeManagerProjectActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.update.RestoreUpdateTree$RestoreUpdateTreeStartUpActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.codeInsight.ChangesToolwindowPassCache":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.vfs.AsyncVfsEventsPostProcessorImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.vfs.AsyncVfsEventsPostProcessorImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
						at com.intellij.vfs.AsyncVfsEventsPostProcessorImpl$1.invokeSuspend(AsyncVfsEventsPostProcessorImpl.kt:31)
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.vcs.log.impl.VcsProjectLog$InitLogStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.history.integration.LocalHistoryImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.datagrid.DataGridStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.platform.rpc.backend.impl.RemoteApiRegistry":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.platform.kernel.backend.BackendKernelService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.kernel.backend.BackendKernelService":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
						at fleet.kernel.TransactorKt$withTransactor$2$transactor$1$log$1.invokeSuspend(Transactor.kt:598)
						at kotlinx.coroutines.flow.AbstractFlow.collect(Flow.kt:226)
						at com.intellij.platform.kernel.util.KernelUtilsKt$updateDbInTheEventDispatchThread$2.invokeSuspend(kernelUtils.kt:95)
						at com.intellij.platform.kernel.util.KernelUtilsKt.updateDbInTheEventDispatchThread(kernelUtils.kt:93)
						at com.intellij.platform.kernel.backend.BackendKernelService$1.invokeSuspend(BackendKernelService.kt:61)
						- "KernelCoroutineScope":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							- "RemoteKernelScope":supervisor:ChildScope{Active} [ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), Dispatchers.Default]
			- "(ApplicationImpl@942166693 x hg4idea)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.jetbrains.plugins.webDeployment)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.jetbrains.plugins.webDeployment))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.jetbrains.plugins.webDeployment.DeploymentPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.jetbrains.restClient)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x intellij.charts)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x tanvd.grazi)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.grazie.spellcheck.GrazieCheckers":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "ConfigurationChanged":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, LimitedDispatcher]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x tanvd.grazi))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.grazie.ide.notification.GrazieNotificationComponent":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x Git4Idea)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x Git4Idea))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.repo.GitCommitTemplateTracker$GitCommitTemplateTrackerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.GitMergeCommitMessageActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.ignore.GitIgnoreInStoreDirGeneratorActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.config.GitSetupProjectConfig":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.repo.GitShallowRepositoryCheck":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.GitModalCommitDeprecationNotifier":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.index.GitStageStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.signing.GpgAgentConfiguratorStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x JavaScript)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x JavaScript))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.interpreter.NodeJsInterpreterManagerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.webSymbols.nodejs.PackageJsonWebSymbolsRegistryManagerStartup":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.webSymbols.nodejs.WebTypesNpmLoader$TriggerUpdateIfNeededActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.lang.javascript.linter.PackageJsonForLinterConfigChangeTracker":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.library.node_modules.NodeModulesDirectoryStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.library.yarn.pnp.YarnPnpStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.library.exclude.JsExcludeStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.library.core.NodeCoreLibraryStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.packageJson.workspaceModel.PackageJsonEntityStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.lang.javascript.library.typings.TypeScriptInstallExternalDefinitionsStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.packageJson.notification.PackageJsonUpdateNotifier$MyStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.javascript.webSymbols.nodejs.WebTypesNpmLoader":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.lang.typescript.compiler.PrioritySuspendingTaskQueueFactory":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.javascript.testing.detection.JsTestFrameworkDetectors":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x JavaScriptDebugger)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x JavaScriptDebugger))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.debugger.activity.NextJsCreateClientRunConfigurationActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.debugger.activity.NextJsCreateServerRunConfigurationActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x org.intellij.qodana)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "org.jetbrains.qodana.cloud.api.IjQDCloudClientProviderImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x org.intellij.qodana))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.staticAnalysis.QodanaAwaitBackgroundStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.QodanaStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.staticAnalysis.QodanaLinterBackgroundStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij.configurationScript)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x intellij.indexing.shared.core)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x intellij.indexing.shared.core))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.download.IndexDownloadServiceProjectStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.platform.impl.BundledSharedIndexPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij.database)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij.database))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.editor.DatabaseStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.autoconfig.DatabaseConfigStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.database.dataSource.srcStorage.DbSrcChangesTrackerApplication":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.dataSource.srcStorage.DbSrcChangesTrackerApplication":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at com.intellij.database.dataSource.srcStorage.DbSrcChangesTrackerApplication$launchUpdater$1.invokeSuspend(DbSrcChangesTrackerApplication.kt:77)
						- "com.intellij.database.dataSource.srcStorage.DbSrcChangesTrackerApplication":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
							at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
							at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
							at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
							- "com.intellij.database.dataSource.srcStorage.DbSrcChangesTrackerApplication":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
								at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
				- "com.intellij.database.dataSource.DataSourceModelStorageImpl$App":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.dataSource.DataSourceModelStorageImpl$App":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at com.intellij.util.SingleAlarm$scheduleTask$1$1.invokeSuspend(SingleAlarm.kt:410)
			- "(ApplicationImpl@942166693 x NodeJS)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.openapi.client.ClientAppSessionsManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "ApplicationImpl@942166693 container":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "ApplicationImpl@942166693 container":StandaloneCoroutine{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
			-[x2 of] "ApplicationImpl@942166693 container":StandaloneCoroutine{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
			- "com.intellij.configurationStore.statistic.eventLog.FeatureUsageSettingsEvents":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.configurationStore.statistic.eventLog.FeatureUsageSettingsEvents":StandaloneCoroutine{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
			- "com.intellij.diagnostic.WriteLockMeasurerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.openapi.vfs.newvfs.persistent.ExecuteOnCoroutine":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "PersistentFsLoader":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
			- "ApplicationImpl@942166693 container":StandaloneCoroutine{Completing} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
				- "com.intellij.openapi.vfs.newvfs.persistent.VFSHealthCheckServiceStarter":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at com.intellij.openapi.vfs.newvfs.persistent.VFSHealthCheckServiceStarter.execute(VFSHealthChecker.kt:105)
					at com.intellij.platform.ide.bootstrap.ApplicationLoader$executeAsyncAppInitListeners$1.invokeSuspend(ApplicationLoader.kt:296)
				- "com.intellij.internal.statistic.updater.StatisticsJobsScheduler":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
					at com.intellij.internal.statistic.updater.StatisticsJobsScheduler$execute$2.invokeSuspend(StatisticsJobsScheduler.kt:72)
					at com.intellij.internal.statistic.updater.StatisticsJobsScheduler.execute(StatisticsJobsScheduler.kt:37)
					at com.intellij.platform.ide.bootstrap.ApplicationLoader$executeAsyncAppInitListeners$1.invokeSuspend(ApplicationLoader.kt:296)
					- "com.intellij.internal.statistic.updater.StatisticsJobsScheduler":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at com.intellij.internal.statistic.updater.StatisticsJobsSchedulerKt.checkPreviousExternalUploadResult(StatisticsJobsScheduler.kt:129)
						at com.intellij.internal.statistic.updater.StatisticsJobsScheduler$execute$2$3.invokeSuspend(StatisticsJobsScheduler.kt:62)
					- "com.intellij.internal.statistic.updater.StatisticsJobsScheduler":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at com.intellij.internal.statistic.updater.StatisticsJobsScheduler.runEventLogStatisticsService(StatisticsJobsScheduler.kt:77)
						at com.intellij.internal.statistic.updater.StatisticsJobsScheduler$execute$2$4.invokeSuspend(StatisticsJobsScheduler.kt:65)
					- "com.intellij.internal.statistic.updater.StatisticsJobsScheduler":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at com.intellij.internal.statistic.updater.StatisticsJobsSchedulerKt.runValidationRulesUpdate(StatisticsJobsScheduler.kt:115)
						at com.intellij.internal.statistic.updater.StatisticsJobsScheduler$execute$2$5.invokeSuspend(StatisticsJobsScheduler.kt:68)
			- "com.intellij.internal.statistic.StatisticsServiceApplicationScope":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.internal.statistic.service.fus.collectors.FUStateUsagesLogger":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.internal.statistic.service.fus.collectors.FUStateUsagesLogger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at com.intellij.internal.statistic.service.fus.collectors.FUStateUsagesLogger.logApplicationStateRegularly(FUStateUsagesLogger.kt:157)
					at com.intellij.internal.statistic.service.fus.collectors.FUStateUsagesLogger$1.invokeSuspend(FUStateUsagesLogger.kt:53)
			- "com.intellij.openapi.util.registry.RegistryManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "app service preloading":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "FileTypeManager Redetect":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, LimitedDispatcher]
				- "com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
					at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
					at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
					at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
					at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
					at com.intellij.openapi.fileTypes.impl.AlarmAdapterKt$singleAlarm$1.invokeSuspend(AlarmAdapter.kt:24)
					- "com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
						at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
						at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
						at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
						- "com.intellij.openapi.fileTypes.impl.FileTypeManagerImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
							at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
			- "com.intellij.configurationStore.schemeManager.SchemeManagerFactoryBase$ApplicationSchemeManagerFactory":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.util.indexing.FileBasedIndexImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.diagnostic.PerformanceWatcherImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.util.net.ssl.CertificateManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.ide.plugins.marketplace.MarketplaceRequests":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.internal.statistic.StatisticsRegionUrlMapperServiceImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.internal.statistic.StatisticsRegionUrlMapperServiceImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at com.intellij.internal.statistic.StatisticsRegionUrlMapperServiceImpl$1.invokeSuspend(StatisticsRegionUrlMapperServiceImpl.kt:35)
			- "com.intellij.openapi.progress.impl.PlatformTaskSupport":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.openapi.progress.impl.TaskInfoEntityCollector":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.progress.impl.TaskInfoEntityCollector":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at fleet.kernel.rete.ReteKt$tokenSetsFlow$1$1.invokeSuspend(Rete.kt:537)
					at kotlinx.coroutines.flow.AbstractFlow.collect(Flow.kt:226)
					at com.intellij.openapi.progress.impl.TaskInfoEntityCollectorKt$collectActiveTasks$1$1.invokeSuspend(TaskInfoEntityCollector.kt:52)
					at com.intellij.platform.kernel.ApiKt$withKernel$2.invokeSuspend(api.kt:24)
					at com.intellij.platform.kernel.ApiKt.withKernel(api.kt:22)
					at com.intellij.openapi.progress.impl.TaskInfoEntityCollectorKt$collectActiveTasks$1.invokeSuspend(TaskInfoEntityCollector.kt:49)
			- "DefaultProjectImpl@736796386 container":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x com.intellij.platform.images)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x com.intellij.css)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x com.intellij.modules.json)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x org.editorconfig.editorconfigjetbrains)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x org.jetbrains.plugins.yaml)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x org.intellij.intelliLang)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				-[x10 of] "(DefaultProjectImpl@736796386 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x hg4idea)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x com.jetbrains.plugins.webDeployment)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x com.jetbrains.restClient)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x Git4Idea)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x JavaScript)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x org.intellij.qodana)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x com.intellij.configurationScript)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x tslint)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x com.intellij.database)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x Karma)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x NodeJS)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x intellij.indexing.shared)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(DefaultProjectImpl@736796386 x AngularJS)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.client.ClientProjectSessionsManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl$subscribeToChanges$1.invokeSuspend(CodeInsightContextManagerImpl.kt:78)
						- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
							- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":ProducerCoroutine{Completing} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
									at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
									at kotlinx.coroutines.flow.internal.ChannelLimitedFlowMerge$collectTo$2$1.invokeSuspend(Merge.kt:92)
				- "com.intellij.workspaceModel.ide.impl.WorkspaceModelImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.workspaceModel.ide.impl.WorkspaceModelCacheImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.workspaceModel.ide.impl.WorkspaceModelCacheImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
						at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
						at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
						at com.intellij.workspaceModel.ide.impl.WorkspaceModelCacheImpl$2.invokeSuspend(WorkspaceModelCacheImpl.kt:84)
						- "com.intellij.workspaceModel.ide.impl.WorkspaceModelCacheImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
							at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
				- "com.intellij.configurationStore.statistic.eventLog.FeatureUsageSettingsEvents":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.configurationStore.statistic.eventLog.FeatureUsageSettingsEvents":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at com.intellij.configurationStore.statistic.eventLog.FeatureUsageSettingsEvents$1.invokeSuspend(FeatureUsageSettingsEvents.kt:60)
				- "com.intellij.configurationStore.schemeManager.SchemeManagerFactoryBase$ProjectSchemeManagerFactory":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(DefaultProjectImpl@736796386), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.util.indexing.diagnostic.IndexDiagnosticDumper":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "org.jetbrains.ide.BuiltInServerManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.psi.search.FileTypeIndexChangeNotifierService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "FileTypeIndex change notificator":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
					at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
					at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
					at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
					at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
					at com.intellij.psi.search.FileTypeIndexChangeNotifier$worker$1.invokeSuspend(FileTypeIndexChangeNotifier.kt:24)
					- "FileTypeIndex change notificator":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
						- "FileTypeIndex change notificator":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at com.intellij.platform.util.coroutines.flow.FlowKt$throttle$1.invokeSuspend(flow.kt:46)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
							- "FileTypeIndex change notificator":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at com.intellij.platform.util.coroutines.flow.FlowKt$throttle$1$latchJob$1.invokeSuspend(flow.kt:41)
							- "FileTypeIndex change notificator":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
								at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
			- "com.intellij.util.indexing.events.ChangedFilesCollector":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "FileBasedIndex Vfs Event Processor":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, LimitedDispatcher]
			- "com.intellij.internal.statistic.eventLog.EventLogListenersManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "ProjectImpl@920711237 container":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "project service preloading":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "project activities":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.interpreter.NodeJsInterpreterManagerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.codeInsight.actions.ReaderModeEditorSettingsListener":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.codeInsight.navigation.InitCtrlMouseHandlerActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.facet.impl.pointers.FacetPointersPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.usages.impl.rules.UsageFilteringRulesActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.workspaceModel.ide.impl.WorkspaceEntitiesLifecycleActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
							at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
							at com.intellij.workspaceModel.ide.impl.WorkspaceEntitiesLifecycleActivity$execute$2.invokeSuspend(WorkspaceEntitiesLifecycleActivity.kt:35)
							at com.intellij.workspaceModel.ide.impl.WorkspaceEntitiesLifecycleActivity.execute(WorkspaceEntitiesLifecycleActivity.kt:24)
							at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
					- "com.intellij.workspaceModel.ide.impl.OrphanageActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.actions.IdeScalePostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.fileTypes.impl.ApproveRemovedMappingsActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vfs.encoding.EncodingProjectManagerStartUpActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.ui.localization.statistics.StartupStatisticsListener":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.actions.PowerSaveModeNotifier":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.actions.EssentialHighlightingNotifier":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.execution.startup.ProjectStartupRunner":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.startup.CheckKeysStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.util.indexing.projectFilter.ProjectIndexableFilesFilterHealthCheckStarter":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
							at com.intellij.util.indexing.projectFilter.ProjectIndexableFilesFilterHealthCheckStarter.execute(ProjectIndexableFilesFilterHealthCheck.kt:57)
							at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
					- "com.intellij.codeInsight.folding.impl.FoldingHintPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.codeInsight.codeVision.CodeVisionInitializer$CodeVisionInitializerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.scratch.workspace.ScratchWorkspaceStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
							at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
							at com.intellij.ide.scratch.workspace.ScratchWorkspaceStartupActivity$execute$2.invokeSuspend(ScratchWorkspaceStartupActivity.kt:30)
							at com.intellij.ide.scratch.workspace.ScratchWorkspaceStartupActivity.execute(ScratchWorkspaceStartupActivity.kt:23)
							at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
					- "com.intellij.codeInsight.documentation.DocumentationSettingsListener":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.projectRoots.impl.UnknownSdkStartupChecker":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
							at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
							at com.intellij.openapi.projectRoots.impl.UnknownSdkStartupChecker.execute(UnknownSdkStartupChecker.kt:31)
							at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
					- "com.intellij.openapi.projectRoots.impl.UnknownSdkHeadlessActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.tasks.impl.TaskManagerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.externalSystem.service.ExternalSystemStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.spellchecker.grazie.GrazieSpellCheckerEngine$SpellerLoadActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.internal.statistic.updater.StatisticsStateCollectorsScheduler$MyStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
							at com.intellij.internal.statistic.updater.StatisticsStateCollectorsScheduler$MyStartupActivity.execute(StatisticsStateCollectorsScheduler.kt:63)
							at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
					- "com.intellij.openapi.vcs.impl.VcsInitialization$StartUpActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.changes.shelf.ShelveChangeManagerProjectActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.update.RestoreUpdateTree$RestoreUpdateTreeStartUpActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.vcs.log.impl.VcsProjectLog$InitLogStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.bookmarks.frontend.LineBookmarkActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.debugger.impl.frontend.evaluate.quick.common.ValueLookupManagerProjectActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.debugger.impl.frontend.FrontendXDebuggerInitializationProjectActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.editor.frontend.FrontendEditorHandler":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.ide.newUiOnboarding.newUi.NewUiOnboardingStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.repo.GitCommitTemplateTracker$GitCommitTemplateTrackerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.GitMergeCommitMessageActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.webSymbols.nodejs.PackageJsonWebSymbolsRegistryManagerStartup":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.webSymbols.nodejs.WebTypesNpmLoader$TriggerUpdateIfNeededActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.lang.javascript.linter.PackageJsonForLinterConfigChangeTracker":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.library.node_modules.NodeModulesDirectoryStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.library.yarn.pnp.YarnPnpStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.library.exclude.JsExcludeStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.library.core.NodeCoreLibraryStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.packageJson.workspaceModel.PackageJsonEntityStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.debugger.activity.NextJsCreateClientRunConfigurationActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.debugger.activity.NextJsCreateServerRunConfigurationActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.staticAnalysis.QodanaAwaitBackgroundStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.download.IndexDownloadServiceProjectStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.platform.impl.BundledSharedIndexPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.ultimate.git.SharedGitHashesProjectStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.vuejs.lang.html.psi.arrangement.VueArrangementSettingsMigration":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.vuejs.libraries.nuxt.library.NuxtFolderLibraryStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.execution.ui.ExecutionReasonableHistoryManager":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.script.IdeStartupScripts":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.datagrid.DataGridStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.editor.DatabaseStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.workspaceModel.ide.impl.VirtualFileUrlsLazyInitializer":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.workspaceModel.ide.impl.WorkspaceModelCachesInvalidatorBackgroundActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.diagnostic.IdeHeartbeatEventReporter":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.featureStatistics.InternalFlagDetection":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.diagnostic.UnsupportedGlibcNotifierActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.updateSettings.impl.pluginsAdvertisement.PluginsAdvertiserStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.projectRoots.impl.jdkDownloader.JdkUpdaterStartup":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
							at com.intellij.openapi.projectRoots.impl.jdkDownloader.JdkUpdaterStartup.execute(JdkUpdater.kt:58)
							at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
					- "com.intellij.accessibility.AccessibilityUsageTrackerCollector$CollectStatisticsTask":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.plugins.LanguagePluginDetectionStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.notification.impl.RemindLaterActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.plugins.DependencyFeatureCollector":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.diagnostic.opentelemetry.JVMStatsToOTelReporter":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vfs.newvfs.monitoring.VFSInitializationConditionsToFusReporter":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.workspaceModel.ide.impl.jps.serialization.DelayedProjectSynchronizer":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.facet.FacetTypeFeatureCollector":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.feedback.csat.CsatNewUserTracker":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.externalSystem.autolink.UnlinkedProjectStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.externalSystem.service.project.manage.ReprocessContentRootDataActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.codeInsight.ChangesToolwindowPassCache":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.execution.dashboard.RunDashboardCheckerActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.jetbrains.plugins.webDeployment.DeploymentPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.grazie.ide.notification.GrazieNotificationComponent":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.ignore.GitIgnoreInStoreDirGeneratorActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.config.GitSetupProjectConfig":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.repo.GitShallowRepositoryCheck":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.GitModalCommitDeprecationNotifier":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.index.GitStageStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.signing.GpgAgentConfiguratorStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.lang.javascript.library.typings.TypeScriptInstallExternalDefinitionsStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.packageJson.notification.PackageJsonUpdateNotifier$MyStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.aqua.runners.playwright.js.duration.TestDurationInlayService$TestDurationInlayServiceFileListenerInstaller":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.QodanaStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.staticAnalysis.QodanaLinterBackgroundStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.autoconfig.DatabaseConfigStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.ultimate.license.ProjectSharedIndexesLicenseActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.webstorm.typeEngineWidget.survey.WebStormTypeEngineStateLogger":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.platform.project.ProjectEntitiesStorage$ProjectEntityScopeService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.project.ProjectEntitiesStorage$ProjectEntityScopeService":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
						at com.intellij.util.CoroutinesKt$awaitCancellationAndInvoke$1.invokeSuspend(coroutines.kt:35)
				- "(ProjectImpl@920711237 x com.intellij.platform.images)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x com.intellij.css)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x com.intellij.modules.json)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x org.editorconfig.editorconfigjetbrains)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x org.jetbrains.plugins.yaml)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x org.intellij.intelliLang)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.changes.ChangeListManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.impl.ProjectLevelVcsManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.vcs.commit.CommitModeManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.impl.VcsInitialization":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.changes.VcsIgnoreManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.impl.ModuleVcsDetector":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.changes.VcsDirtyScopeManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.changes.VcsDirtyScopeVfsListener":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.impl.LineStatusTrackerManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.roots.VcsRootScanner":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.openapi.vcs.roots.VcsRootScanner":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at com.intellij.openapi.vcs.roots.VcsRootScanner$4.invokeSuspend(VcsRootScanner.kt:65)
							- "com.intellij.openapi.vcs.roots.VcsRootScanner":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
								at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
								at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
								at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
								- "com.intellij.openapi.vcs.roots.VcsRootScanner":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
									at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
									at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
					- "com.intellij.openapi.vcs.changes.shelf.ShelveChangesManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.impl.FileStatusManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.vcs.log.impl.VcsProjectLog":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "Close VCS log":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
							at com.intellij.util.CoroutinesKt$awaitCancellationAndInvoke$1.invokeSuspend(coroutines.kt:35)
				-[x7 of] "(ProjectImpl@920711237 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.debugger.impl.frontend.evaluate.quick.common.ValueLookupManagerSubscriptionService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.platform.debugger.impl.frontend.evaluate.quick.common.ValueLookupManagerSubscriptionService":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
							at fleet.kernel.rete.ReteKt$tokenSetsFlow$1$1.invokeSuspend(Rete.kt:537)
							at kotlinx.coroutines.flow.AbstractFlow.collect(Flow.kt:226)
							at com.intellij.platform.debugger.impl.frontend.evaluate.quick.common.ValueLookupManagerSubscriptionsKt$subscribeForDebuggingStart$1.invokeSuspend(ValueLookupManagerSubscriptions.kt:21)
						- "com.intellij.platform.debugger.impl.frontend.evaluate.quick.common.ValueLookupManagerSubscriptionService":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
							at fleet.kernel.rete.ReteKt$tokenSetsFlow$1$1.invokeSuspend(Rete.kt:537)
							at kotlinx.coroutines.flow.AbstractFlow.collect(Flow.kt:226)
							at com.intellij.platform.debugger.impl.frontend.evaluate.quick.common.ValueLookupManagerSubscriptionsKt$subscribeForValueHintHideRequest$1.invokeSuspend(ValueLookupManagerSubscriptions.kt:31)
					- "com.intellij.platform.debugger.impl.frontend.FrontendXDebuggerManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.platform.debugger.impl.frontend.FrontendXDebuggerManager":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at kotlinx.coroutines.flow.FlowKt__ShareKt$launchSharing$1.invokeSuspend(Share.kt:210)
							- "com.intellij.platform.debugger.impl.frontend.FrontendXDebuggerManager":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
								at com.intellij.platform.debugger.impl.frontend.FrontendXDebuggerManager$currentSession$1.invokeSuspend(FrontendXDebuggerManager.kt:20)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
								- "com.intellij.platform.debugger.impl.frontend.FrontendXDebuggerManager":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
									at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
									at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
									at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
									at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
									at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
									at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
									at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
									- "com.intellij.platform.debugger.impl.frontend.FrontendXDebuggerManager":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
										at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
										at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
										at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
				- "(ProjectImpl@920711237 x hg4idea)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x com.jetbrains.plugins.webDeployment)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x com.jetbrains.restClient)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x Git4Idea)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.GitDisposable":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "GitVcs":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
							-[x2 of] "GitVcs":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
								at com.intellij.util.CoroutinesKt$awaitCancellationAndInvoke$1.invokeSuspend(coroutines.kt:35)
						- "GitRepositoryImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
							- "GitTagHolder":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.repo.GitRepositoryManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.repo.GitCommitTemplateTracker":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.GitMergeCommitMessageHolder":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.ignore.GitIgnoreInStoreDirGenerator":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.signing.GpgAgentConfigurator":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "git4idea.commit.signing.GpgAgentConfigurator":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at git4idea.commit.signing.GpgAgentConfigurator$init$1.invokeSuspend(GpgAgentConfigurator.kt:99)
							- "git4idea.commit.signing.GpgAgentConfigurator":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
								at com.intellij.platform.util.coroutines.flow.FlowKt$debounceBatch$1.invokeSuspend(flow.kt:147)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
					- "git4idea.commit.signing.GpgAgentConfigurationNotificator":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x JavaScript)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.lang.javascript.library.typings.TypeScriptExternalDefinitionsService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.lang.javascript.library.typings.TypeScriptExternalDefinitionsService":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at com.intellij.lang.javascript.library.typings.TypeScriptExternalDefinitionsService$4.invokeSuspend(TypeScriptExternalDefinitionsService.kt:69)
							- "com.intellij.lang.javascript.library.typings.TypeScriptExternalDefinitionsService":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
								at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
								at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
								at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
								- "com.intellij.lang.javascript.library.typings.TypeScriptExternalDefinitionsService":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
									at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
									at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
					- "com.intellij.lang.javascript.modules.remote.JSRemoteModulesUsagesDetector":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.lang.javascript.modules.remote.JSRemoteModulesUsagesDetector":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at com.intellij.util.SingleAlarm$request$1$1.invokeSuspend(SingleAlarm.kt:345)
					- "com.intellij.lang.javascript.service.JSLanguageServiceService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "Scope for VueClassicTypeScript":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "Scope for Angular2TypeScript":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "Scope for TypeScriptServerServiceImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.lang.typescript.tsconfig.TypeScriptConfigLibraryUpdater":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.lang.typescript.tsconfig.TypeScriptConfigLibraryUpdater":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at com.intellij.lang.typescript.tsconfig.TypeScriptConfigLibraryUpdater$1.invokeSuspend(TypeScriptConfigLibraryUpdater.kt:50)
							- "com.intellij.lang.typescript.tsconfig.TypeScriptConfigLibraryUpdater":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
								at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
								- "com.intellij.lang.typescript.tsconfig.TypeScriptConfigLibraryUpdater":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
									at com.intellij.lang.typescript.tsconfig.TypeScriptConfigLibraryUpdater$1$1.invokeSuspend(TypeScriptConfigLibraryUpdater.kt:55)
									at kotlinx.coroutines.flow.FlowKt__MergeKt$mapLatest$1.invokeSuspend(Merge.kt:213)
									at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1$2.invokeSuspend(Merge.kt:30)
				- "(ProjectImpl@920711237 x org.intellij.qodana)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.inspectionKts.KtsInspectionsManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "org.jetbrains.qodana.inspectionKts.KtsInspectionsManager":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at kotlinx.coroutines.flow.FlowKt__EmittersKt$onEmpty$$inlined$unsafeFlow$1.collect(Emitters.kt:113)
							at kotlinx.coroutines.flow.FlowKt__ShareKt$launchSharing$1.invokeSuspend(Share.kt:215)
							- "org.jetbrains.qodana.inspectionKts.KtsInspectionsManager":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
								at org.jetbrains.qodana.inspectionKts.KtsInspectionsManager$inspectionFilesFlow$1.invokeSuspend(KtsInspectionsManager.kt:134)
								at kotlinx.coroutines.flow.AbstractFlow.collect(Flow.kt:226)
								at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
								- "org.jetbrains.qodana.inspectionKts.KtsInspectionsManager":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
									at kotlinx.coroutines.channels.ProduceKt.awaitClose(Produce.kt:150)
									at org.jetbrains.qodana.coroutines.FlowKt$vfsChangesMapFlow$1.invokeSuspend(flow.kt:54)
									at kotlinx.coroutines.flow.CallbackFlowBuilder.collectTo(Builders.kt:330)
									at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
				- "(ProjectImpl@920711237 x com.intellij.configurationScript)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x tslint)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x com.intellij.database)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.dataSource.DataSourceModelStorageImpl$Prj":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.database.dataSource.DataSourceModelStorageImpl$Prj":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at com.intellij.util.SingleAlarm$scheduleTask$1$1.invokeSuspend(SingleAlarm.kt:410)
				- "(ProjectImpl@920711237 x Karma)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x NodeJS)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				-[x2 of] "(ProjectImpl@920711237 x intellij.indexing.shared)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x AngularJS)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.client.ClientProjectSessionsManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.workspaceModel.ide.impl.WorkspaceModelImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.workspaceModel.ide.impl.WorkspaceModelCacheImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.workspaceModel.ide.impl.WorkspaceModelCacheImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
						at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
						at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
						at com.intellij.workspaceModel.ide.impl.WorkspaceModelCacheImpl$2.invokeSuspend(WorkspaceModelCacheImpl.kt:84)
						- "com.intellij.workspaceModel.ide.impl.WorkspaceModelCacheImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
							at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
				- "com.intellij.openapi.project.ExternalStorageConfigurationManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.configurationStore.statistic.eventLog.FeatureUsageSettingsEvents":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.configurationStore.statistic.eventLog.FeatureUsageSettingsEvents":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at com.intellij.configurationStore.statistic.eventLog.FeatureUsageSettingsEvents$1.invokeSuspend(FeatureUsageSettingsEvents.kt:60)
				- "com.intellij.workspaceModel.ide.impl.legacyBridge.project.ProjectRootManagerBridge":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.workspaceModel.ide.impl.legacyBridge.module.ModuleManagerComponentBridge":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "ModuleBridgeImpl container":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ModuleBridgeImpl), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "(ModuleBridgeImpl x org.jetbrains.security.package-checker)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ModuleBridgeImpl), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.util.indexing.ProjectEntityIndexingService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.xdebugger.impl.XDebuggerManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "XDebuggerExecutionPointManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.EDT]
						- "XDebuggerExecutionPointManager":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.EDT]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at kotlinx.coroutines.flow.FlowKt__ShareKt$launchSharing$1.invokeSuspend(Share.kt:210)
							- "XDebuggerExecutionPointManager":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.EDT]
								at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
								at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
						- "XDebuggerExecutionPointManager/UI":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.EDT]
							-[x2 of] "XDebuggerExecutionPointManager/UI":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.EDT]
								at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
								at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
								at com.intellij.xdebugger.impl.ui.ExecutionPositionUiKt$showExecutionPositionUi$1.invokeSuspend(ExecutionPositionUi.kt:49)
								- "XDebuggerExecutionPointManager/UI":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.EDT]
									at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
									at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
									at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
									at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
									at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
									at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
									at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
									- "XDebuggerExecutionPointManager/UI":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.EDT]
										at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
										at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
										at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
									- "XDebuggerExecutionPointManager/UI":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.EDT]
										at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
										at com.intellij.xdebugger.impl.ui.ExecutionPositionUiKt$showExecutionPositionUi$1$2.invokeSuspend(ExecutionPositionUi.kt:51)
										at kotlinx.coroutines.flow.FlowKt__MergeKt$mapLatest$1.invokeSuspend(Merge.kt:213)
										at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1$2.invokeSuspend(Merge.kt:30)
							- "XDebuggerExecutionPointManager/UI":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.EDT]
								at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
								at com.intellij.xdebugger.impl.XDebuggerExecutionPointManager$1.invokeSuspend(XDebuggerExecutionPointManager.kt:69)
					- "com.intellij.xdebugger.impl.XDebuggerManagerImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at com.intellij.xdebugger.impl.XDebuggerLineChangeHandler$1.invokeSuspend(XDebuggerLineChangeHandler.kt:28)
						- "com.intellij.xdebugger.impl.XDebuggerManagerImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
							at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
				- "com.intellij.configurationStore.schemeManager.SchemeManagerFactoryBase$ProjectSchemeManagerFactory":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.project.InitialVfsRefreshService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.project.InitialVfsRefreshService":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
						at com.intellij.util.CoroutinesKt$awaitCancellationAndInvoke$1.invokeSuspend(coroutines.kt:35)
				- "com.intellij.openapi.fileEditor.impl.IdeDocumentHistoryImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl$subscribeToChanges$1.invokeSuspend(CodeInsightContextManagerImpl.kt:78)
						- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
							- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":ProducerCoroutine{Completing} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								- "com.intellij.codeInsight.multiverse.CodeInsightContextManagerImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
									at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
									at kotlinx.coroutines.flow.internal.ChannelLimitedFlowMerge$collectTo$2$1.invokeSuspend(Merge.kt:92)
				- "com.intellij.ide.startup.impl.StartupManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.GeneratedSourceFileChangeTrackerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.GeneratedSourceFileChangeTrackerImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at com.intellij.ide.GeneratedSourceFileChangeTrackerImpl$startHandlingCheckRequests$1.invokeSuspend(GeneratedSourceFileChangeTrackerImpl.kt:58)
						- "com.intellij.ide.GeneratedSourceFileChangeTrackerImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
							at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
							at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
							at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
							- "com.intellij.ide.GeneratedSourceFileChangeTrackerImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
								at kotlinx.coroutines.flow.FlowKt__LimitKt$takeWhile$$inlined$unsafeFlow$1.collect(Limit.kt:123)
								at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
				- "com.intellij.openapi.project.DumbServiceImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.project.DumbServiceImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.util.indexing.ProjectFileBasedIndexStartupActivityScope":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.util.indexing.UnindexedFilesScannerExecutorImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "Scanning (merge parameters)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "Scanning (root)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
						- "Scanning (root)":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
							at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
							at kotlinx.coroutines.flow.FlowKt__ReduceKt.first(Reduce.kt:179)
							at com.intellij.util.indexing.UnindexedFilesScannerExecutorImpl$1.invokeSuspend(UnindexedFilesScannerExecutorImpl.kt:113)
							- "scanning task execution trigger":DeferredCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
								at kotlinx.coroutines.channels.BufferedChannel.receiveCatchingOnNoWaiterSuspend-GKJJFZk(BufferedChannel.kt:3084)
								at kotlinx.coroutines.channels.BufferedChannel.receiveCatching-JP2dKIU$suspendImpl(BufferedChannel.kt:741)
								at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2.invokeSuspend(Combine.kt:51)
								at kotlinx.coroutines.flow.FlowKt__ReduceKt.first(Reduce.kt:179)
								at com.intellij.util.indexing.UnindexedFilesScannerExecutorImpl$1$1.invokeSuspend(UnindexedFilesScannerExecutorImpl.kt:94)
								- "scanning task execution trigger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
									at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
									at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
								- "scanning task execution trigger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
									at kotlinx.coroutines.channels.BufferedChannel.receiveCatchingOnNoWaiterSuspend-GKJJFZk(BufferedChannel.kt:3084)
									at kotlinx.coroutines.channels.BufferedChannel.receiveCatching-JP2dKIU$suspendImpl(BufferedChannel.kt:741)
									at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2.invokeSuspend(Combine.kt:51)
									at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
									- "scanning task execution trigger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
										at kotlinx.coroutines.channels.BufferedChannel.receiveCatchingOnNoWaiterSuspend-GKJJFZk(BufferedChannel.kt:3084)
										at kotlinx.coroutines.channels.BufferedChannel.receiveCatching-JP2dKIU$suspendImpl(BufferedChannel.kt:741)
										at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2.invokeSuspend(Combine.kt:51)
										at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
										- "scanning task execution trigger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
											at kotlinx.coroutines.channels.BufferedChannel.receiveCatchingOnNoWaiterSuspend-GKJJFZk(BufferedChannel.kt:3084)
											at kotlinx.coroutines.channels.BufferedChannel.receiveCatching-JP2dKIU$suspendImpl(BufferedChannel.kt:741)
											at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2.invokeSuspend(Combine.kt:51)
											at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
											- "scanning task execution trigger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
												at kotlinx.coroutines.channels.BufferedChannel.receiveCatchingOnNoWaiterSuspend-GKJJFZk(BufferedChannel.kt:3084)
												at kotlinx.coroutines.channels.BufferedChannel.receiveCatching-JP2dKIU$suspendImpl(BufferedChannel.kt:741)
												at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2.invokeSuspend(Combine.kt:51)
												at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
												-[x2 of] "scanning task execution trigger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
													at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
													at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
											- "scanning task execution trigger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
												at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
												at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
										- "scanning task execution trigger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
											at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
											at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
									- "scanning task execution trigger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.IO]
										at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
										at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
				- "(ProjectImpl@920711237 x intellij.indexing.shared.core)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.download.ShadeIndexDumbModeTracker":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.indexing.shared.download.ShadeIndexDumbModeTracker":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), ClientId(value=Host), Dispatchers.Default]
							at com.intellij.indexing.shared.download.ShadeIndexDumbModeTracker$exitDumbMode$1.invokeSuspend(SharedIndexDumbModeTracking.kt:37)
					- "com.intellij.indexing.shared.download.SharedIndexSuggestionService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.download.SharedIndexProjectRootsRefreshQueue":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.indexing.shared.download.SharedIndexProjectRootsRefreshQueue":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at com.intellij.indexing.shared.download.SharedIndexProjectRootsRefreshQueue$1.invokeSuspend(SharedIndexesRefresh.kt:50)
							- "com.intellij.indexing.shared.download.SharedIndexProjectRootsRefreshQueue":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
								at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
								at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
								at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
								- "com.intellij.indexing.shared.download.SharedIndexProjectRootsRefreshQueue":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
									at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
									at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x JavaScript))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.interpreter.NodeJsInterpreterManagerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.webSymbols.nodejs.PackageJsonWebSymbolsRegistryManagerStartup":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.webSymbols.nodejs.WebTypesNpmLoader$TriggerUpdateIfNeededActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.lang.javascript.linter.PackageJsonForLinterConfigChangeTracker":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.library.node_modules.NodeModulesDirectoryStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.library.yarn.pnp.YarnPnpStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.library.exclude.JsExcludeStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.library.core.NodeCoreLibraryStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.packageJson.workspaceModel.PackageJsonEntityStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.lang.javascript.library.typings.TypeScriptInstallExternalDefinitionsStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.nodejs.packageJson.notification.PackageJsonUpdateNotifier$MyStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.codeInsight.actions.ReaderModeEditorSettingsListener":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.codeInsight.navigation.InitCtrlMouseHandlerActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.facet.impl.pointers.FacetPointersPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.usages.impl.rules.UsageFilteringRulesActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.workspaceModel.ide.impl.WorkspaceEntitiesLifecycleActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
						at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
						at com.intellij.workspaceModel.ide.impl.WorkspaceEntitiesLifecycleActivity$execute$2.invokeSuspend(WorkspaceEntitiesLifecycleActivity.kt:35)
						at com.intellij.workspaceModel.ide.impl.WorkspaceEntitiesLifecycleActivity.execute(WorkspaceEntitiesLifecycleActivity.kt:24)
						at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
				- "com.intellij.workspaceModel.ide.impl.OrphanageActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.actions.IdeScalePostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.fileTypes.impl.ApproveRemovedMappingsActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.vfs.encoding.EncodingProjectManagerStartUpActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.ui.localization.statistics.StartupStatisticsListener":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.actions.PowerSaveModeNotifier":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.actions.EssentialHighlightingNotifier":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.execution.startup.ProjectStartupRunner":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.startup.CheckKeysStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.util.indexing.projectFilter.ProjectIndexableFilesFilterHealthCheckStarter":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
						at com.intellij.util.indexing.projectFilter.ProjectIndexableFilesFilterHealthCheckStarter.execute(ProjectIndexableFilesFilterHealthCheck.kt:57)
						at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
				- "com.intellij.codeInsight.folding.impl.FoldingHintPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.codeInsight.codeVision.CodeVisionInitializer$CodeVisionInitializerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.scratch.workspace.ScratchWorkspaceStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
						at kotlinx.coroutines.DelayKt.awaitCancellation(Delay.kt:160)
						at com.intellij.ide.scratch.workspace.ScratchWorkspaceStartupActivity$execute$2.invokeSuspend(ScratchWorkspaceStartupActivity.kt:30)
						at com.intellij.ide.scratch.workspace.ScratchWorkspaceStartupActivity.execute(ScratchWorkspaceStartupActivity.kt:23)
						at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
				- "com.intellij.codeInsight.documentation.DocumentationSettingsListener":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.projectRoots.impl.UnknownSdkStartupChecker":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
						at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
						at com.intellij.openapi.projectRoots.impl.UnknownSdkStartupChecker.execute(UnknownSdkStartupChecker.kt:31)
						at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
				- "com.intellij.openapi.projectRoots.impl.UnknownSdkHeadlessActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.tasks.impl.TaskManagerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.externalSystem.service.ExternalSystemStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.spellchecker.grazie.GrazieSpellCheckerEngine$SpellerLoadActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.internal.statistic.updater.StatisticsStateCollectorsScheduler$MyStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
						at com.intellij.internal.statistic.updater.StatisticsStateCollectorsScheduler$MyStartupActivity.execute(StatisticsStateCollectorsScheduler.kt:63)
						at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.impl.VcsInitialization$StartUpActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.changes.shelf.ShelveChangeManagerProjectActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.vcs.update.RestoreUpdateTree$RestoreUpdateTreeStartUpActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.codeInsight.ChangesToolwindowPassCache":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.vcs.log.impl.VcsProjectLog$InitLogStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.bookmarks.frontend.LineBookmarkActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.debugger.impl.frontend.evaluate.quick.common.ValueLookupManagerProjectActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.debugger.impl.frontend.FrontendXDebuggerInitializationProjectActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.editor.frontend.FrontendEditorHandler":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.ide.newUiOnboarding.newUi.NewUiOnboardingStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x Git4Idea))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.repo.GitCommitTemplateTracker$GitCommitTemplateTrackerStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.GitMergeCommitMessageActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.ignore.GitIgnoreInStoreDirGeneratorActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.config.GitSetupProjectConfig":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.repo.GitShallowRepositoryCheck":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.GitModalCommitDeprecationNotifier":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.index.GitStageStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "git4idea.commit.signing.GpgAgentConfiguratorStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x JavaScriptDebugger))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.debugger.activity.NextJsCreateClientRunConfigurationActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.javascript.debugger.activity.NextJsCreateServerRunConfigurationActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x org.intellij.qodana))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.staticAnalysis.QodanaAwaitBackgroundStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.QodanaStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.qodana.staticAnalysis.QodanaLinterBackgroundStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x intellij.indexing.shared.core))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.download.IndexDownloadServiceProjectStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.platform.impl.BundledSharedIndexPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x intellij.indexing.shared))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.ultimate.git.SharedGitHashesProjectStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x org.jetbrains.plugins.vue))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.vuejs.lang.html.psi.arrangement.VueArrangementSettingsMigration":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.vuejs.libraries.nuxt.library.NuxtFolderLibraryStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.execution.ui.ExecutionReasonableHistoryManager":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.script.IdeStartupScripts":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.datagrid.DataGridStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij.database))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.editor.DatabaseStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.database.autoconfig.DatabaseConfigStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.project.impl.BaseProjectDirectoriesImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.project.impl.BaseProjectDirectoriesImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
						at com.intellij.ide.project.impl.BaseProjectDirectoriesImpl$1.invokeSuspend(BaseProjectDirectoriesImpl.kt:31)
				- "com.intellij.codeInsight.navigation.CtrlMouseHandler2":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ui.EditorNotificationsImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "EditorNotificationsImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ui.EditorNotificationsImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at com.intellij.ui.EditorNotificationsImpl$6.invokeSuspend(EditorNotificationsImpl.kt:115)
						- "com.intellij.ui.EditorNotificationsImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
							at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
							at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
							at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
							- "com.intellij.ui.EditorNotificationsImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
								at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
				- "com.intellij.openapi.project.SmartModeScheduler":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.project.SmartModeScheduler":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
						at com.intellij.openapi.project.SmartModeScheduler$2.invokeSuspend(SmartModeScheduler.kt:66)
					- "com.intellij.openapi.project.SmartModeScheduler":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.openapi.project.SmartModeScheduler":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
						at com.intellij.openapi.project.SmartModeScheduler$3.invokeSuspend(SmartModeScheduler.kt:73)
				- "com.intellij.ide.actions.ui.ideScaleIndicator.IdeScaleIndicatorManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.ide.actions.ui.ideScaleIndicator.IdeScaleIndicatorManager":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at com.intellij.ide.actions.ui.ideScaleIndicator.IdeScaleIndicatorManager$1.invokeSuspend(IdeScaleIndicatorManager.kt:39)
						- "com.intellij.ide.actions.ui.ideScaleIndicator.IdeScaleIndicatorManager":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
							at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
				- "com.intellij.openapi.projectRoots.impl.UnknownSdkTrackerQueue":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.platform.backend.observation.PlatformActivityTrackerService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.spellchecker.grazie.GrazieSpellCheckerEngine":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.externalSystem.service.project.manage.ExternalProjectsManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.util.indexing.DumbModeWhileScanningTrigger":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.internal.statistic.service.fus.collectors.ProjectFUStateUsagesLogger":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.internal.statistic.service.fus.collectors.ProjectFUStateUsagesLogger":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at com.intellij.internal.statistic.service.fus.collectors.ProjectFUStateUsagesLogger.logProjectStateRegularly(FUStateUsagesLogger.kt:218)
						at com.intellij.internal.statistic.service.fus.collectors.ProjectFUStateUsagesLogger$1.invokeSuspend(FUStateUsagesLogger.kt:207)
				- "com.intellij.util.indexing.diagnostic.DumbModeFromScanningTrackerService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.util.indexing.diagnostic.DumbModeFromScanningTrackerService":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
						at com.intellij.util.indexing.diagnostic.DumbModeFromScanningTrackerService$1.invokeSuspend(DumbModeFromScanningTrackerService.kt:21)
				- "(ProjectImpl@920711237 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.dvcs.repo.VcsRepositoryManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.dvcs.ignore.IgnoredToExcludedSynchronizer":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
						- "com.intellij.dvcs.ignore.IgnoredToExcludedSynchronizer":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
							at com.intellij.dvcs.ignore.IgnoredToExcludedSynchronizer$1.invokeSuspend(IgnoredToExcludedSynchronizer.kt:82)
				- "com.intellij.openapi.wm.impl.status.widget.StatusBarWidgetsManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.webSymbols.context.impl.WebSymbolsContextDiscoveryInfo":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.spellchecker.SpellCheckerManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.codeInsight.daemon.impl.DaemonCodeAnalyzerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x org.jetbrains.plugins.vue)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.bookmarks.frontend.LineBookmarkListener":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.platform.lsp.impl.LspServerManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.lsp.impl.LspServerManagerImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
						at com.intellij.platform.lsp.impl.LspServerManagerImpl$addWorkspaceModelListener$1.invokeSuspend(LspServerManagerImpl.kt:251)
				- "com.intellij.workspaceModel.ide.impl.VirtualFileUrlsLazyInitializer":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.workspaceModel.ide.impl.WorkspaceModelCachesInvalidatorBackgroundActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.diagnostic.IdeHeartbeatEventReporter":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.featureStatistics.InternalFlagDetection":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.diagnostic.UnsupportedGlibcNotifierActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.updateSettings.impl.pluginsAdvertisement.PluginsAdvertiserStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.projectRoots.impl.jdkDownloader.JdkUpdaterStartup":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "run activity":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, run activity, Dispatchers.Default]
						at com.intellij.openapi.projectRoots.impl.jdkDownloader.JdkUpdaterStartup.execute(JdkUpdater.kt:58)
						at com.intellij.ide.startup.impl.StartupManagerImplKt$launchActivity$1.invokeSuspend(StartupManagerImpl.kt:493)
				- "com.intellij.accessibility.AccessibilityUsageTrackerCollector$CollectStatisticsTask":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.plugins.LanguagePluginDetectionStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.notification.impl.RemindLaterActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.plugins.DependencyFeatureCollector":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.diagnostic.opentelemetry.JVMStatsToOTelReporter":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.vfs.newvfs.monitoring.VFSInitializationConditionsToFusReporter":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.workspaceModel.ide.impl.jps.serialization.DelayedProjectSynchronizer":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.facet.FacetTypeFeatureCollector":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.platform.feedback.csat.CsatNewUserTracker":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.externalSystem.autolink.UnlinkedProjectStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.externalSystem.service.project.manage.ReprocessContentRootDataActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.execution.dashboard.RunDashboardCheckerActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.jetbrains.plugins.webDeployment))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.jetbrains.plugins.webDeployment.DeploymentPostStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x tanvd.grazi))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.grazie.ide.notification.GrazieNotificationComponent":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x JavaScript))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.aqua.runners.playwright.js.duration.TestDurationInlayService$TestDurationInlayServiceFileListenerInstaller":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x intellij.indexing.shared))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.ultimate.license.ProjectSharedIndexesLicenseActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.webstorm.typeEngineWidget.survey.WebStormTypeEngineStateLogger":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.externalSystem.autolink.UnlinkedProjectStartupActivity$CoroutineScopeService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.openapi.externalSystem.service.project.manage.SourceFolderManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x JavaScript)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.configurationStore.ProjectWithModulesStoreReloadManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "configuration store reload request flow processing":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
						at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
						at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
						at com.intellij.configurationStore.StoreReloadManagerImpl$1.invokeSuspend(StoreReloadManagerImpl.kt:51)
						- "configuration store reload request flow processing":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
							at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
				- "com.intellij.codeInspection.ex.ProjectInspectionToolRegistrar":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.codeInspection.ex.ProjectInspectionToolRegistrar":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at kotlinx.coroutines.flow.FlowKt__ShareKt$launchSharing$1.invokeSuspend(Share.kt:215)
						- "com.intellij.codeInspection.ex.ProjectInspectionToolRegistrar":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
							at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
							at kotlinx.coroutines.flow.FlowKt__EmittersKt$onStart$$inlined$unsafeFlow$1.collect(Emitters.kt:119)
							at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
							- "com.intellij.codeInspection.ex.ProjectInspectionToolRegistrar":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.channels.BufferedChannel.receiveCatchingOnNoWaiterSuspend-GKJJFZk(BufferedChannel.kt:3084)
								at kotlinx.coroutines.channels.BufferedChannel.receiveCatching-JP2dKIU$suspendImpl(BufferedChannel.kt:741)
								at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2.invokeSuspend(Combine.kt:51)
								at kotlinx.coroutines.flow.FlowKt__EmittersKt$onEmpty$$inlined$unsafeFlow$1.collect(Emitters.kt:113)
								at com.intellij.codeInspection.ex.Dynamic_inspectionsKt$dynamicInspectionsFlow$$inlined$flatMapLatest$1.invokeSuspend(dynamic-inspections.kt:189)
								at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3$1$2.invokeSuspend(Merge.kt:30)
								- "com.intellij.codeInspection.ex.ProjectInspectionToolRegistrar":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
									at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
									at kotlinx.coroutines.flow.FlowKt__EmittersKt$onEmpty$$inlined$unsafeFlow$1.collect(Emitters.kt:113)
									at kotlinx.coroutines.flow.internal.CombineKt$combineInternal$2$1.invokeSuspend(Combine.kt:28)
							- "com.intellij.codeInspection.ex.ProjectInspectionToolRegistrar":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.channels.ProduceKt.awaitClose(Produce.kt:150)
								at com.intellij.codeInspection.ex.Dynamic_inspectionsKt$dynamicInspectionsFlow$epUpdatedFlow$1.invokeSuspend(dynamic-inspections.kt:72)
								at kotlinx.coroutines.flow.CallbackFlowBuilder.collectTo(Builders.kt:330)
								at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
					- "com.intellij.codeInspection.ex.ProjectInspectionToolRegistrar":LazyStandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.flow.StateFlowImpl.collect(StateFlow.kt:402)
						at com.intellij.codeInspection.ex.ProjectInspectionToolRegistrar$updateInspectionProfilesSubscription$1.invokeSuspend(ProjectInspectionToolRegistrar.kt:31)
				- "(ProjectImpl@920711237 x intellij.prettierJS)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.util.indexing.projectFilter.ProjectIndexableFilesFilterHealthCheck":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.workspaceModel.ide.impl.GlobalWorkspaceModelCacheImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.workspaceModel.ide.impl.GlobalWorkspaceModelCacheImpl":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
					at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
					at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
					at com.intellij.workspaceModel.ide.impl.GlobalWorkspaceModelCacheImpl$1.invokeSuspend(GlobalWorkspaceModelCacheImpl.kt:56)
					- "com.intellij.workspaceModel.ide.impl.GlobalWorkspaceModelCacheImpl":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
						at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
			- "com.intellij.workspaceModel.ide.impl.jps.serialization.JpsGlobalModelSynchronizerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.openapi.editor.impl.EditorFactoryImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.psi.impl.DocumentCommitThread":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "Document Commit Pool":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, LimitedDispatcher]
			- "com.intellij.util.AlarmSharedCoroutineScopeHolder":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.javascript.nodejs.library.node_modules.NodeModulesIndexingRulesProvider$$Lambda/0x00007f37ce589b20@6f54c67b (Alarm)":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, LimitedDispatcher]
					at com.intellij.util.Alarm$Request$schedule$1.invokeSuspend(Alarm.kt:383)
			- "com.intellij.ide.AttachedModuleAwareRecentProjectsManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.ide.AttachedModuleAwareRecentProjectsManager":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
					at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
					at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
					at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
					at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
					at com.intellij.ide.RecentProjectsManagerBase$1.invokeSuspend(RecentProjectsManagerBase.kt:111)
					- "com.intellij.ide.AttachedModuleAwareRecentProjectsManager":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
						at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
						at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
						at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
						- "com.intellij.ide.AttachedModuleAwareRecentProjectsManager":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
							at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
			- "(ApplicationImpl@942166693 x intellij.indexing.shared)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x intellij.indexing.shared))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.ultimate.git.SharedGitHashesProjectStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.bookmarks.frontend.LineBookmarkActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.debugger.impl.frontend.evaluate.quick.common.ValueLookupManagerProjectActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.debugger.impl.frontend.FrontendXDebuggerInitializationProjectActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.editor.frontend.FrontendEditorHandler":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.ide.newUiOnboarding.newUi.NewUiOnboardingStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x org.jetbrains.plugins.sass)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x org.jetbrains.plugins.vue)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x org.jetbrains.plugins.vue))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.vuejs.lang.html.psi.arrangement.VueArrangementSettingsMigration":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "org.jetbrains.vuejs.libraries.nuxt.library.NuxtFolderLibraryStartupActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.openapi.vfs.encoding.EncodingManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "EncodingManagerImpl Document Pool":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, LimitedDispatcher]
			- "com.intellij.openapi.vfs.newvfs.RefreshQueueImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "RefreshQueue Pool":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, LimitedDispatcher]
				- "Async Refresh Event Processing":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, LimitedDispatcher]
			- "com.intellij.util.concurrency.EdtScheduler":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				-[x2 of] "com.intellij.util.concurrency.EdtScheduler":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), ClientId(value=Host), Dispatchers.Default]
					at com.intellij.util.concurrency.EdtScheduler$schedule$1.invokeSuspend(EdtScheduler.kt:56)
			- "com.intellij.util.gist.GistManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.util.SingleAlarmSharedCoroutineScopeHolder":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.openapi.wm.impl.status.widget.StatusBarActionManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.openapi.actionSystem.impl.ActionManagerImpl":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "ApplicationImpl@942166693 container":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x intellij.grid.plugin)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.grid.scripting.impl.DataLoaderManager$Associations":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.grid.scripting.impl.DataLoaderManager$Associations":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
						at kotlinx.coroutines.channels.BufferedChannel.emitAllInternal$kotlinx_coroutines_core(BufferedChannel.kt:1558)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:44)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllInternal(Channels.kt:47)
						at kotlinx.coroutines.flow.FlowKt__ChannelsKt.emitAllImpl$FlowKt__ChannelsKt(Channels.kt:32)
						at kotlinx.coroutines.flow.internal.ChannelFlow$collect$2.invokeSuspend(ChannelFlow.kt:119)
						at com.intellij.grid.scripting.impl.DataLoaderManager$Associations$1.invokeSuspend(DataLoaderManager.kt:225)
						- "com.intellij.grid.scripting.impl.DataLoaderManager$Associations":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
							at kotlinx.coroutines.selects.SelectImplementation.doSelectSuspend(Select.kt:440)
							at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1.invokeSuspend(Delay.kt:416)
							at kotlinx.coroutines.flow.internal.FlowCoroutineKt$scopedFlow$1$1.invokeSuspend(FlowCoroutine.kt:47)
							at kotlinx.coroutines.flow.internal.ChannelFlowTransformLatest$flowCollect$3.invokeSuspend(Merge.kt:23)
							at kotlinx.coroutines.flow.internal.ChannelFlow$collectToFun$1.invokeSuspend(ChannelFlow.kt:56)
							- "com.intellij.grid.scripting.impl.DataLoaderManager$Associations":ProducerCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
								at kotlinx.coroutines.flow.SharedFlowImpl.collect$suspendImpl(SharedFlow.kt:389)
								at kotlinx.coroutines.flow.FlowKt__DelayKt$debounceInternal$1$values$1.invokeSuspend(Delay.kt:204)
			- "(ApplicationImpl@942166693 x org.editorconfig.editorconfigjetbrains)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.ide.ui.customization.CustomActionsSchema":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.platform.execution.dashboard.RunDashboardCheckerActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x JavaScript)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x JavaScript))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.aqua.runners.playwright.js.duration.TestDurationInlayService$TestDurationInlayServiceFileListenerInstaller":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x intellij.indexing.shared)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x intellij.indexing.shared))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.indexing.shared.ultimate.license.ProjectSharedIndexesLicenseActivity":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "(ApplicationImpl@942166693 x com.intellij)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "(ProjectImpl@920711237 x (ApplicationImpl@942166693 x com.intellij))":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
					- "com.intellij.webstorm.typeEngineWidget.survey.WebStormTypeEngineStateLogger":ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ProjectImpl@920711237), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.ide.actionMacro.ActionMacroManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.platform.ide.impl.presentationAssistant.PresentationAssistant":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.codeInsight.intention.impl.IntentionShortcutManager":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.diagnostic.IdeHeartbeatEventReporterService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "com.intellij.diagnostic.IdeHeartbeatEventReporterService":StandaloneCoroutine{Active}, state: SUSPENDED [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor, Dispatchers.Default]
					at com.intellij.diagnostic.IdeHeartbeatEventReporterService.heartBeatRoutine(IdeHeartbeatEventReporter.kt:57)
					at com.intellij.diagnostic.IdeHeartbeatEventReporterService$1.invokeSuspend(IdeHeartbeatEventReporter.kt:52)
			- "com.intellij.ide.environment.impl.HeadlessEnvironmentService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.diagnostic.opentelemetry.JVMStatsToOTelReporter$ReportingService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.codeInspection.ex.InspectionToolRegistrar":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
			- "com.intellij.util.concurrency.InvokerService":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]
				- "Invoker.0.Thread.ReadAction=YES: Project(name=project, containerState=COMPONENT_CREATED, componentStore=/data/project)":supervisor:ChildScope{Active} [Kernel@3346pk9b9tohudv85lc1, Rete(failFast=false, commands=capacity=**********,data=[onReceive], reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6, dbSource=ReteDbSource(reteState=kotlinx.coroutines.flow.StateFlowImpl@3b07a0d6)), DbSourceContextElement(kernel Kernel@3346pk9b9tohudv85lc1), ComponentManager(ApplicationImpl@942166693), com.intellij.codeWithMe.ClientIdContextElementPrecursor]

- StandaloneCoroutine{Active}, state: SUSPENDED [Dispatchers.Default]
	at com.intellij.util.indexing.diagnostic.StorageDiagnosticData$setupIndexingReporting$2.invokeSuspend(StorageDiagnosticData.kt:327)
