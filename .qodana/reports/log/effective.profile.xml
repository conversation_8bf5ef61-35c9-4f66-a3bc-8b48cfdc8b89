<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="qodana.effective.profile" />
    <inspection_tool class="AngularAmbiguousComponentTag" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularBindingTypeMismatch" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularCliAddDependency" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularDeferBlockOnTrigger" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularForBlockNonIterableVar" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularIllegalForLoopTrackAccess" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularInaccessibleSymbol" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularIncorrectBlockUsage" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularIncorrectLetUsage" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularIncorrectTemplateDefinition" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularInsecureBindingToEvent" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularInvalidAnimationTriggerAssignment" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularInvalidEntryComponent" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularInvalidI18nAttribute" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularInvalidImportedOrDeclaredSymbol" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularInvalidSelector" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularInvalidTemplateReferenceVariable" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularMissingEventHandler" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularMissingOrInvalidDeclarationInModule" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularMissingRequiredDirectiveInputBinding" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularMultipleStructuralDirectives" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularNgOptimizedImage" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularNonEmptyNgContent" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularNonStandaloneComponentImports" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularRecursiveModuleImportExport" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularUndefinedBinding" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularUndefinedModuleExport" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularUndefinedTag" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularUnresolvedPipe" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularUnsupportedSyntax" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="AngularUnusedComponentImport" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="BadExpressionStatementJS" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="CallerJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="CheckDependencyLicenses" enabled="true" level="WARNING" enabled_by_default="false">
      <scope name="Hierarchy:[(com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@7b2fea3c, false), (com.intellij.psi.search.scope.packageSet.FilePatternPackageSet@2de65f7e, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@3ab99455, false), (com.intellij.psi.search.scope.packageSet.NamedPackageSetReference@34a6db64, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@30dca9c6, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@2f5e1d0c, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@59b32f8e, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@5bcca227, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@e8ba201, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@516278a1, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@116df25c, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@2f8eb7cc, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@7cc68396, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@3ea34bdf, false), (com.intellij.psi.search.scope.packageSet.CustomScopesProviderEx$AllScopeHolder$1@4022a145, true)]" level="WARNING" enabled="true" />
    </inspection_tool>
    <inspection_tool class="CommaExpressionJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ConstantConditionalExpressionJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ContinueOrBreakFromFinallyBlockJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="CustomRegExpInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ES6BindWithArrowFunction" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ES6ClassMemberInitializationOrder" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ES6DestructuringVariablesMerge" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ES6MissingAwait" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ES6PossiblyAsyncFunction" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ES6PreferShortImport" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ES6RedundantAwait" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ES6RedundantNestingInTemplateLiteral" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ES6UnusedImports" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="EmptyStatementBodyJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:**.md" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="file:buildSrc/*" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="$gitignore" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:**.test.ts" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:.qodana/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:tools/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:tests/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:dist/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:builds/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:build/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <scope name="glob:vendor/**" level="WARNING" enabled="false">
        <option name="m_reportEmptyBlocks" value="false" />
      </scope>
      <option name="m_reportEmptyBlocks" value="false" />
    </inspection_tool>
    <inspection_tool class="ExceptionCaughtLocallyJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="FallThroughInSwitchStatementJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="FlowJSConfig" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="FlowJSFlagCommentPlacement" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="HardcodedPasswords" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="IncompatibleMaskJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="InfiniteLoopJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="InfiniteRecursionJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="InjectedReferences" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSAccessibilityCheck" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSAssignmentUsedAsCondition" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSBitwiseOperatorUsage" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSClosureCompilerSyntax" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSCommentMatchesSignature" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSComparisonWithNaN" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSConsecutiveCommasInArrayLiteral" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSConstantReassignment" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="ERROR" enabled="false" />
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSDeprecatedSymbols" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSDuplicateCaseLabel" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSDuplicatedDeclaration" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSEqualityComparisonWithCoercion" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSIgnoredPromiseFromCall" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSIncompatibleTypesComparison" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSJQueryEfficiency" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSLastCommaInArrayLiteral" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSLastCommaInObjectLiteral" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSMismatchedCollectionQueryUpdate" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:**.md" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="file:buildSrc/*" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="$gitignore" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:**.test.ts" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:.qodana/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:tools/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:tests/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:dist/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:builds/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:build/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <scope name="glob:vendor/**" level="WARNING" enabled="false">
        <option name="queries" value="trace,write,forEach,length,size" />
        <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
      </scope>
      <option name="queries" value="trace,write,forEach,length,size" />
      <option name="updates" value="pop,push,shift,splice,unshift,add,insert,remove,reverse,copyWithin,fill,sort" />
    </inspection_tool>
    <inspection_tool class="JSNonASCIINames" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSObjectNullOrUndefined" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSOctalInteger" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="ERROR" enabled="false" />
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSPotentiallyInvalidConstructorUsage" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:**.md" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="file:buildSrc/*" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="$gitignore" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:**.test.ts" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:.qodana/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:tools/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:tests/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:dist/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:builds/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:build/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <scope name="glob:vendor/**" level="WARNING" enabled="false">
        <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
      </scope>
      <option name="myConsiderUppercaseFunctionsToBeConstructors" value="true" />
    </inspection_tool>
    <inspection_tool class="JSPotentiallyInvalidTargetOfIndexedPropertyAccess" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSPotentiallyInvalidUsageOfClassThis" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSPotentiallyInvalidUsageOfThis" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSPrimitiveTypeWrapperUsage" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSReferencingMutableVariableFromClosure" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSSuspiciousEqPlus" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSSuspiciousNameCombination" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:**.md" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="file:buildSrc/*" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="$gitignore" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:**.test.ts" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:.qodana/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:tools/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:tests/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:dist/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:builds/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:build/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <scope name="glob:vendor/**" level="WARNING" enabled="false">
        <group names="x,width,left,right" />
        <group names="y,height,top,bottom" />
        <exclude classes="Math" />
      </scope>
      <group names="x,width,left,right" />
      <group names="y,height,top,bottom" />
      <exclude classes="Math" />
    </inspection_tool>
    <inspection_tool class="JSSwitchVariableDeclarationIssue" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSTypeOfValues" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSUndeclaredVariable" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSUndefinedPropertyAssignment" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSUnnecessarySemicolon" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSUnreachableSwitchBranches" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSUnresolvedExtXType" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSUnusedAssignment" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSUnusedLocalSymbols" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSValidateJSDoc" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSVoidFunctionReturnValueUsed" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSXDomNesting" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JSXUnresolvedComponent" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JadeTabsAndSpaces" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="JsCoverageInspection" enabled="true" level="WARNING" enabled_by_default="false">
      <scope name="Hierarchy:[(com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@7b2fea3c, false), (com.intellij.psi.search.scope.packageSet.FilePatternPackageSet@2de65f7e, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@3ab99455, false), (com.intellij.psi.search.scope.packageSet.NamedPackageSetReference@34a6db64, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@30dca9c6, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@2f5e1d0c, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@59b32f8e, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@5bcca227, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@e8ba201, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@516278a1, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@116df25c, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@2f8eb7cc, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@7cc68396, false), (com.intellij.codeInspection.inspectionProfile.YamlInspectionProfileImpl$getGlobScope$packageSet$1@3ea34bdf, false), (com.intellij.psi.search.scope.packageSet.CustomScopesProviderEx$AllScopeHolder$1@4022a145, true)]" level="WARNING" enabled="true" />
    </inspection_tool>
    <inspection_tool class="KarmaConfigFile" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="LoopStatementThatDoesntLoopJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="MaliciousLibrariesLocal" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="MongoJSDeprecationInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="MongoJSExtDeprecationInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="MongoJSExtResolveInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="MongoJSExtSideEffectsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="MongoJSResolveInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="MongoJSSideEffectsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="NodeCoreCodingAssistance" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="NpmVulnerableApiCode" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="PointlessArithmeticExpressionJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="PointlessBooleanExpressionJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpDuplicateAlternationBranch" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpDuplicateCharacterInClass" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpEmptyAlternationBranch" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpRedundantClassElement" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpRedundantEscape" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpRedundantNestedCharacterClass" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpRepeatedSpace" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpSimplifiable" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpSingleCharAlternation" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpSuspiciousBackref" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpUnexpectedAnchor" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="RegExpUnnecessaryNonCapturingGroup" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ReservedWordUsedAsNameJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ReturnFromFinallyBlockJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ShiftOutOfRangeJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="SillyAssignmentJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="SuspiciousTypeOfGuard" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ThisExpressionReferencesGlobalObjectJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="ThrowFromFinallyBlockJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TrivialConditionalJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TrivialIfJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptAbstractClassConstructorCanBeMadeProtected" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptCheckImport" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="ERROR" enabled="false" />
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptConfig" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptDuplicateUnionOrIntersectionType" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptFieldCanBeMadeReadonly" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptJSXUnresolvedComponent" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptLibrary" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="ERROR" enabled="false" />
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptMissingConfigOption" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptRedundantGenericType" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptSmartCast" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptSuspiciousConstructorParameterAssignment" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptUMDGlobal" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptUnresolvedReference" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="ERROR" enabled="false" />
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptValidateGenericTypes" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="ERROR" enabled="false" />
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="TypeScriptValidateTypes" enabled="true" level="ERROR" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="ERROR" enabled="false" />
      <scope name="glob:**.md" level="ERROR" enabled="false" />
      <scope name="file:buildSrc/*" level="ERROR" enabled="false" />
      <scope name="glob:scope#test:*..*" level="ERROR" enabled="false" />
      <scope name="$gitignore" level="ERROR" enabled="false" />
      <scope name="glob:**.test.ts" level="ERROR" enabled="false" />
      <scope name="glob:**/node_modules/**" level="ERROR" enabled="false" />
      <scope name="glob:.qodana/**" level="ERROR" enabled="false" />
      <scope name="glob:tools/**" level="ERROR" enabled="false" />
      <scope name="glob:tests/**" level="ERROR" enabled="false" />
      <scope name="glob:dist/**" level="ERROR" enabled="false" />
      <scope name="glob:builds/**" level="ERROR" enabled="false" />
      <scope name="glob:buildSrc/**" level="ERROR" enabled="false" />
      <scope name="glob:build/**" level="ERROR" enabled="false" />
      <scope name="glob:vendor/**" level="ERROR" enabled="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryContinueJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryLabelJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryLabelOnBreakStatementJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryLabelOnContinueStatementJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryLocalVariableJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:**.md" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="file:buildSrc/*" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="$gitignore" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:**.test.ts" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:.qodana/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:tools/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:tests/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:dist/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:builds/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:build/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <scope name="glob:vendor/**" level="WARNING" enabled="false">
        <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
        <option name="m_ignoreAnnotatedVariables" value="false" />
      </scope>
      <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
      <option name="m_ignoreAnnotatedVariables" value="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryReturnJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="UnreachableCodeJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="VueDataFunction" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="VueDeprecatedSymbol" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="VueDuplicateTag" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="VueMissingComponentImportInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="VueUnrecognizedDirective" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="VueUnrecognizedSlot" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WEAK WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WEAK WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WEAK WARNING" enabled="false" />
      <scope name="$gitignore" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WEAK WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:build/**" level="WEAK WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="VulnerableLibrariesLocal" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="WebpackConfigHighlighting" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="WithStatementJS" enabled="true" level="WARNING" enabled_by_default="true">
      <scope name="glob:**/resources/**" level="WARNING" enabled="false" />
      <scope name="glob:**.md" level="WARNING" enabled="false" />
      <scope name="file:buildSrc/*" level="WARNING" enabled="false" />
      <scope name="glob:scope#test:*..*" level="WARNING" enabled="false" />
      <scope name="$gitignore" level="WARNING" enabled="false" />
      <scope name="glob:**.test.ts" level="WARNING" enabled="false" />
      <scope name="glob:**/node_modules/**" level="WARNING" enabled="false" />
      <scope name="glob:.qodana/**" level="WARNING" enabled="false" />
      <scope name="glob:tools/**" level="WARNING" enabled="false" />
      <scope name="glob:tests/**" level="WARNING" enabled="false" />
      <scope name="glob:dist/**" level="WARNING" enabled="false" />
      <scope name="glob:builds/**" level="WARNING" enabled="false" />
      <scope name="glob:buildSrc/**" level="WARNING" enabled="false" />
      <scope name="glob:build/**" level="WARNING" enabled="false" />
      <scope name="glob:vendor/**" level="WARNING" enabled="false" />
    </inspection_tool>
  </profile>
</component>