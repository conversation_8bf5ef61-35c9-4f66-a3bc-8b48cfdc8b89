[{"name": "FilePageCache.capacityInBytes", "description": "Cache capacity, configured on application startup", "unit": "bytes", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 629145600}]}}, {"name": "DirectByteBufferAllocator.reclaimed", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 40}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.maxRegisteredFiles", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 932}]}}, {"name": "FilePageCache.totalPageLoadsUs", "description": "", "unit": "us", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "StorageLockContext.competingThreads.90P", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "FilePageCache.maxCacheSizeInBytes", "description": "Max size of all cached pages observed since application start", "unit": "bytes", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 482344960}]}}, {"name": "FilePageCache.uncachedFileAccess", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 38}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FileChannelInterruptsRetryer.totalRetriedAttempts", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DirectByteBufferAllocator.totalSizeOfBuffersCachedInBytes", "description": "", "unit": "bytes", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 41943040}]}}, {"name": "DirectByteBufferAllocator.misses", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.pageLoadsAboveSizeThreshold", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DirectByteBufferAllocator.totalSizeOfBuffersAllocatedInBytes", "description": "", "unit": "bytes", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 41943040}]}}, {"name": "FilePageCache.pageHits", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "StorageLockContext.competingThreads.avg", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "DirectByteBufferAllocator.hits", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.totalPageDisposalsUs", "description": "", "unit": "us", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DirectByteBufferAllocator.disposed", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 420}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.disposedBuffers", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 460}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.pageLoads", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.pageFastCacheHits", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 52400}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "StorageLockContext.competingThreads.max", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "workspaceModel.moduleManagerBridge.get.modules.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.to.snapshot.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.orphan.listener.update.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.put.entity.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleBridge.before.changed.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.set.unloadedModules.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.artifact.entities.serializer.save.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.new.nonPersistent.module.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.create.module.instance.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.initialize.library.bridges.after.loading.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.resolve.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.save.cache.to.file.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.facet.change.listener.process.change.events.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.artifact.entities.serializer.load.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleBridgeLoader.loading.modules.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.reload.project.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedChild.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewParent.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.load.cache.metadata.from.file.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.mutable.vfurl.index.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.delayed.project.synchronizer.sync.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.get.library.by.name.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.global.apply.state.to.project.builder.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.library.entities.serializer.save.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedParent.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.replace.by.source.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.get.library.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.facet.change.listener.before.change.events.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.build.module.graph.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.app.storage.content.writer.save.component.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.app.storage.content.reader.load.component.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.remove.entity.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.module.iml.entities.serializer.save.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.get.libraries.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.module.iml.entities.serializer.load.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.project.serializers.save.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.load.all.modules.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.apply.loaded.storage.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.handle.before.change.events.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.sync.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.save.global.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleBridge.facet.initialization.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.library.entities.serializer.load.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.storage.jps.conf.reader.load.component.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.load.initial.state.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.apply.changes.from.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.add.entity.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.mutable.ext.mapping.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.newModule.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.load.project.to.empty.storage.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.collect.changes.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.do.save.caches.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.referrers.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.load.module.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleBridge.update.option.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.facet.change.listener.init.bridge.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.global.updates.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.instances.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.project.serializers.load.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.has.same.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.initialize.library.bridges.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.entities.by.source.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.load.cache.from.file.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.handle.changed.events.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.modify.entity.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.global.updates.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.global.apply.state.to.project.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewChild.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.module.dependency.index.workspace.model.listener.on.changed.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.save.changed.project.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalCpuTimeMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 102970}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalSafepointCount", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 79}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.threadCount", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 64}]}}, {"name": "JVM.maxThreadCount", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 116}]}}, {"name": "JVM.GC.collections", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 25}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalTimeToSafepointsMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 28}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalBytesAllocated", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 16879804824}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalTimeAtSafepointsMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1929}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "MEM.ramMinusFileMappingsBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 3563184128}]}}, {"name": "JVM.newThreadsCount", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 30}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalDirectByteBuffersBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 50886245}]}}, {"name": "JVM.usedNativeBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 450429632}]}}, {"name": "MEM.ramBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 4002344960}]}}, {"name": "MEM.ramPlusSwapMinusFileMappingsBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 3563184128}]}}, {"name": "JVM.committedHeapBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 2340421632}]}}, {"name": "JVM.usedHeapBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 998244344}]}}, {"name": "JVM.maxHeapBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 5834276864}]}}, {"name": "OS.loadAverage", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 4.0205078125}]}}, {"name": "MEM.fileMappingsRamBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 439160832}]}}, {"name": "JVM.GC.collectionTimesMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1774}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.initializing.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.loading.from.cache.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.instances.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValueWithParameters.from.cache.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValueWithParameters.total.get.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexContributor.registerFileSets.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.collect.changes.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.on.changed.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.pre.handlers.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.replace.project.model.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.visitFileSets.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.init.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValueWithParameters.clear.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValue.clear.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValue.calculated.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.updates.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValueWithParameters.calculated.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.updates.precise.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.getPackageName.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.entityStorageSnapshotImpl.instances.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.update.unloaded.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.to.snapshot.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValue.total.get.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 245}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.markDirty.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.loading.total.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.getFileInfo.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 583}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.updateDirtyEntities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.updates.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.onEntitiesChanged.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.check.recursive.update.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValue.from.cache.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 245}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.on.before.changed.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.getDirectoriesByPackageName.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.full.replace.project.model.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.init.bridges.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.processFileSets.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "AWTEventQueue.dispatchTimeTotalNS", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 186026598}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "NonBlockingReadAction.finalizedExecutionTimeUs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 291970}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "NonBlockingReadAction.failedExecutionTimeUs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "NonBlockingReadAction.failedExecutionsCount", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "NonBlockingReadAction.finalizedExecutionsCount", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1921}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.entries.lookups", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 20540}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.writer_2.totalTimeSpentWritingMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.writer_0.totalTimeSpentWritingMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.allKeys.lookupDurationMaxMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexing.writer_3.totalTimeSpentWritingMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.cache.totalCacheEvicted", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 70431}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.entries.lookupDuration90PMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexing.writesQueuedAvg", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexing.cache.totalCacheAccesses", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 581254}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.entries.lookupDurationMaxMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexes.stubs.lookups", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 146750}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.stubs.lookupDuration90PMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexes.allKeys.lookupDuration90PMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexes.allKeys.lookups", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 3}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.stubs.lookupDurationAvgMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexes.entries.lookupDurationAvgMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexing.cache.totalCacheMisses", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 63721}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.totalTimeIndexersSleptMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.writesQueuedMin", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "Indexing.writer_1.totalTimeSpentWritingMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.allKeys.lookupDurationAvgMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexing.writesQueuedMax", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "Indexes.stubs.lookupDurationMaxMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "cacheStateStorage.set.counter", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 5}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "cacheStateStorage.get.counter", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "cacheStateStorage.set.duration", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "cacheStateStorage.get.duration", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.fileChildByName", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1098563}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.invertedFileNameIndex.requests", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.fileByIdCache.misses", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 153838}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.fileByIdCache.hits", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.yaml.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 333259}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.TypeScript.JSX.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 9796418}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.JSRegexp.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 388}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.JSRegexp.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 7010278}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.Shell.Script.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 29763}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.SVG.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 953352}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.Shell.Script.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 29763}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DiskQueryRelay.taskExecutionTotalTimeUs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 3564}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.TypeScript.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 675934}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.TypeScript.JSX.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 102712}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.GenericSQL.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 3301}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.SVG.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 6461}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.CSS.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 2860709}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.IgnoreLang.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1511}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.TypeScript.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 503970}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.HTML.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 25156}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DiskQueryRelay.tasksExecuted", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 37}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.textmate.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 67590}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.HTML.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 4871594}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.TypeScript.JSX.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 161897}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DiskQueryRelay.taskWaitingTotalTimeUs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 56258}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.GenericSQL.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 3296}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.Shell.Script.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 236953171}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.SVG.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 6461}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.IgnoreLang.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1907747}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.IgnoreLang.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 548269}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.ECMAScript.6.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1473693}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.TypeScript.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 31516280}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.JSON.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 747947541}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.ECMAScript.6.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 211390119}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.HTML.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 25156}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "writeAction.count", "description": "Number of write actions run", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 3}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.yaml.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 811108}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.HTML.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 4380348}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "writeAction.max.wait.ms", "description": "Max time waiting for the write lock", "unit": "ms", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 32}]}}, {"name": "lexer.CSS.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 39760}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.CSS.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 39760}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.IgnoreLang.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1511}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DiskQueryRelay.tasksRequested", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 37}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.JSON.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 2668660}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "writeAction.wait.ms", "description": "Total time waiting for the write lock", "unit": "ms", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 2}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.JSON.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 94488795}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.GenericSQL.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 51606125}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.GenericSQL.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 10119992}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.SVG.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 6495865}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.yaml.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 2011}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.TypeScript.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 219321276}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.CSS.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 15818723}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.textmate.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 67590}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.ECMAScript.6.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 1131671}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "writeAction.median.wait.ms", "description": "Median time waiting for the write lock, step 100 ms", "unit": "ms", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "lexer.ECMAScript.6.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 97274047}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.JSRegexp.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 707826}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.textmate.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 466457}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.JSRegexp.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 388}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.TypeScript.JSX.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 32883020}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.yaml.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 2011}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.textmate.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 100337}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.Shell.Script.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 9065413}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.JSON.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271685484000000, "epochNanos": 1755271740249000000, "attributes": {"data": [], "empty": true}, "value": 2668660}], "monotonic": true, "aggregationTemporality": "DELTA"}}]