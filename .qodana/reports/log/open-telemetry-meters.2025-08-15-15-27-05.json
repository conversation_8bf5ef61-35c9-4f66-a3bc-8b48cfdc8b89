[{"name": "FilePageCache.capacityInBytes", "description": "Cache capacity, configured on application startup", "unit": "bytes", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 629145600}]}}, {"name": "MappedFileStorage.storages", "description": "MappedFileStorage instances in operation at the moment", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 16}]}}, {"name": "StreamlinedBlobStorage.recordsAllocated", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [{"type": "STRING", "key": "file", "keyUtf8": "ZmlsZQ=="}, "attributes.dat"], "empty": false}, "value": 1492}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DirectByteBufferAllocator.reclaimed", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 3}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.maxRegisteredFiles", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 932}]}}, {"name": "FilePageCache.totalPageLoadsUs", "description": "", "unit": "us", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1410854}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "StorageLockContext.competingThreads.90P", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "StreamlinedBlobStorage.recordsRelocated", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [{"type": "STRING", "key": "file", "keyUtf8": "ZmlsZQ=="}, "attributes.dat"], "empty": false}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "MappedFileStorage.totalPagesMapped", "description": "MappedFileStorage.Page instances in operation at the moment", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 16}]}}, {"name": "FilePageCache.maxCacheSizeInBytes", "description": "Max size of all cached pages observed since application start", "unit": "bytes", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 477102080}]}}, {"name": "FilePageCache.uncachedFileAccess", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 548}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FileChannelInterruptsRetryer.totalRetriedAttempts", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DirectByteBufferAllocator.totalSizeOfBuffersCachedInBytes", "description": "", "unit": "bytes", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "DirectByteBufferAllocator.misses", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 455}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.pageLoadsAboveSizeThreshold", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DirectByteBufferAllocator.totalSizeOfBuffersAllocatedInBytes", "description": "", "unit": "bytes", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 477102080}]}}, {"name": "FilePageCache.pageHits", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "StorageLockContext.competingThreads.avg", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "DirectByteBufferAllocator.hits", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 3}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.totalPageDisposalsUs", "description": "", "unit": "us", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 4781}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DirectByteBufferAllocator.disposed", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.disposedBuffers", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 3}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.pageLoads", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 458}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FilePageCache.pageFastCacheHits", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 788830}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "StreamlinedBlobStorage.totalLiveRecordsPayloadBytes", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [{"type": "STRING", "key": "file", "keyUtf8": "ZmlsZQ=="}, "attributes.dat"], "empty": false}, "value": 273159}], "monotonic": false, "aggregationTemporality": "DELTA"}}, {"name": "StreamlinedBlobStorage.recordsDeleted", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [{"type": "STRING", "key": "file", "keyUtf8": "ZmlsZQ=="}, "attributes.dat"], "empty": false}, "value": 27}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "StreamlinedBlobStorage.totalLiveRecordsCapacityBytes", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [{"type": "STRING", "key": "file", "keyUtf8": "ZmlsZQ=="}, "attributes.dat"], "empty": false}, "value": 416748}], "monotonic": false, "aggregationTemporality": "DELTA"}}, {"name": "MappedFileStorage.totalTimeSpentOnMappingUs", "description": "Total time (us) spent inside Page.map() method (file expansion/zeroing, + mmap)", "unit": "us", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 14974}]}}, {"name": "StorageLockContext.competingThreads.max", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "MappedFileStorage.totalBytesMapped", "description": "Total size of MappedByteBuffers in use by MappedFileStorage at the moment", "unit": "bytes", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 202637312}]}}, {"name": "workspaceModel.moduleManagerBridge.get.modules.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.to.snapshot.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 3}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.orphan.listener.update.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 4}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.put.entity.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 28}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleBridge.before.changed.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.set.unloadedModules.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.artifact.entities.serializer.save.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.new.nonPersistent.module.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.create.module.instance.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.initialize.library.bridges.after.loading.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.resolve.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.save.cache.to.file.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 250}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.facet.change.listener.process.change.events.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.artifact.entities.serializer.load.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleBridgeLoader.loading.modules.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 210}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.reload.project.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 97}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedChild.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewParent.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 7}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.load.cache.metadata.from.file.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 25}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.mutable.vfurl.index.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.delayed.project.synchronizer.sync.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 5443}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.get.library.by.name.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.global.apply.state.to.project.builder.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.library.entities.serializer.save.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedParent.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.replace.by.source.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 97}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.get.library.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.facet.change.listener.before.change.events.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.build.module.graph.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.app.storage.content.writer.save.component.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.app.storage.content.reader.load.component.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.remove.entity.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.module.iml.entities.serializer.save.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.get.libraries.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.module.iml.entities.serializer.load.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 64}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.project.serializers.save.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.load.all.modules.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 46}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.apply.loaded.storage.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 5310}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.handle.before.change.events.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.sync.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.save.global.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 7}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleBridge.facet.initialization.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.library.entities.serializer.load.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.storage.jps.conf.reader.load.component.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 8}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.load.initial.state.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 17}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.apply.changes.from.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 33}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.add.entity.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 104}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.mutable.ext.mapping.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.newModule.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.load.project.to.empty.storage.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 123}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.collect.changes.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.do.save.caches.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 283}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.referrers.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleManagerBridge.load.module.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.moduleBridge.update.option.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.facet.change.listener.init.bridge.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.global.updates.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.instances.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 50}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.project.serializers.load.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 118}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.has.same.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.initialize.library.bridges.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.entities.by.source.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.load.cache.from.file.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 174}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.global.handle.changed.events.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.modify.entity.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 11}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.global.updates.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 7}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.global.apply.state.to.project.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 53}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewChild.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 12}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.module.dependency.index.workspace.model.listener.on.changed.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "jps.save.changed.project.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalCpuTimeMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 118050}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalSafepointCount", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 240}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.threadCount", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 114}]}}, {"name": "JVM.maxThreadCount", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 116}]}}, {"name": "JVM.GC.collections", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 65}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalTimeToSafepointsMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 101}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalBytesAllocated", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 4785946288}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalTimeAtSafepointsMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1787}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "MEM.ramMinusFileMappingsBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2247626752}]}}, {"name": "JVM.newThreadsCount", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 147}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "JVM.totalDirectByteBuffersBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 490160145}]}}, {"name": "JVM.usedNativeBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 392987136}]}}, {"name": "MEM.ramBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2669096960}]}}, {"name": "MEM.ramPlusSwapMinusFileMappingsBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2247626752}]}}, {"name": "JVM.committedHeapBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1149239296}]}}, {"name": "JVM.usedHeapBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 889192448}]}}, {"name": "JVM.maxHeapBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 5834276864}]}}, {"name": "OS.loadAverage", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 3.28466796875}]}}, {"name": "MEM.fileMappingsRamBytes", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 421470208}]}}, {"name": "JVM.GC.collectionTimesMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1467}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.initializing.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.loading.from.cache.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 179}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.instances.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValueWithParameters.from.cache.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValueWithParameters.total.get.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexContributor.registerFileSets.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 259}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.collect.changes.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.on.changed.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 36}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.pre.handlers.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.replace.project.model.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.visitFileSets.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 8}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.init.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 80}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValueWithParameters.clear.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValue.clear.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValue.calculated.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 17}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.updates.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 206}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValueWithParameters.calculated.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.updates.precise.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 19}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.getPackageName.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.entityStorageSnapshotImpl.instances.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 35}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.update.unloaded.entities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.to.snapshot.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValue.total.get.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 137}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.markDirty.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.loading.total.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 190}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.getFileInfo.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 391}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.updateDirtyEntities.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.updates.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 6}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.onEntitiesChanged.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 106}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.check.recursive.update.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.cachedValue.from.cache.count", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 120}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.on.before.changed.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 40}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.getDirectoriesByPackageName.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.full.replace.project.model.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 14}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.init.bridges.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "workspaceModel.workspaceFileIndexData.processFileSets.ms", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "AWTEventQueue.dispatchTimeTotalNS", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 7091103724}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "AWTEventQueue.dispatchTime90PNs", "description": "", "unit": "ns", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "AWTEventQueue.dispatchTimeAvgNs", "description": "", "unit": "ns", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "FlushQueue.queueSizeMax", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "FlushQueue.executionTime90PNs", "description": "", "unit": "ns", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "NonBlockingReadAction.finalizedExecutionTimeUs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 627397}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FlushQueue.executionTimeMaxNs", "description": "", "unit": "ns", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "FlushQueue.waitingTimeAvgNs", "description": "", "unit": "ns", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "ReadAction.executionsCount", "description": "Total read actions executed", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 53204}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "AWTEventQueue.eventsDispatched", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "FlushQueue.waitingTime90PNs", "description": "", "unit": "ns", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "WriteAction.executionsCount", "description": "Total write actions executed", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 37}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "AWTEventQueue.dispatchTimeMaxNs", "description": "", "unit": "ns", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "FlushQueue.waitingTimeMaxNs", "description": "", "unit": "ns", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "NonBlockingReadAction.failedExecutionTimeUs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 36776}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "NonBlockingReadAction.failedExecutionsCount", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "NonBlockingReadAction.finalizedExecutionsCount", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 27}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FlushQueue.tasksExecuted", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "FlushQueue.executionTimeAvgNs", "description": "", "unit": "ns", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "FlushQueue.queueSize90P", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "FlushQueue.queueSizeAvg", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexes.entries.lookups", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1732}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.writer_2.totalTimeSpentWritingMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.writer_0.totalTimeSpentWritingMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.allKeys.lookupDurationMaxMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexing.writer_3.totalTimeSpentWritingMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.cache.totalCacheEvicted", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 331941}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.entries.lookupDuration90PMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexing.cache.totalCacheAccesses", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1183121}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.entries.lookupDurationMaxMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexes.stubs.lookups", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 4768}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.stubs.lookupDuration90PMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexes.allKeys.lookupDuration90PMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexes.allKeys.lookups", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.stubs.lookupDurationAvgMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.05263157894736842}]}}, {"name": "Indexes.entries.lookupDurationAvgMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexing.cache.totalCacheMisses", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 333612}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.totalTimeIndexersSleptMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexing.writesQueuedMin", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 9223372036854775807}]}}, {"name": "Indexing.writer_1.totalTimeSpentWritingMs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "Indexes.allKeys.lookupDurationAvgMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0.0}]}}, {"name": "Indexing.writesQueuedMax", "description": "", "unit": "", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "Indexes.stubs.lookupDurationMaxMs", "description": "", "unit": "", "type": "DOUBLE_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1.0}]}}, {"name": "cacheStateStorage.set.counter", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 13}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "cacheStateStorage.get.counter", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 12}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "cacheStateStorage.set.duration", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 89}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "cacheStateStorage.get.duration", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.contentStorage.recordsDeduplicated", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 340}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.fileChildByName", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 165375}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.contentStorage.recordsStoredSize", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 773538}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FileNameCache.totalMisses", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 704}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.contentStorage.recordsStored", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 126}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "FileNameCache.queries", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2189888}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.invertedFileNameIndex.requests", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.contentStorage.recordsUncompressedSize", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1834251}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.fileByIdCache.misses", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 20301}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.fileByIdCache.hits", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 65}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.contentStorage.recordsReadDecompressed", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 114}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.contentStorage.recordsStoredCompressed", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 23}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.contentStorage.recordsDecompressionTimeUs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 72791}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.contentStorage.recordsRead", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 266}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "VFS.contentStorage.recordsReadSize", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 6412671}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.HTML.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1114738}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "writeAction.count", "description": "Number of write actions run", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 37}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.yaml.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 6889990}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.yaml.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1673397}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.HTML.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 50667308}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "writeAction.max.wait.ms", "description": "Max time waiting for the write lock", "unit": "ms", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 32}]}}, {"name": "lexer.TypeScript.JSX.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 70969330}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.CSS.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 753296}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.Shell.Script.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 3898}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.CSS.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 753296}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.Shell.Script.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 3898}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DiskQueryRelay.tasksRequested", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 3592}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DiskQueryRelay.taskExecutionTotalTimeUs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1269142}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.TypeScript.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 21141704}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.TypeScript.JSX.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 228573}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.CSS.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 90013449}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.JSON.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 136676}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "writeAction.wait.ms", "description": "Total time waiting for the write lock", "unit": "ms", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 84}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.JSON.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 8535286}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.TypeScript.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 18250135}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.HTML.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1114738}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DiskQueryRelay.tasksExecuted", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 3592}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.HTML.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 142437484}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.TypeScript.JSX.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 357185}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "DiskQueryRelay.taskWaitingTotalTimeUs", "description": "", "unit": "", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 1557672}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.yaml.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 7390}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.Shell.Script.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 37427664}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.TypeScript.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 4461244513}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.CSS.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 285313315}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.ECMAScript.6.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 2323002}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "writeAction.median.wait.ms", "description": "Median time waiting for the write lock, step 100 ms", "unit": "ms", "type": "LONG_GAUGE", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 0}]}}, {"name": "parser.ECMAScript.6.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 6325970}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.ECMAScript.6.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 155720607}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.TypeScript.JSX.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 197731068}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.yaml.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 7390}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.Shell.Script.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 640340}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.TypeScript.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 937359919}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.JSON.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 110047727}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "parser.ECMAScript.6.time.ns", "description": "", "unit": "ns", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 844588792}], "monotonic": true, "aggregationTemporality": "DELTA"}}, {"name": "lexer.JSON.size.bytes", "description": "", "unit": "byte", "type": "LONG_SUM", "data": {"points": [{"startEpochNanos": 1755271565444000000, "epochNanos": 1755271625516000000, "attributes": {"data": [], "empty": true}, "value": 136676}], "monotonic": true, "aggregationTemporality": "DELTA"}}]