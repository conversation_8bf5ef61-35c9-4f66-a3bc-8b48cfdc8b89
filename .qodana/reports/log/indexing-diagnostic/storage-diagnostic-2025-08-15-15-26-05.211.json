{"pageCacheStats": {"cachedChannelsStatistics": {"hit": 56, "miss": 1, "load": 99, "capacity": 400}, "uncachedFileAccess": 652, "maxRegisteredFiles": 932, "maxCacheSizeInBytes": 482344960, "totalCachedSizeInBytes": 482344960, "pageHits": 0, "pageFastCacheHits": 856878, "pageLoadsAboveSizeThreshold": 0, "regularPageLoads": 463, "disposedBuffers": 3, "totalPageDisposalUs": 4795, "totalPageLoadUs": 1422395, "totalPagesLoaded": 463, "capacityInBytes": 629145600}, "indexStorageStats": {"indexStoragesStats": {"CompassFunctionsIndex": {"statsPerPhm": {"CompassFunctionsIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "CompassFunctionsIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "Css.ampersand.selector": {"statsPerPhm": {"Css.ampersand.selector.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "Css.custom.variable": {"statsPerPhm": {"Css.custom.variable.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1429, "dataFileSizeInBytes": 40906, "storageSizeInBytes": 11576, "btreeStatistics": {"pages": 1, "elements": 1429, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 7, "searchRequests": 2859, "searchSteps": 818, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 8462}}}, "CssIndex": {"statsPerPhm": {"CssIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 492, "dataFileSizeInBytes": 3806, "storageSizeInBytes": 4064, "btreeStatistics": {"pages": 1, "elements": 492, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 2, "searchRequests": 990, "searchSteps": 68, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 7671}, "CssIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 12, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 44, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 10143}}}, "DomFileIndex": {"statsPerPhm": {"DomFileIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 7, "dataFileSizeInBytes": 205, "storageSizeInBytes": 184, "btreeStatistics": {"pages": 1, "elements": 7, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 13, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 127}, "DomFileIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 15, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 43, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 1042}}}, "FrameworkDetectionIndex": {"statsPerPhm": {".perFileVersion/indexed_versions/indexed_versions": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 12, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 2, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 55}, "FrameworkDetectionIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "FrameworkDetectionIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "HashFragmentIndex": {"statsPerPhm": {".perFileVersion/indexed_versions/indexed_versions": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 4, "dataFileSizeInBytes": 279, "storageSizeInBytes": 160, "btreeStatistics": {"pages": 1, "elements": 4, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 15, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 73}, "HashFragmentIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 3, "elements": 6936, "height": 2, "moves": 0, "leafPages": 2, "maxSearchStepsInRequest": 54, "searchRequests": 22263, "searchSteps": 32213, "pageCapacity": 32768, "sizeInBytes": 98304}}, "valueStorageSizeInBytes": 115016}, "HashFragmentIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 118, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 547, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 107723}}}, "HtmlScriptSrcIndex": {"statsPerPhm": {"HtmlScriptSrcIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 4, "dataFileSizeInBytes": 144, "storageSizeInBytes": 160, "btreeStatistics": {"pages": 1, "elements": 4, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 22, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 80}, "HtmlScriptSrcIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 4, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 18, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 281}}}, "HtmlTagIdIndex": {"statsPerPhm": {"HtmlTagIdIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 15, "dataFileSizeInBytes": 240, "storageSizeInBytes": 248, "btreeStatistics": {"pages": 1, "elements": 15, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 29, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 155}, "HtmlTagIdIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 3, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 16, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 341}}}, "IdIndex": {"statsPerPhm": {".perFileVersion/indexed_versions/indexed_versions": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 19, "dataFileSizeInBytes": 1172, "storageSizeInBytes": 280, "btreeStatistics": {"pages": 1, "elements": 19, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 73, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 163}, "IdIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 58, "elements": 132721, "height": 2, "moves": 777, "leafPages": 57, "maxSearchStepsInRequest": 91, "searchRequests": 495500, "searchSteps": 756863, "pageCapacity": 32768, "sizeInBytes": 1900544}}, "valueStorageSizeInBytes": 1709416}, "IdIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 2430, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 9, "searchRequests": 7741, "searchSteps": 3973, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 1807148}}}, "JS.base.types.index": {"statsPerPhm": {"JS.base.types.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 54, "dataFileSizeInBytes": 337, "storageSizeInBytes": 560, "btreeStatistics": {"pages": 1, "elements": 54, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 781, "searchSteps": 2, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 358}}}, "JS.class.implements": {"statsPerPhm": {"JS.class.implements.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 54, "dataFileSizeInBytes": 679, "storageSizeInBytes": 560, "btreeStatistics": {"pages": 1, "elements": 54, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 114, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 671}}}, "JS.framework.markers.index": {"statsPerPhm": {"JS.framework.markers.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 24, "dataFileSizeInBytes": 520, "storageSizeInBytes": 320, "btreeStatistics": {"pages": 1, "elements": 24, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 435, "searchSteps": 1, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 316}}}, "Less.mixins": {"statsPerPhm": {"Less.mixins.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "Less.variables": {"statsPerPhm": {"Less.variables.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "RelaxSymbolIndex": {"statsPerPhm": {"RelaxSymbolIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "RelaxSymbolIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "SchemaTypeInheritance": {"statsPerPhm": {"SchemaTypeInheritance.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 62, "dataFileSizeInBytes": 2781, "storageSizeInBytes": 624, "btreeStatistics": {"pages": 1, "elements": 62, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 123, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 12976}, "SchemaTypeInheritance_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 9, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 27, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 19356}}}, "Scss.function": {"statsPerPhm": {"Scss.function.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 124, "dataFileSizeInBytes": 1087, "storageSizeInBytes": 1120, "btreeStatistics": {"pages": 1, "elements": 124, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 247, "searchSteps": 8, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 830}}}, "Scss.placeholder.selector": {"statsPerPhm": {"Scss.placeholder.selector.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "Scss.variable": {"statsPerPhm": {"Scss.variable.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 71, "dataFileSizeInBytes": 469, "storageSizeInBytes": 696, "btreeStatistics": {"pages": 1, "elements": 71, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 141, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 611}}}, "Stubs": {"statsPerPhm": {".perFileVersion/indexed_versions/indexed_versions": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 8, "dataFileSizeInBytes": 287, "storageSizeInBytes": 192, "btreeStatistics": {"pages": 1, "elements": 8, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 51, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 97}, "Stubs.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 1985, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 7, "searchRequests": 8335, "searchSteps": 3138, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 9262268}, "amd.requirepaths.index/amd.requirePaths.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "angular2.ivy.directive.index/angular2.ivy.directive.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "angular2.ivy.module.index/angular2.ivy.module.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "angular2.ivy.pipe.index/angular2.ivy.pipe.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "angular2.metadata.directive.index/angular2.metadata.directive.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "angular2.metadata.node.index/angular2.metadata.node.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "angular2.templateurl.index/angular2.templateUrl.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "css.attr/Css.attr.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 5, "dataFileSizeInBytes": 42, "storageSizeInBytes": 168, "btreeStatistics": {"pages": 1, "elements": 5, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 9, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 104}, "css.class/Css.class.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 218, "dataFileSizeInBytes": 2387, "storageSizeInBytes": 1872, "btreeStatistics": {"pages": 1, "elements": 218, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 435, "searchSteps": 4, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 1258}, "css.custom.mixin/Css.custom.mixin.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "css.custom.property/Css.custom.property.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "css.id/Css.id.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 5, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 1, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 54}, "dom.elementclass/dom.elementClass.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "dom.namespacekey/dom.namespaceKey.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "es6.exported.index/es6.exported.index.storage": {"persistentEnumeratorStatistics": {"collisions": 2, "values": 12329, "dataFileSizeInBytes": 184401, "storageSizeInBytes": 102416, "btreeStatistics": {"pages": 5, "elements": 12328, "height": 2, "moves": 2, "leafPages": 4, "maxSearchStepsInRequest": 53, "searchRequests": 35987, "searchSteps": 52396, "pageCapacity": 32768, "sizeInBytes": 163840}}, "valueStorageSizeInBytes": 111142}, "jade.mixin/jade.mixin.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.class.super/JS.class.super.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 337, "dataFileSizeInBytes": 4767, "storageSizeInBytes": 2824, "btreeStatistics": {"pages": 1, "elements": 337, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 2, "searchRequests": 681, "searchSteps": 30, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 2755}, "js.element.qualifiedname/js.element.qualifiedName.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 6, "elements": 14304, "height": 2, "moves": 4, "leafPages": 5, "maxSearchStepsInRequest": 69, "searchRequests": 47880, "searchSteps": 84562, "pageCapacity": 32768, "sizeInBytes": 196608}}, "valueStorageSizeInBytes": 131841}, "js.global.qualified.elements/JS.global.qualified.elements.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 737, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 3, "searchRequests": 2405, "searchSteps": 316, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 4384}, "js.global.symbol.index/js.global.symbol.index.storage": {"persistentEnumeratorStatistics": {"collisions": 74, "values": 13401, "dataFileSizeInBytes": 197245, "storageSizeInBytes": 111640, "btreeStatistics": {"pages": 5, "elements": 13364, "height": 2, "moves": 2, "leafPages": 4, "maxSearchStepsInRequest": 69, "searchRequests": 55942, "searchSteps": 150818, "pageCapacity": 32768, "sizeInBytes": 163840}}, "valueStorageSizeInBytes": 116607}, "js.qualified.shortname/js.qualified.shortName.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.subclass.stub.index/JS.subclass.stub.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 38, "dataFileSizeInBytes": 376, "storageSizeInBytes": 432, "btreeStatistics": {"pages": 1, "elements": 38, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 75, "searchSteps": 2, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 283}, "js.symbol.qualified.names.index/js.symbol.qualified.names.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 17, "elements": 43221, "height": 2, "moves": 50, "leafPages": 16, "maxSearchStepsInRequest": 80, "searchRequests": 187547, "searchSteps": 299929, "pageCapacity": 32768, "sizeInBytes": 557056}}, "valueStorageSizeInBytes": 392676}, "postcss.custom.selector/postcss.custom.selector.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "redis.dml.shortname/redis.dml.shortName.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "scss.mixin/Scss.mixin.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 9, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 1, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 53}, "ts.local.classes/ts.local.classes.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 48, "dataFileSizeInBytes": 527, "storageSizeInBytes": 512, "btreeStatistics": {"pages": 1, "elements": 48, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 95, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 313}, "vue.components.index/vue.components.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 5, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 1, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 54}, "vue.url.index/vue.url.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "vuex.store.index/vuex.store.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "TodoIndex": {"statsPerPhm": {".perFileVersion/indexed_versions/indexed_versions": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "TodoIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "Trigram.Index": {"statsPerPhm": {"Trigram.Index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 28, "elements": 68825, "height": 2, "moves": 212, "leafPages": 27, "maxSearchStepsInRequest": 430, "searchRequests": 288943, "searchSteps": 567900, "pageCapacity": 32768, "sizeInBytes": 917504}}, "valueStorageSizeInBytes": 1519247}, "Trigram.Index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 2447, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 9, "searchRequests": 7775, "searchSteps": 4062, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 1358422}}}, "TypeScriptExternalPathCandidates": {"statsPerPhm": {"TypeScriptExternalPathCandidates.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 26, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 2, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 63}, "TypeScriptExternalPathCandidates_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 4, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 86}}}, "VueComponentStylesIndex": {"statsPerPhm": {"VueComponentStylesIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "VueComponentStylesIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "VueNoScriptFilesIndex": {"statsPerPhm": {"VueNoScriptFilesIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "VueNoScriptFilesIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "WebComponentsIndex": {"statsPerPhm": {"WebComponentsIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "WebComponentsIndex_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "XmlNamespaces": {"statsPerPhm": {"XmlNamespaces.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 9, "dataFileSizeInBytes": 351, "storageSizeInBytes": 200, "btreeStatistics": {"pages": 1, "elements": 9, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 17, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 5255}, "XmlNamespaces_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 14, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 40, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 5754}}}, "XmlTagNames": {"statsPerPhm": {"XmlTagNames.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 190, "dataFileSizeInBytes": 1519, "storageSizeInBytes": 1648, "btreeStatistics": {"pages": 1, "elements": 190, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 379, "searchSteps": 10, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 1660}, "XmlTagNames_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 7, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 22, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 2494}}}, "amd.baseUrl.index": {"statsPerPhm": {"amd.baseUrl.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "angular2.metadata.className.index": {"statsPerPhm": {"angular2.metadata.className.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "angular2.metadata.function.index": {"statsPerPhm": {"angular2.metadata.function.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "angular2.metadata.module.index": {"statsPerPhm": {"angular2.metadata.module.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "angular2.metadata.pipe.index": {"statsPerPhm": {"angular2.metadata.pipe.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "angular2.source.directive.index": {"statsPerPhm": {"angular2.source.directive.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "angular2.source.module.index": {"statsPerPhm": {"angular2.source.module.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "angular2.source.pipe.index": {"statsPerPhm": {"angular2.source.pipe.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "angular2.symbol.index": {"statsPerPhm": {"angular2.symbol.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "css.template.selectors": {"statsPerPhm": {"css.template.selectors.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 133, "dataFileSizeInBytes": 1713, "storageSizeInBytes": 1192, "btreeStatistics": {"pages": 1, "elements": 133, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 265, "searchSteps": 4, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 3979}, "css.template.selectors_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 6, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 22, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 5608}}}, "editorconfig.index.name": {"statsPerPhm": {"editorconfig.index.name.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "editorconfig.index.name_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "es6.assignment.index": {"statsPerPhm": {"es6.assignment.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 1, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 273}}}, "es6.side.effect.import.index": {"statsPerPhm": {"es6.side.effect.import.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 10, "dataFileSizeInBytes": 163, "storageSizeInBytes": 208, "btreeStatistics": {"pages": 1, "elements": 10, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 19, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 158}}}, "fileIncludes": {"statsPerPhm": {".perFileVersion/indexed_versions/indexed_versions": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 3, "dataFileSizeInBytes": 40, "storageSizeInBytes": 152, "btreeStatistics": {"pages": 1, "elements": 3, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 10, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 67}, "fileIncludes.storage": {"persistentEnumeratorStatistics": {"collisions": 2, "values": 853, "dataFileSizeInBytes": 9752, "storageSizeInBytes": 8208, "btreeStatistics": {"pages": 1, "elements": 852, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 3, "searchRequests": 1718, "searchSteps": 186, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 73168}, "fileIncludes_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 1447, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 7, "searchRequests": 13252, "searchSteps": 3088, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 251764}}}, "html5.custom.attributes.index": {"statsPerPhm": {"html5.custom.attributes.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "html5.custom.attributes.index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "http.request.completion.host": {"statsPerPhm": {"http.request.completion.host.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.completion.host_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "http.request.execution.environment": {"statsPerPhm": {"http.request.execution.environment.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 5, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 1, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 150}, "http.request.execution.environment_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 2, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 4, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 158}}}, "http.request.headers.values.index": {"statsPerPhm": {"http.request.headers.values.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.headers.values.index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "http.request.name.index": {"statsPerPhm": {"http.request.name.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.name.index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.amd.modules": {"statsPerPhm": {"js.amd.modules.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.class.index2": {"statsPerPhm": {"js.class.index2.storage": {"persistentEnumeratorStatistics": {"collisions": 2, "values": 4566, "dataFileSizeInBytes": 74169, "storageSizeInBytes": 40816, "btreeStatistics": {"pages": 3, "elements": 4565, "height": 2, "moves": 0, "leafPages": 2, "maxSearchStepsInRequest": 47, "searchRequests": 12820, "searchSteps": 14345, "pageCapacity": 32768, "sizeInBytes": 98304}}, "valueStorageSizeInBytes": 34941}}}, "js.custom.single.entry.index": {"statsPerPhm": {"js.custom.single.entry.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.custom.single.entry.index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.doc.modules": {"statsPerPhm": {"js.doc.modules.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.export.default.react.component.index": {"statsPerPhm": {"js.export.default.react.component.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.exported.index": {"statsPerPhm": {"js.exported.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 2, "dataFileSizeInBytes": 8, "storageSizeInBytes": 144, "btreeStatistics": {"pages": 1, "elements": 2, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 5, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 1937}}}, "js.gcl.modules": {"statsPerPhm": {"js.gcl.modules.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.generics.index": {"statsPerPhm": {"js.generics.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 13, "dataFileSizeInBytes": 137, "storageSizeInBytes": 232, "btreeStatistics": {"pages": 1, "elements": 13, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 29, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 115}}}, "js.implicit.elements.index": {"statsPerPhm": {"js.implicit.elements.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 9, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 2880, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 157}, "js.implicit.elements.index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 6, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 22, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 396}}}, "js.imported.bindings.index": {"statsPerPhm": {"js.imported.bindings.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1762, "dataFileSizeInBytes": 25416, "storageSizeInBytes": 14248, "btreeStatistics": {"pages": 1, "elements": 1762, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 7, "searchRequests": 3541, "searchSteps": 1068, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 22543}}}, "js.lazy.packages": {"statsPerPhm": {"js.lazy.packages.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.local.namespace.members.index": {"statsPerPhm": {"js.local.namespace.members.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 643, "dataFileSizeInBytes": 8464, "storageSizeInBytes": 5280, "btreeStatistics": {"pages": 1, "elements": 643, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 3, "searchRequests": 1300, "searchSteps": 114, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 3907}}}, "js.namespace.index": {"statsPerPhm": {"js.namespace.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 3939, "dataFileSizeInBytes": 82107, "storageSizeInBytes": 31696, "btreeStatistics": {"pages": 3, "elements": 3938, "height": 2, "moves": 0, "leafPages": 2, "maxSearchStepsInRequest": 46, "searchRequests": 11571, "searchSteps": 13252, "pageCapacity": 32768, "sizeInBytes": 98304}}, "valueStorageSizeInBytes": 28990}}}, "js.nonglobal.symbol.index": {"statsPerPhm": {"js.nonglobal.symbol.index.storage": {"persistentEnumeratorStatistics": {"collisions": 26, "values": 11835, "dataFileSizeInBytes": 154032, "storageSizeInBytes": 99088, "btreeStatistics": {"pages": 5, "elements": 11822, "height": 2, "moves": 1, "leafPages": 4, "maxSearchStepsInRequest": 53, "searchRequests": 37884, "searchSteps": 51977, "pageCapacity": 32768, "sizeInBytes": 163840}}, "valueStorageSizeInBytes": 115554}}}, "js.package.index": {"statsPerPhm": {"js.package.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.package.index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.string.literal.words.index": {"statsPerPhm": {"js.string.literal.words.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 4020, "dataFileSizeInBytes": 44122, "storageSizeInBytes": 32344, "btreeStatistics": {"pages": 3, "elements": 4020, "height": 2, "moves": 0, "leafPages": 2, "maxSearchStepsInRequest": 42, "searchRequests": 12277, "searchSteps": 13854, "pageCapacity": 32768, "sizeInBytes": 98304}}, "valueStorageSizeInBytes": 102430}, "js.string.literal.words.index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 1540, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 7, "searchRequests": 5292, "searchSteps": 1333, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 208288}}}, "js.symbol.index2": {"statsPerPhm": {"js.symbol.index2.storage": {"persistentEnumeratorStatistics": {"collisions": 1666, "values": 28572, "dataFileSizeInBytes": 400492, "storageSizeInBytes": 245528, "btreeStatistics": {"pages": 12, "elements": 27623, "height": 2, "moves": 25, "leafPages": 11, "maxSearchStepsInRequest": 72, "searchRequests": 107541, "searchSteps": 160492, "pageCapacity": 32768, "sizeInBytes": 393216}}, "valueStorageSizeInBytes": 316978}}}, "js.test.names": {"statsPerPhm": {"js.test.names.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.test.names_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.test.names.extensible": {"statsPerPhm": {"js.test.names.extensible.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.test.names.extensible_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "js.typedef.index2": {"statsPerPhm": {"js.typedef.index2.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 32, "dataFileSizeInBytes": 401, "storageSizeInBytes": 384, "btreeStatistics": {"pages": 1, "elements": 32, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 1687, "searchSteps": 16, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 235}}}, "js.used.remote.modules": {"statsPerPhm": {"js.used.remote.modules.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "json.file.root.values": {"statsPerPhm": {"json.file.root.values.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 3, "dataFileSizeInBytes": 63, "storageSizeInBytes": 152, "btreeStatistics": {"pages": 1, "elements": 3, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 8, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 728}, "json.file.root.values_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 113, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 30969, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 8516}}}, "postcss.custom.media": {"statsPerPhm": {"postcss.custom.media.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "sql.column.name": {"statsPerPhm": {"sql.column.name.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "sql.names": {"statsPerPhm": {"sql.names.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "sql.routine.index": {"statsPerPhm": {"sql.routine.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "sql.routine.index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "sql.table.shortName": {"statsPerPhm": {"sql.table.shortName.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "sql.types.count.index": {"statsPerPhm": {"sql.types.count.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "sql.types.count.index_inputs": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "ts.embedded.content.index": {"statsPerPhm": {"ts.embedded.content.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "ts.external.module.index": {"statsPerPhm": {"ts.external.module.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 2, "dataFileSizeInBytes": 8, "storageSizeInBytes": 144, "btreeStatistics": {"pages": 1, "elements": 2, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 27, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 128}}}, "ts.external.module.name.index": {"statsPerPhm": {"ts.external.module.name.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 111, "dataFileSizeInBytes": 1502, "storageSizeInBytes": 1016, "btreeStatistics": {"pages": 1, "elements": 111, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 376, "searchSteps": 11, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 622}}}, "ts.global.exported.index": {"statsPerPhm": {"ts.global.exported.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "vue.composition.app.index": {"statsPerPhm": {"vue.composition.app.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "vue.extends.binding.index": {"statsPerPhm": {"vue.extends.binding.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 6, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 2, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 73}}}, "vue.global.directives.index": {"statsPerPhm": {"vue.global.directives.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "vue.global.filters.index": {"statsPerPhm": {"vue.global.filters.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "vue.id.index": {"statsPerPhm": {"vue.id.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "vue.mixin.binding.index": {"statsPerPhm": {"vue.mixin.binding.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}, "vue.options.index": {"statsPerPhm": {"vue.options.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}}}, "otherStoragesStats": {"statsPerPhm": {"shared_indexes/sih.CompassFunctionsIndex/sih.CompassFunctionsIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.CssIndex/sih.CssIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.DomFileIndex/sih.DomFileIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.FrameworkDetectionIndex/sih.FrameworkDetectionIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.HashFragmentIndex/sih.HashFragmentIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.HtmlScriptSrcIndex/sih.HtmlScriptSrcIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.HtmlTagIdIndex/sih.HtmlTagIdIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.IdIndex/sih.IdIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 113, "dataFileSizeInBytes": 565, "storageSizeInBytes": 1032, "btreeStatistics": {"pages": 1, "elements": 112, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 223, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 542}, "shared_indexes/sih.RelaxSymbolIndex/sih.RelaxSymbolIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.SchemaTypeInheritance/sih.SchemaTypeInheritance_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.Stubs/sih.Stubs_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 113, "dataFileSizeInBytes": 565, "storageSizeInBytes": 1032, "btreeStatistics": {"pages": 1, "elements": 112, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 1365, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 542}, "shared_indexes/sih.TodoIndex/sih.TodoIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.Trigram.Index/sih.Trigram.Index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 113, "dataFileSizeInBytes": 565, "storageSizeInBytes": 1032, "btreeStatistics": {"pages": 1, "elements": 112, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 223, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 542}, "shared_indexes/sih.TypeScriptExternalPathCandidates/sih.TypeScriptExternalPathCandidates_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.VueComponentStylesIndex/sih.VueComponentStylesIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.VueNoScriptFilesIndex/sih.VueNoScriptFilesIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.WebComponentsIndex/sih.WebComponentsIndex_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.XmlNamespaces/sih.XmlNamespaces_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.XmlTagNames/sih.XmlTagNames_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.css.template.selectors/sih.css.template.selectors_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.editorconfig.index.name/sih.editorconfig.index.name_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.fileIncludes/sih.fileIncludes_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.html5.custom.attributes.index/sih.html5.custom.attributes.index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.http.request.completion.host/sih.http.request.completion.host_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.http.request.execution.environment/sih.http.request.execution.environment_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.http.request.headers.values.index/sih.http.request.headers.values.index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.http.request.name.index/sih.http.request.name.index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.js.custom.single.entry.index/sih.js.custom.single.entry.index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.js.implicit.elements.index/sih.js.implicit.elements.index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.js.package.index/sih.js.package.index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.js.string.literal.words.index/sih.js.string.literal.words.index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 113, "dataFileSizeInBytes": 565, "storageSizeInBytes": 1032, "btreeStatistics": {"pages": 1, "elements": 112, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 223, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 542}, "shared_indexes/sih.js.test.names.extensible/sih.js.test.names.extensible_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.js.test.names/sih.js.test.names_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 11, "dataFileSizeInBytes": 55, "storageSizeInBytes": 216, "btreeStatistics": {"pages": 1, "elements": 11, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 21, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 93}, "shared_indexes/sih.json.file.root.values/sih.json.file.root.values_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.sql.routine.index/sih.sql.routine.index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "shared_indexes/sih.sql.types.count.index/sih.sql.types.count.index_storage.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}, "statsPerEnumerator": {"rep.names": {"collisions": 0, "values": 914, "dataFileSizeInBytes": 26266, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 914, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 3, "searchRequests": 2741, "searchSteps": 363, "pageCapacity": 32768, "sizeInBytes": 32768}}}}}, "otherStorageStats": {"statsPerPhm": {"CompassFunctionsIndex/CompassFunctionsIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "CompassFunctionsIndex/CompassFunctionsIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "DomFileIndex/DomFileIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "DomFileIndex/DomFileIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "FrameworkDetectionIndex/FrameworkDetectionIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "FrameworkDetectionIndex/FrameworkDetectionIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "HashFragmentIndex/HashFragmentIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "HashFragmentIndex/HashFragmentIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "HtmlScriptSrcIndex/HtmlScriptSrcIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "HtmlScriptSrcIndex/HtmlScriptSrcIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "HtmlTagIdIndex/HtmlTagIdIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "HtmlTagIdIndex/HtmlTagIdIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "IdIndex/IdIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 13, "elements": 23829, "height": 2, "moves": 0, "leafPages": 12, "maxSearchStepsInRequest": 71, "searchRequests": 112021, "searchSteps": 157016, "pageCapacity": 32768, "sizeInBytes": 425984}}, "valueStorageSizeInBytes": 205245}, "RelaxSymbolIndex/RelaxSymbolIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "RelaxSymbolIndex/RelaxSymbolIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "SchemaTypeInheritance/SchemaTypeInheritance.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "SchemaTypeInheritance/SchemaTypeInheritance.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Css.ampersand.selector/Css.ampersand.selector.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Css.attr/Css.attr.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Css.class/Css.class.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Css.custom.mixin/Css.custom.mixin.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Css.custom.property/Css.custom.property.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Css.custom.variable/Css.custom.variable.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Css.id/Css.id.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/JS.base.types.index/JS.base.types.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 100, "dataFileSizeInBytes": 1914, "storageSizeInBytes": 928, "btreeStatistics": {"pages": 1, "elements": 100, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 347, "searchSteps": 3, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 449}, "Stubs/JS.class.implements/JS.class.implements.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1, "dataFileSizeInBytes": 9, "storageSizeInBytes": 136, "btreeStatistics": {"pages": 1, "elements": 1, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 4, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 53}, "Stubs/JS.class.super/JS.class.super.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 266, "dataFileSizeInBytes": 4475, "storageSizeInBytes": 2256, "btreeStatistics": {"pages": 1, "elements": 266, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 534, "searchSteps": 6, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 1643}, "Stubs/JS.framework.markers.index/JS.framework.markers.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 16, "dataFileSizeInBytes": 357, "storageSizeInBytes": 256, "btreeStatistics": {"pages": 1, "elements": 16, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 216, "searchSteps": 3, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 150}, "Stubs/JS.global.qualified.elements/JS.global.qualified.elements.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 2818, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 17, "searchRequests": 8842, "searchSteps": 6703, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 16199}, "Stubs/JS.subclass.stub.index/JS.subclass.stub.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 32, "dataFileSizeInBytes": 447, "storageSizeInBytes": 384, "btreeStatistics": {"pages": 1, "elements": 32, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 63, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 195}, "Stubs/Less.mixins/Less.mixins.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Less.variables/Less.variables.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Scss.function/Scss.function.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Scss.mixin/Scss.mixin.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Scss.placeholder.selector/Scss.placeholder.selector.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Scss.variable/Scss.variable.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/Stubs.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 113, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 757, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 2110069}, "Stubs/amd.baseUrl.index/amd.baseUrl.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/amd.requirePaths.index/amd.requirePaths.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.ivy.directive.index/angular2.ivy.directive.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.ivy.module.index/angular2.ivy.module.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.ivy.pipe.index/angular2.ivy.pipe.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.metadata.className.index/angular2.metadata.className.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.metadata.directive.index/angular2.metadata.directive.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.metadata.function.index/angular2.metadata.function.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.metadata.module.index/angular2.metadata.module.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.metadata.node.index/angular2.metadata.node.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.metadata.pipe.index/angular2.metadata.pipe.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.source.directive.index/angular2.source.directive.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.source.module.index/angular2.source.module.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.source.pipe.index/angular2.source.pipe.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.symbol.index/angular2.symbol.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/angular2.templateUrl.index/angular2.templateUrl.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/dom.elementClass/dom.elementClass.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/dom.namespaceKey/dom.namespaceKey.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/es6.assignment.index/es6.assignment.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/es6.exported.index/es6.exported.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 860, "dataFileSizeInBytes": 11372, "storageSizeInBytes": 7016, "btreeStatistics": {"pages": 1, "elements": 860, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 4, "searchRequests": 1719, "searchSteps": 212, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 3967}, "Stubs/es6.side.effect.import.index/es6.side.effect.import.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/jade.mixin/jade.mixin.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.amd.modules/js.amd.modules.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.class.index2/js.class.index2.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 2418, "dataFileSizeInBytes": 45238, "storageSizeInBytes": 19504, "btreeStatistics": {"pages": 1, "elements": 2418, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 10, "searchRequests": 4835, "searchSteps": 2500, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 14445}, "Stubs/js.doc.modules/js.doc.modules.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.element.qualifiedName/js.element.qualifiedName.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 2856, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 18, "searchRequests": 9827, "searchSteps": 8617, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 16357}, "Stubs/js.export.default.react.component.index/js.export.default.react.component.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.exported.index/js.exported.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 2, "dataFileSizeInBytes": 8, "storageSizeInBytes": 144, "btreeStatistics": {"pages": 1, "elements": 2, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 3, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 80}, "Stubs/js.gcl.modules/js.gcl.modules.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.generics.index/js.generics.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 8, "dataFileSizeInBytes": 221, "storageSizeInBytes": 192, "btreeStatistics": {"pages": 1, "elements": 8, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 16, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 81}, "Stubs/js.global.symbol.index/js.global.symbol.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 2383, "dataFileSizeInBytes": 41247, "storageSizeInBytes": 19224, "btreeStatistics": {"pages": 1, "elements": 2383, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 17, "searchRequests": 15529, "searchSteps": 17077, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 14194}, "Stubs/js.imported.bindings.index/js.imported.bindings.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.lazy.packages/js.lazy.packages.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.local.namespace.members.index/js.local.namespace.members.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.namespace.index/js.namespace.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1903, "dataFileSizeInBytes": 36673, "storageSizeInBytes": 15376, "btreeStatistics": {"pages": 1, "elements": 1902, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 6, "searchRequests": 3806, "searchSteps": 1373, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 11193}, "Stubs/js.nonglobal.symbol.index/js.nonglobal.symbol.index.storage": {"persistentEnumeratorStatistics": {"collisions": 2, "values": 6898, "dataFileSizeInBytes": 93244, "storageSizeInBytes": 59512, "btreeStatistics": {"pages": 3, "elements": 6897, "height": 2, "moves": 0, "leafPages": 2, "maxSearchStepsInRequest": 52, "searchRequests": 18156, "searchSteps": 29206, "pageCapacity": 32768, "sizeInBytes": 98304}}, "valueStorageSizeInBytes": 44641}, "Stubs/js.qualified.shortName/js.qualified.shortName.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.symbol.index2/js.symbol.index2.storage": {"persistentEnumeratorStatistics": {"collisions": 2, "values": 8997, "dataFileSizeInBytes": 131126, "storageSizeInBytes": 76336, "btreeStatistics": {"pages": 5, "elements": 8996, "height": 2, "moves": 1, "leafPages": 4, "maxSearchStepsInRequest": 54, "searchRequests": 29210, "searchSteps": 39545, "pageCapacity": 32768, "sizeInBytes": 163840}}, "valueStorageSizeInBytes": 57666}, "Stubs/js.symbol.qualified.names.index/js.symbol.qualified.names.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 10, "elements": 17723, "height": 2, "moves": 0, "leafPages": 9, "maxSearchStepsInRequest": 57, "searchRequests": 94466, "searchSteps": 118099, "pageCapacity": 32768, "sizeInBytes": 327680}}, "valueStorageSizeInBytes": 98693}, "Stubs/js.typedef.index2/js.typedef.index2.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/js.used.remote.modules/js.used.remote.modules.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/postcss.custom.media/postcss.custom.media.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/postcss.custom.selector/postcss.custom.selector.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/redis.dml.shortName/redis.dml.shortName.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/sql.column.name/sql.column.name.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/sql.names/sql.names.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/sql.table.shortName/sql.table.shortName.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/ts.embedded.content.index/ts.embedded.content.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/ts.external.module.index/ts.external.module.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 2, "dataFileSizeInBytes": 8, "storageSizeInBytes": 144, "btreeStatistics": {"pages": 1, "elements": 2, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 6, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 70}, "Stubs/ts.external.module.name.index/ts.external.module.name.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 34, "dataFileSizeInBytes": 314, "storageSizeInBytes": 400, "btreeStatistics": {"pages": 1, "elements": 34, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 152, "searchSteps": 4, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 191}, "Stubs/ts.global.exported.index/ts.global.exported.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/ts.local.classes/ts.local.classes.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vue.components.index/vue.components.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vue.composition.app.index/vue.composition.app.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vue.extends.binding.index/vue.extends.binding.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vue.global.directives.index/vue.global.directives.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vue.global.filters.index/vue.global.filters.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vue.id.index/vue.id.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vue.mixin.binding.index/vue.mixin.binding.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vue.options.index/vue.options.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vue.url.index/vue.url.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Stubs/vuex.store.index/vuex.store.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "TodoIndex/TodoIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "Trigram.Index/Trigram.Index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 4, "elements": 7296, "height": 2, "moves": 0, "leafPages": 3, "maxSearchStepsInRequest": 54, "searchRequests": 29257, "searchSteps": 54090, "pageCapacity": 32768, "sizeInBytes": 131072}}, "valueStorageSizeInBytes": 109831}, "TypeScriptExternalPathCandidates/TypeScriptExternalPathCandidates.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "TypeScriptExternalPathCandidates/TypeScriptExternalPathCandidates.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "VueComponentStylesIndex/VueComponentStylesIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "VueComponentStylesIndex/VueComponentStylesIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "VueNoScriptFilesIndex/VueNoScriptFilesIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "VueNoScriptFilesIndex/VueNoScriptFilesIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "WebComponentsIndex/WebComponentsIndex.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "WebComponentsIndex/WebComponentsIndex.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "XmlNamespaces/XmlNamespaces.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "XmlNamespaces/XmlNamespaces.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "XmlTagNames/XmlTagNames.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "XmlTagNames/XmlTagNames.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "css.template.selectors/css.template.selectors.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "css.template.selectors/css.template.selectors.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "editorconfig.index.name/editorconfig.index.name.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "editorconfig.index.name/editorconfig.index.name.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "fileIncludes/fileIncludes.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "fileIncludes/fileIncludes.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "html5.custom.attributes.index/html5.custom.attributes.index.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "html5.custom.attributes.index/html5.custom.attributes.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.completion.host/http.request.completion.host.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.completion.host/http.request.completion.host.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.execution.environment/http.request.execution.environment.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.execution.environment/http.request.execution.environment.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.headers.values.index/http.request.headers.values.index.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.headers.values.index/http.request.headers.values.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.name.index/http.request.name.index.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "http.request.name.index/http.request.name.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.package.index/js.package.index.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.package.index/js.package.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.string.literal.words.index/js.string.literal.words.index.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 32, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 63, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 27026}, "js.string.literal.words.index/js.string.literal.words.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 1308, "dataFileSizeInBytes": 12592, "storageSizeInBytes": 10608, "btreeStatistics": {"pages": 1, "elements": 1308, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 5, "searchRequests": 2615, "searchSteps": 540, "pageCapacity": 32768, "sizeInBytes": 32768}}, "valueStorageSizeInBytes": 13348}, "js.test.names/js.test.names.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "js.test.names/js.test.names.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "json.file.root.values/json.file.root.values.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "json.file.root.values/json.file.root.values.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "sql.routine.index/sql.routine.index.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "sql.routine.index/sql.routine.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "sql.types.count.index/sql.types.count.index.forward": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": -1, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}, "sql.types.count.index/sql.types.count.index.storage": {"persistentEnumeratorStatistics": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "valueStorageSizeInBytes": 0}}, "statsPerEnumerator": {"/data/cache/idea/251/vcs-users/15375f63.2": {"collisions": 0, "values": 0, "dataFileSizeInBytes": 0, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 0, "elements": 0, "height": 0, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 0, "searchRequests": 0, "searchSteps": 0, "pageCapacity": 32768, "sizeInBytes": 0}}, "Stubs/serializerNames/names": {"collisions": 0, "values": 997, "dataFileSizeInBytes": 28157, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 997, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 3, "searchRequests": 2907, "searchSteps": 417, "pageCapacity": 32768, "sizeInBytes": 32768}}, "hashes": {"collisions": 0, "values": 113, "dataFileSizeInBytes": 2260, "storageSizeInBytes": 96, "btreeStatistics": {"pages": 1, "elements": 113, "height": 1, "moves": 0, "leafPages": 1, "maxSearchStepsInRequest": 1, "searchRequests": 13233, "searchSteps": 419, "pageCapacity": 32768, "sizeInBytes": 32768}}}}}