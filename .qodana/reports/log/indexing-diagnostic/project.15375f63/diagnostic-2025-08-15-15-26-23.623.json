{"appInfo": {"build": "251.25781", "buildDate": "2025-08-15T15:26:03.723Z[Etc/UTC]", "productCode": "QDJS", "generated": "2025-08-15T15:26:23.624953821Z", "os": "Linux 6.11.0-1018-azure", "runtime": "JetBrains s.r.o. 21.0.7 21.0.7+9-b895.115"}, "runtimeInfo": {"maxMemory": 5834276864, "numberOfProcessors": 2, "maxNumberOfIndexingThreads": 2, "maxSizeOfFileForIntelliSense": 2560000, "maxSizeOfFileForContentLoading": 20971520}, "type": "Scanning", "projectIndexingActivityHistory": {"projectName": "project", "times": {"scanningReason": "On project open", "scanningType": "FULL_ON_PROJECT_OPEN", "scanningId": 0, "creatingIteratorsTime": 0, "concurrentHandlingWallTimeWithoutPauses": 0, "concurrentHandlingWallTimeWithPauses": 0, "concurrentHandlingSumOfThreadTimesWithPauses": 8337247399, "concurrentIterationAndScannersApplicationSumOfThreadTimesWithPauses": 177467884, "concurrentFileCheckSumOfThreadTimesWithPauses": **********, "delayedPushPropertiesStageTime": 0, "indexExtensionsTime": 0, "dumbWallTimeWithPauses": 0, "dumbWallTimeWithoutPauses": 0, "updatingStart": "2025-08-15T15:26:12.989104972Z", "updatingEnd": "2025-08-15T15:26:23.617771137Z", "totalWallTimeWithPauses": 10628666165, "wallTimeOnPause": 5532133, "isCancelled": false, "totalWallTimeWithoutPauses": 10623134032}, "fileCount": {"numberOfFileProviders": 15, "numberOfScannedFiles": 650, "numberOfFilesIndexedByInfrastructureExtensionsDuringScan": 0, "numberOfFilesScheduledForIndexingAfterScan": 0}, "scanningStatistics": [{"providerName": "Module 'project' (all roots)", "numberOfScannedFiles": 319, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": **********, "totalOneThreadTimeWithPauses": **********, "iterationAndScannersApplicationTime": 158624641, "filesCheckTime": **********, "timeProcessingUpToDateFiles": 130256932, "timeUpdatingContentLessIndexes": 9407, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Custom kind roots from entity (sass_functions.scss, sass_color.scss, sass_list.scss, ..., empty)", "numberOfScannedFiles": 8, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 16435204, "totalOneThreadTimeWithPauses": **********, "iterationAndScannersApplicationTime": 150268, "filesCheckTime": **********, "timeProcessingUpToDateFiles": 4809132, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "com.intellij.openapi.roots.ImmutableSyntheticLibrary@3db66", "numberOfScannedFiles": 0, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 0, "totalOneThreadTimeWithPauses": 849492439, "iterationAndScannersApplicationTime": 9675460, "filesCheckTime": 834904516, "timeProcessingUpToDateFiles": 0, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'JS required libraries' (non-project)", "numberOfScannedFiles": 0, "numberOfSkippedFiles": 12, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 0, "totalOneThreadTimeWithPauses": 146881647, "iterationAndScannersApplicationTime": 61915, "filesCheckTime": 146750192, "timeProcessingUpToDateFiles": 0, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'com.intellij.javaee.ExternalResourcesRootsProvider@7d684c4d' (non-project)", "numberOfScannedFiles": 208, "numberOfSkippedFiles": 4, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 122598315, "totalOneThreadTimeWithPauses": 138798997, "iterationAndScannersApplicationTime": 7782390, "filesCheckTime": 130946316, "timeProcessingUpToDateFiles": 33166478, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'JS required libraries' (project)", "numberOfScannedFiles": 97, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 76847949, "totalOneThreadTimeWithPauses": 101998070, "iterationAndScannersApplicationTime": 346143, "filesCheckTime": 101596585, "timeProcessingUpToDateFiles": 32306194, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'TypeScript bundled libraries' (project)", "numberOfScannedFiles": 16, "numberOfSkippedFiles": 75, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 8490694, "totalOneThreadTimeWithPauses": 98565938, "iterationAndScannersApplicationTime": 291742, "filesCheckTime": 98079034, "timeProcessingUpToDateFiles": 1862625, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'TypeScript bundled libraries' (non-project)", "numberOfScannedFiles": 0, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 0, "totalOneThreadTimeWithPauses": 28794704, "iterationAndScannersApplicationTime": 28022, "filesCheckTime": 28705899, "timeProcessingUpToDateFiles": 0, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'com.intellij.javaee.ExternalResourcesRootsProvider@7d684c4d' (project)", "numberOfScannedFiles": 0, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 0, "totalOneThreadTimeWithPauses": 25833104, "iterationAndScannersApplicationTime": 16942, "filesCheckTime": 25771360, "timeProcessingUpToDateFiles": 0, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'com.intellij.httpClient.http.request.HttpRequestCollectionProvider@468d927d' (project)", "numberOfScannedFiles": 0, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 0, "totalOneThreadTimeWithPauses": 20395567, "iterationAndScannersApplicationTime": 22692, "filesCheckTime": 20299959, "timeProcessingUpToDateFiles": 0, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'org.jetbrains.plugins.scss.index.SassScssIndexedRootProvider@3ed1bfc7' (non-project)", "numberOfScannedFiles": 0, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 0, "totalOneThreadTimeWithPauses": 9095600, "iterationAndScannersApplicationTime": 21671, "filesCheckTime": 9017345, "timeProcessingUpToDateFiles": 0, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'org.jetbrains.plugins.scss.index.SassScssIndexedRootProvider@3ed1bfc7' (project)", "numberOfScannedFiles": 0, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 0, "totalOneThreadTimeWithPauses": 4226116, "iterationAndScannersApplicationTime": 29515, "filesCheckTime": 4117655, "timeProcessingUpToDateFiles": 0, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'Scratches & Consoles' (project)", "numberOfScannedFiles": 0, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 0, "totalOneThreadTimeWithPauses": 3330993, "iterationAndScannersApplicationTime": 36348, "filesCheckTime": 3207584, "timeProcessingUpToDateFiles": 0, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'Scratches & Consoles' (non-project)", "numberOfScannedFiles": 0, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 0, "totalOneThreadTimeWithPauses": 1603520, "iterationAndScannersApplicationTime": 23864, "filesCheckTime": 1528840, "timeProcessingUpToDateFiles": 0, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}, {"providerName": "Indexable set contributor 'com.intellij.httpClient.http.request.HttpRequestCollectionProvider@468d927d' (non-project)", "numberOfScannedFiles": 2, "numberOfSkippedFiles": 0, "numberOfFilesForIndexing": 0, "numberOfFilesFullyIndexedByInfrastructureExtensions": 0, "filesFullyIndexedByInfrastructureExtensions": [], "statusTime": 547736, "totalOneThreadTimeWithPauses": 1365137, "iterationAndScannersApplicationTime": 356271, "filesCheckTime": 956648, "timeProcessingUpToDateFiles": 105085, "timeUpdatingContentLessIndexes": 0, "timeIndexingWithoutContentViaInfrastructureExtension": 0, "roots": []}]}}