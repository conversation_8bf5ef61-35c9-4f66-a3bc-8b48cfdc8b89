<html>
  <head>
    <title>Indexing diagnostics of 'project'</title>
    <style>body {
  font-family: Arial, sans-serif;
  margin: 0;
}

table, th, td {
  border: 1px solid black;
  border-collapse: collapse;
}

table {
  width: 80%;
}

table.activity-table {
  width: 100%;
}

table.metrics-table {
  width: auto;
}

.red-text {
  color: red;
}

table.narrow-activity-table {
  width: 80%;
}

table.two-columns td {
  width: 50%;
}

th, td {
  padding: 3px;
}

th {
  background: lightgrey;
}

td {
  white-space: pre-wrap;
  word-break: break-word;
}

.stats-content {
  margin-left: 20%;
}

.stats-activity-content {
  margin-left: 20%;
  margin-right: 5%;
}

.aggregate-report-content {
  margin-left: 10%;
}

.aggregate-activity-report-content {
  margin-left: 10%;
  margin-right: 10%;
}

.aggregate-header {
  padding-top: 1em;
  padding-bottom: 1em;
  margin-bottom: 0;
}

.table-with-margin{
  margin-top: 1em;
  margin-bottom: 2em;
}

.navigation-bar {
  width: 15%;
  background-color: lightgrey;
  position: fixed;
  height: 100%;
}

div.navigation-bar ul {
  list-style-type: none;
  overflow: auto;
  margin: 0;
  padding: 0;
  font-size: 24px;
}

div.navigation-bar ul li a, label {
  display: block;
  color: #000;
  padding: 8px 20px;
  text-decoration: none;
}

label input {
  margin-left: 10px;
  width: 20px;
  height: 20px;
}

div.navigation-bar ul li a:hover {
  background-color: #555;
  color: white;
}

.minor-data {}

.invisible {
  display: none;
}

.jetbrains-logo {
  width: 50%;
  bottom: 5%;
  position: absolute;
  left: 20%;
}

.centered-text {
  text-align: center;
}

.linkable-table-row:hover {
  background: #f2f3ff;
  outline: none;
  cursor: pointer;
}

.scanning-table-row {
  background-color: aliceblue;
}

.not-applicable-data{
  color: darkgrey;
}</style>
    <script>document.addEventListener("DOMContentLoaded", () => {
  const rows = document.getElementsByClassName("linkable-table-row")
  for (const row of rows) {
    const href = row.getAttribute("href")
    row.addEventListener("click", () => {
      window.open(href, "_blank");
    });
  }
});</script>
  </head>
  <body>
    <div class="aggregate-activity-report-content">
      <h1 class="aggregate-header">project</h1>
      <div>All durations are wall time; total and content loading time include pauses
        <table class="centered-text table-with-margin activity-table"><caption style="caption-side: bottom; text-align: right; font-size: 14px">Click for details</caption>
          <thead>
            <tr>
              <th colspan="14">History of scannings and indexings</th>
            </tr>
            <tr>
              <th colspan="7">Time</th>
              <th colspan="5">Files</th>
              <th rowspan="2">Scanning ID(s)</th>
              <th rowspan="2">Scanning Type</th>
            </tr>
            <tr>
              <th>Started</th>
              <th>Started dumb mode</th>
              <th>Finished</th>
              <th>Total&nbsp;time</th>
              <th>Time spent on pause</th>
              <th>Full dumb mode time</th>
              <th>Dumb mode time w/o pauses</th>
              <th>Scanned</th>
              <th>Shared indexes (w/o content loading)</th>
              <th>Scheduled for indexing</th>
              <th>Shared indexes (content loaded)</th>
              <th>Total indexed (shared indexes included)</th>
            </tr>
          </thead>
          <tbody>
            <tr class="linkable-table-row" href="diagnostic-2025-08-15-15-26-49.620.html">
              <td>15:26:32 15 Aug</td>
              <td>15:26:32 15 Aug</td>
              <td>15:26:49 15 Aug</td>
              <td>17 s 236 ms</td>
              <td>0</td>
              <td>17 s 236 ms</td>
              <td>17 s 236 ms</td>
              <td class="not-applicable-data">N/A</td>
              <td class="not-applicable-data">N/A</td>
              <td class="not-applicable-data">N/A</td>
              <td>0</td>
              <td>1917<br>(incl. 1 changed in VFS)</td>
              <td>1</td>
              <td class="not-applicable-data">N/A</td>
            </tr>
            <tr class="linkable-table-row scanning-table-row" href="diagnostic-2025-08-15-15-26-32.367.html">
              <td>15:26:31 15 Aug</td>
              <td class="not-applicable-data">Didn't start</td>
              <td>15:26:32 15 Aug</td>
              <td>767 ms</td>
              <td>&lt; 1 ms</td>
              <td>0</td>
              <td>0</td>
              <td>1916</td>
              <td>0</td>
              <td>1916</td>
              <td class="not-applicable-data">N/A</td>
              <td class="not-applicable-data">N/A</td>
              <td>1</td>
              <td>partial</td>
            </tr>
            <tr class="linkable-table-row" href="diagnostic-2025-08-15-15-26-31.589.html">
              <td>15:26:23 15 Aug</td>
              <td>15:26:23 15 Aug</td>
              <td>15:26:31 15 Aug</td>
              <td>7 s 944 ms</td>
              <td>0</td>
              <td>7 s 944 ms</td>
              <td>7 s 944 ms</td>
              <td class="not-applicable-data">N/A</td>
              <td class="not-applicable-data">N/A</td>
              <td class="not-applicable-data">N/A</td>
              <td>0</td>
              <td>319<br>(incl. 319 changed in VFS)</td>
              <td>None</td>
              <td class="not-applicable-data">N/A</td>
            </tr>
            <tr class="linkable-table-row scanning-table-row" href="diagnostic-2025-08-15-15-26-23.623.html">
              <td>15:26:12 15 Aug</td>
              <td class="not-applicable-data">Didn't start</td>
              <td>15:26:23 15 Aug</td>
              <td>10 s 628 ms</td>
              <td>5 ms</td>
              <td>0</td>
              <td>0</td>
              <td>650</td>
              <td>0</td>
              <td>0</td>
              <td class="not-applicable-data">N/A</td>
              <td class="not-applicable-data">N/A</td>
              <td>0</td>
              <td>full on project open</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div>
        <table class="table-with-margin activity-table metrics-table">
          <thead>
            <tr>
              <th colspan="2">Metrics</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>indexingTimeWithoutPauses</td>
              <td>25 s 181 ms</td>
            </tr>
            <tr>
              <td>scanningTimeWithoutPauses</td>
              <td>11 s 390 ms</td>
            </tr>
            <tr>
              <td>pausedTimeInIndexingOrScanning</td>
              <td>5 ms</td>
            </tr>
            <tr>
              <td>dumbModeTimeWithPauses</td>
              <td>25 s 180 ms</td>
            </tr>
            <tr>
              <td>numberOfIndexedFiles</td>
              <td>2236</td>
            </tr>
            <tr>
              <td>numberOfIndexedFilesWritingIndexValue</td>
              <td>2236</td>
            </tr>
            <tr>
              <td>numberOfIndexedFilesWithNothingToWrite</td>
              <td>0</td>
            </tr>
            <tr>
              <td>numberOfFilesIndexedByExtensions</td>
              <td>0</td>
            </tr>
            <tr>
              <td>numberOfFilesIndexedWithoutExtensions</td>
              <td>2236</td>
            </tr>
            <tr>
              <td>numberOfRunsOfScannning</td>
              <td>2</td>
            </tr>
            <tr>
              <td>numberOfRunsOfIndexing</td>
              <td>2</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#HTML</td>
              <td>205</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#JavaScript</td>
              <td>854</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#TypeScript JSX</td>
              <td>117</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#JSON</td>
              <td>2111</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#textmate</td>
              <td>2114</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#TypeScript</td>
              <td>1052</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#PLAIN_TEXT</td>
              <td>935</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#YAML</td>
              <td>471</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#CSS</td>
              <td>383</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#Shell Script</td>
              <td>1114</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#GitIgnore file</td>
              <td>205</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#SVG</td>
              <td>924</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#tsconfig</td>
              <td>193</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#SARIF</td>
              <td>182</td>
            </tr>
            <tr>
              <td>processingSpeedAvg#AUTO_DETECTED</td>
              <td>0</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#HTML</td>
              <td>134</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#JavaScript</td>
              <td>447</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#TypeScript JSX</td>
              <td>75</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#JSON</td>
              <td>976</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#textmate</td>
              <td>1380</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#TypeScript</td>
              <td>65</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#PLAIN_TEXT</td>
              <td>610</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#YAML</td>
              <td>307</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#CSS</td>
              <td>108</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#Shell Script</td>
              <td>727</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#GitIgnore file</td>
              <td>134</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#SVG</td>
              <td>603</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#tsconfig</td>
              <td>126</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#SARIF</td>
              <td>119</td>
            </tr>
            <tr>
              <td>processingSpeedWorst#AUTO_DETECTED</td>
              <td>0</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#JavaScript</td>
              <td>950</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#XML</td>
              <td>207</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#HTML</td>
              <td>205</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#TypeScript</td>
              <td>965</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#TypeScript JSX</td>
              <td>117</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#JSON</td>
              <td>2074</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#textmate</td>
              <td>2114</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#TEXT</td>
              <td>935</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#yaml</td>
              <td>471</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#CSS</td>
              <td>383</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#Shell Script</td>
              <td>1114</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#GitIgnore</td>
              <td>205</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#IgnoreLang</td>
              <td>205</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageAvg#SVG</td>
              <td>924</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#JavaScript</td>
              <td>289</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#XML</td>
              <td>135</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#HTML</td>
              <td>134</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#TypeScript</td>
              <td>74</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#TypeScript JSX</td>
              <td>75</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#JSON</td>
              <td>976</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#textmate</td>
              <td>1380</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#TEXT</td>
              <td>610</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#yaml</td>
              <td>307</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#CSS</td>
              <td>108</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#Shell Script</td>
              <td>727</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#GitIgnore</td>
              <td>134</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#IgnoreLang</td>
              <td>134</td>
            </tr>
            <tr>
              <td>processingSpeedOfBaseLanguageWorst#SVG</td>
              <td>603</td>
            </tr>
            <tr>
              <td>processingTime#HTML</td>
              <td>2 s 732 ms</td>
            </tr>
            <tr>
              <td>processingTime#JavaScript</td>
              <td>2 s 747 ms</td>
            </tr>
            <tr>
              <td>processingTime#TypeScript JSX</td>
              <td>1 s 752 ms</td>
            </tr>
            <tr>
              <td>processingTime#JSON</td>
              <td>380 ms</td>
            </tr>
            <tr>
              <td>processingTime#textmate</td>
              <td>168 ms</td>
            </tr>
            <tr>
              <td>processingTime#TypeScript</td>
              <td>17 s 81 ms</td>
            </tr>
            <tr>
              <td>processingTime#PLAIN_TEXT</td>
              <td>49 ms</td>
            </tr>
            <tr>
              <td>processingTime#YAML</td>
              <td>47 ms</td>
            </tr>
            <tr>
              <td>processingTime#CSS</td>
              <td>156 ms</td>
            </tr>
            <tr>
              <td>processingTime#Shell Script</td>
              <td>37 ms</td>
            </tr>
            <tr>
              <td>processingTime#GitIgnore file</td>
              <td>11 ms</td>
            </tr>
            <tr>
              <td>processingTime#SVG</td>
              <td>6 ms</td>
            </tr>
            <tr>
              <td>processingTime#tsconfig</td>
              <td>5 ms</td>
            </tr>
            <tr>
              <td>processingTime#SARIF</td>
              <td>1 ms</td>
            </tr>
            <tr>
              <td>processingTime#AUTO_DETECTED</td>
              <td>1 ms</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div>
        <table class="table-with-margin activity-table">
          <thead>
            <tr>
              <th colspan="9">Shared indexes</th>
            </tr>
            <tr>
              <th>Time</th>
              <th>Kind</th>
              <th>Name</th>
              <th>Size</th>
              <th>Download time</th>
              <th>Download speed</th>
              <th>Status</th>
              <th>ID</th>
              <th>Generation time</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
      <div>
        <table class="table-with-margin activity-table">
          <thead>
            <tr>
              <th colspan="5">Scanning to push properties of changed files</th>
            </tr>
            <tr>
              <th>Time</th>
              <th>Reason</th>
              <th>Full duration</th>
              <th>Is cancelled</th>
              <th>Number</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>15:26:23 15 Aug</td>
              <td>Push on VFS changes</td>
              <td>78 ms</td>
              <td>fully finished</td>
              <td>1</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>
