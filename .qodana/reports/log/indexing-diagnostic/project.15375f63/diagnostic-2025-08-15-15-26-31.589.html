<html>
  <head>
    <title>Indexing diagnostics of 'project'</title>
    <style>body {
  font-family: Arial, sans-serif;
  margin: 0;
}

table, th, td {
  border: 1px solid black;
  border-collapse: collapse;
}

table {
  width: 80%;
}

table.activity-table {
  width: 100%;
}

table.metrics-table {
  width: auto;
}

.red-text {
  color: red;
}

table.narrow-activity-table {
  width: 80%;
}

table.two-columns td {
  width: 50%;
}

th, td {
  padding: 3px;
}

th {
  background: lightgrey;
}

td {
  white-space: pre-wrap;
  word-break: break-word;
}

.stats-content {
  margin-left: 20%;
}

.stats-activity-content {
  margin-left: 20%;
  margin-right: 5%;
}

.aggregate-report-content {
  margin-left: 10%;
}

.aggregate-activity-report-content {
  margin-left: 10%;
  margin-right: 10%;
}

.aggregate-header {
  padding-top: 1em;
  padding-bottom: 1em;
  margin-bottom: 0;
}

.table-with-margin{
  margin-top: 1em;
  margin-bottom: 2em;
}

.navigation-bar {
  width: 15%;
  background-color: lightgrey;
  position: fixed;
  height: 100%;
}

div.navigation-bar ul {
  list-style-type: none;
  overflow: auto;
  margin: 0;
  padding: 0;
  font-size: 24px;
}

div.navigation-bar ul li a, label {
  display: block;
  color: #000;
  padding: 8px 20px;
  text-decoration: none;
}

label input {
  margin-left: 10px;
  width: 20px;
  height: 20px;
}

div.navigation-bar ul li a:hover {
  background-color: #555;
  color: white;
}

.minor-data {}

.invisible {
  display: none;
}

.jetbrains-logo {
  width: 50%;
  bottom: 5%;
  position: absolute;
  left: 20%;
}

.centered-text {
  text-align: center;
}

.linkable-table-row:hover {
  background: #f2f3ff;
  outline: none;
  cursor: pointer;
}

.scanning-table-row {
  background-color: aliceblue;
}

.not-applicable-data{
  color: darkgrey;
}</style>
    <script>function hideElementsHavingClass(className, hideOrShow) {
  const elements = document.getElementsByClassName(className)
  const displayType = hideOrShow ? 'none' : 'initial'
  for (const element of elements) {
    element.classList.toggle('invisible', hideOrShow)
  }
}</script>
  </head>
  <body>
    <div class="navigation-bar">
      <ul>
        <li><a href="#id-project-name">Project name</a></li>
        <li><a href="#id-app-info">Application info</a></li>
        <li><a href="#id-runtime-info">Runtime</a></li>
        <li><a href="#id-indexing-info">Overview</a></li>
        <li><a href="#id-slow-files">Slowly indexed files</a></li>
        <li><a href="#id-stats-per-file-type">Statistics per file type</a></li>
        <li><a href="#id-stats-per-indexer">Statistics per indexer</a></li>
        <li><a href="#id-indexing">Indexing</a></li>
      </ul>
      <hr class="solid">
      <ul>
        <li><label for="id-hide-minor-data-checkbox">Hide minor data<input checked="checked" id="id-hide-minor-data-checkbox" type="checkbox" onclick="hideElementsHavingClass('minor-data', this.checked)">
              <style>padding-left: 10px</style>
            </label></li>
      </ul>
      <div class="jetbrains-logo"><svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120.21407 130.10375">
  <title>logo-grey</title>
  <g>
    <path d="M120.2,68.6a4.99684,4.99684,0,0,0-1.4-3.5L55.191,2.68736a7.86372,7.86372,0,0,0-3.36731-2.261c-.03467-.012-.06812-.02612-.103-.03766-.19336-.06323-.3913-.11511-.591-.16327-.06689-.0163-.13177-.0368-.1994-.05133-.18744-.04-.37909-.06738-.57117-.09363-.0788-.0108-.15527-.0274-.23492-.03589A7.83914,7.83914,0,0,0,49.3,0a7.73961,7.73961,0,0,0-1.21088.10413c-.0235.00391-.04694.00671-.07037.0108a7.62573,7.62573,0,0,0-3.092,1.24969c-.07343.04773-.155.08575-.22668.13538L4.9,28.3c-.08221.08221-.15106.10632-.17773.16437L4.67218,28.5H4.6A11.11647,11.11647,0,0,0,.15875,39.45683l.00854.04449c.05176.28589.11011.56958.18372.84973.03052.119.06964.235.104.35284.054.181.10278.3634.16571.5412A11.15109,11.15109,0,0,0,5.3,47.1a8.82025,8.82025,0,0,0,2,.9c.4.2,45.4,18.8,45.4,18.8a4.291,4.291,0,0,0,4.4-7.3c-.06525,0-16.839-13.21332-28.69928-22.52606l21.105-19.03113,57.91815,49.58282L28.6,110.7a9.82332,9.82332,0,0,0-4.7,4.1,10.0662,10.0662,0,0,0,3.6,13.9,10.28689,10.28689,0,0,0,10.7-.2c.2-.2.5-.3.7-.5L116.9,73.2a18.32,18.32,0,0,0,1.58612-1.2663A4.74573,4.74573,0,0,0,120.2,68.6Z" transform="translate(0.01406 0.00002)" fill="#cdcdcd"/>
    <g id="_Group_" data-name="&lt;Group&gt;">
      <rect id="_Path_" data-name="&lt;Path&gt;" x="34.61406" y="37.40002" width="51" height="51"/>
      <rect id="_Path_2" data-name="&lt;Path&gt;" x="39.01406" y="78.80002" width="19.1" height="3.2" fill="#fff"/>
      <g id="_Group_2" data-name="&lt;Group&gt;">
        <path id="_Path_3" data-name="&lt;Path&gt;" d="M38.8,50.8l1.5-1.4a1.70271,1.70271,0,0,0,1.3.8q.9,0,.9-1.2V43.7h2.3V49a2.79543,2.79543,0,0,1-3.1,3.1A3.026,3.026,0,0,1,38.8,50.8Z" transform="translate(0.01406 0.00002)" fill="#fff"/>
        <path id="_Path_4" data-name="&lt;Path&gt;" d="M45.3,43.8H52v1.9H47.6V47h4v1.8h-4v1.3h4.5v2H45.4Z" transform="translate(0.01406 0.00002)" fill="#fff"/>
        <path id="_Path_5" data-name="&lt;Path&gt;" d="M55,45.8H52.5v-2h7.3v2H57.3v6.3H55Z" transform="translate(0.01406 0.00002)" fill="#fff"/>
        <path id="_Compound_Path_" data-name="&lt;Compound Path&gt;" d="M39,54h4.3a3.7023,3.7023,0,0,1,2.3.7,1.97822,1.97822,0,0,1,.5,1.4h0A1.95538,1.95538,0,0,1,44.8,58a1.94762,1.94762,0,0,1,1.6,2h0c0,1.4-1.2,2.3-3.1,2.3H39Zm4.8,2.6c0-.5-.4-.7-1-.7H41.3v1.5h1.4c.7-.1,1.1-.3,1.1-.8ZM43,59H41.2v1.5H43c.7,0,1.1-.3,1.1-.8h0C44.1,59.2,43.7,59,43,59Z" transform="translate(0.01406 0.00002)" fill="#fff"/>
        <path id="_Compound_Path_2" data-name="&lt;Compound Path&gt;" d="M46.8,54h3.9a3.51463,3.51463,0,0,1,2.7.9,2.48948,2.48948,0,0,1,.7,1.9h0a2.76053,2.76053,0,0,1-1.7,2.6l2,2.9H51.8l-1.7-2.5h-1v2.5H46.8Zm3.8,4c.8,0,1.2-.4,1.2-1h0c0-.7-.5-1-1.2-1H49.1v2Z" transform="translate(0.01406 0.00002)" fill="#fff"/>
        <path id="_Compound_Path_3" data-name="&lt;Compound Path&gt;" d="M56.8,54H59l3.5,8.4H60l-.6-1.5H56.2l-.6,1.5H53.2Zm2,5-.9-2.3L57,59Z" transform="translate(0.01406 0.00002)" fill="#fff"/>
        <path id="_Path_6" data-name="&lt;Path&gt;" d="M62.8,54h2.3v8.3H62.8Z" transform="translate(0.01406 0.00002)" fill="#fff"/>
        <path id="_Path_7" data-name="&lt;Path&gt;" d="M65.7,54h2.1l3.4,4.4V54h2.3v8.3h-2L68,57.8v4.6H65.7Z" transform="translate(0.01406 0.00002)" fill="#fff"/>
        <path id="_Path_8" data-name="&lt;Path&gt;" d="M73.7,61.1,75,59.6a3.94219,3.94219,0,0,0,2.7,1c.6,0,1-.2,1-.6h0c0-.4-.3-.5-1.4-.8-1.8-.4-3.1-.9-3.1-2.6h0c0-1.5,1.2-2.7,3.2-2.7A5.33072,5.33072,0,0,1,80.8,55l-1.2,1.6a4.55346,4.55346,0,0,0-2.3-.8c-.6,0-.8.2-.8.5h0c0,.4.3.5,1.4.8,1.9.4,3.1,1,3.1,2.6h0c0,1.7-1.3,2.7-3.4,2.7A5.29336,5.29336,0,0,1,73.7,61.1Z" transform="translate(0.01406 0.00002)" fill="#fff"/>
      </g>
    </g>
  </g>
</svg></div>
    </div>
    <div class="stats-activity-content">
      <div id="id-project-name">
        <h1 class="aggregate-header">project</h1>
      </div>
All durations are wall time; they include pauses unless specified otherwise
      <div id="id-app-info">
        <table class="two-columns table-with-margin narrow-activity-table">
          <thead>
            <tr>
              <th colspan="2">Application info</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Build</td>
              <td>251.25781</td>
            </tr>
            <tr>
              <td>Build date</td>
              <td>15:26:03 15 Aug</td>
            </tr>
            <tr>
              <td>Product code</td>
              <td>QDJS</td>
            </tr>
            <tr>
              <td>Generated</td>
              <td>15:26:31 15 Aug</td>
            </tr>
            <tr>
              <td>OS</td>
              <td>Linux 6.11.0-1018-azure</td>
            </tr>
            <tr>
              <td>Runtime</td>
              <td>JetBrains s.r.o. 21.0.7 21.0.7+9-b895.115</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div id="id-runtime-info">
        <table class="two-columns table-with-margin narrow-activity-table">
          <thead>
            <tr>
              <th colspan="2">Runtime</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Max memory</td>
              <td>5.83 GB</td>
            </tr>
            <tr>
              <td>Number of processors</td>
              <td>2</td>
            </tr>
            <tr>
              <td>Max number of indexing threads</td>
              <td>2</td>
            </tr>
            <tr>
              <td>Max size of file for analysis</td>
              <td>2.56 MB</td>
            </tr>
            <tr>
              <td>Max size of file for content loading</td>
              <td>20.97 MB</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div id="id-indexing-info">
        <table class="two-columns table-with-margin narrow-activity-table">
          <thead>
            <tr>
              <th colspan="2">Overview</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Activity</td>
              <td>Dumb indexing</td>
            </tr>
            <tr>
              <td>Indexed files from scanning sessions with IDs</td>
              <td>Only refreshed files were indexed</td>
            </tr>
            <tr>
              <td>Started at</td>
              <td>15:26:23 15 Aug</td>
            </tr>
            <tr>
              <td>Finished at</td>
              <td>15:26:31 15 Aug</td>
            </tr>
            <tr>
              <td>Is cancelled</td>
              <td>false</td>
            </tr>
            <tr>
              <td>Cancellation reason</td>
              <td></td>
            </tr>
            <tr>
              <td>Total time with pauses</td>
              <td>7 s 944 ms</td>
            </tr>
            <tr>
              <td>Pauses time</td>
              <td>0</td>
            </tr>
            <tr>
              <td>Time of retrieving list of the files changed in VFS<br><small>Indexing handles both files found by scanning(s) and files reported as changed by VFS</small></td>
              <td>20 ms</td>
            </tr>
            <tr>
              <td>Content loading time</td>
              <td>65 ms</td>
            </tr>
            <tr>
              <td>Index writing time</td>
              <td>590 ms</td>
            </tr>
            <tr>
              <td>Number of files indexed by shared indexes during the indexing stage (with loading content)</td>
              <td>0</td>
            </tr>
            <tr>
              <td>Number of files for which there was nothing to write (indexes were already up-to-date)</td>
              <td>0</td>
            </tr>
            <tr>
              <td>Number of files indexed during the indexing stage with loading content (including indexed by shared indexes)</td>
              <td>319</td>
            </tr>
            <tr>
              <td>Number of too large for indexing files</td>
              <td>1</td>
            </tr>
            <tr>
              <td>Number of files changed in VFS</td>
              <td>319</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div id="id-slow-files">
        <table class="table-with-margin activity-table">
          <thead>
            <tr>
              <th colspan="5">Slowly indexed files (&gt; 500 ms)</th>
            </tr>
            <tr>
              <th>Provider name</th>
              <th>File</th>
              <th>Content loading time</th>
              <th>Indexes values evaluation time</th>
              <th>Total processing time</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Refreshed files</td>
              <td>index.html</td>
              <td>&lt; 1 ms</td>
              <td>2 s 576 ms</td>
              <td>2 s 611 ms</td>
            </tr>
            <tr>
              <td>^</td>
              <td>index.js</td>
              <td>1 ms</td>
              <td>836 ms</td>
              <td>895 ms</td>
            </tr>
            <tr>
              <td>^</td>
              <td>multi_api_test_page.html</td>
              <td>&lt; 1 ms</td>
              <td>862 ms</td>
              <td>871 ms</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div id="id-stats-per-file-type">
        <table class="table-with-margin activity-table">
          <thead>
            <tr>
              <th colspan="7">Statistics per file type</th>
            </tr>
            <tr>
              <th>File type</th>
              <th>Number of files</th>
              <th>Total processing time (% of total processing time)</th>
              <th>Content loading time (% of total content loading time)</th>
              <th>Total files size</th>
              <th>Total processing speed (relative to the sum of wall times of all threads)</th>
              <th>The biggest contributors</th>
            </tr>
          </thead>
          <tbody>
            <tr class="">
              <td>HTML</td>
              <td>6</td>
              <td>2 s 732 ms (34.4%)</td>
              <td>&lt; 1 ms (&lt; 1%)</td>
              <td>562.42 kB</td>
              <td>134.31 kB/s</td>
              <td>100.0%: Refreshed files 6 files of total size 562.42 kB</td>
            </tr>
            <tr class="">
              <td>JavaScript</td>
              <td>60</td>
              <td>2 s 617 ms (33.0%)</td>
              <td>4 ms (6.9%)</td>
              <td>1.79 MB</td>
              <td>447.34 kB/s</td>
              <td>100.0%: Refreshed files 60 files of total size 1.79 MB</td>
            </tr>
            <tr class="">
              <td>TypeScript JSX</td>
              <td>59</td>
              <td>1 s 742 ms (21.9%)</td>
              <td>18 ms (29.0%)</td>
              <td>200.4 kB</td>
              <td>75.05 kB/s</td>
              <td>100.0%: Refreshed files 59 files of total size 200.4 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>JSON</td>
              <td>41</td>
              <td>312 ms (3.9%)</td>
              <td>14 ms (22.0%)</td>
              <td>671.44 kB</td>
              <td>1.4 MB/s</td>
              <td>100.0%: Refreshed files 41 files of total size 671.44 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>textmate</td>
              <td>76</td>
              <td>168 ms (2.1%)</td>
              <td>11 ms (18.3%)</td>
              <td>355.45 kB</td>
              <td>1.38 MB/s</td>
              <td>100.0%: Refreshed files 76 files of total size 355.45 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>TypeScript</td>
              <td>9</td>
              <td>166 ms (2.1%)</td>
              <td>1 ms (1.9%)</td>
              <td>16.61 kB</td>
              <td>65.06 kB/s</td>
              <td>100.0%: Refreshed files 9 files of total size 16.61 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>PLAIN_TEXT</td>
              <td>33</td>
              <td>49 ms (&lt; 1%)</td>
              <td>2 ms (3.9%)</td>
              <td>46.6 kB</td>
              <td>610.63 kB/s</td>
              <td>100.0%: Refreshed files 33 files of total size 46.6 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>YAML</td>
              <td>9</td>
              <td>47 ms (&lt; 1%)</td>
              <td>3 ms (5.1%)</td>
              <td>22.21 kB</td>
              <td>307.93 kB/s</td>
              <td>100.0%: Refreshed files 9 files of total size 22.21 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>CSS</td>
              <td>3</td>
              <td>42 ms (&lt; 1%)</td>
              <td>&lt; 1 ms (&lt; 1%)</td>
              <td>7 kB</td>
              <td>108.47 kB/s</td>
              <td>100.0%: Refreshed files 3 files of total size 7 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>Shell Script</td>
              <td>13</td>
              <td>37 ms (&lt; 1%)</td>
              <td>4 ms (6.7%)</td>
              <td>42.27 kB</td>
              <td>727.55 kB/s</td>
              <td>100.0%: Refreshed files 13 files of total size 42.27 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>GitIgnore file</td>
              <td>3</td>
              <td>11 ms (&lt; 1%)</td>
              <td>1 ms (2.3%)</td>
              <td>2.42 kB</td>
              <td>134.12 kB/s</td>
              <td>100.0%: Refreshed files 3 files of total size 2.42 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>SVG</td>
              <td>2</td>
              <td>6 ms (&lt; 1%)</td>
              <td>1 ms (1.9%)</td>
              <td>6.46 kB</td>
              <td>603.24 kB/s</td>
              <td>100.0%: Refreshed files 2 files of total size 6.46 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>tsconfig</td>
              <td>2</td>
              <td>5 ms (&lt; 1%)</td>
              <td>&lt; 1 ms (&lt; 1%)</td>
              <td>1.03 kB</td>
              <td>126.58 kB/s</td>
              <td>100.0%: Refreshed files 2 files of total size 1.03 kB</td>
            </tr>
            <tr class="minor-data invisible">
              <td>SARIF</td>
              <td>1</td>
              <td>1 ms (&lt; 1%)</td>
              <td>&lt; 1 ms (&lt; 1%)</td>
              <td>363 B</td>
              <td>119.11 kB/s</td>
              <td>100.0%: Refreshed files 1 files of total size 363 B</td>
            </tr>
            <tr class="minor-data invisible">
              <td>AUTO_DETECTED</td>
              <td>1</td>
              <td>1 ms (&lt; 1%)</td>
              <td>&lt; 1 ms (&lt; 1%)</td>
              <td>0 B</td>
              <td>0 B/s</td>
              <td>100.0%: Refreshed files 1 files of total size 0 B</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div id="id-stats-per-indexer">
        <table class="table-with-margin activity-table">
          <thead>
            <tr>
              <th colspan="7">Statistics per indexer</th>
            </tr>
            <tr>
              <th>Index</th>
              <th>Number of files</th>
              <th>Part of total indexing time</th>
              <th>Total number of files indexed by shared indexes</th>
              <th>Total files size</th>
              <th>Indexing speed (relative to the sum of wall times on multiple threads)</th>
            </tr>
          </thead>
          <tbody>
            <tr class="">
              <td>Stubs</td>
              <td>137</td>
              <td>29.6%</td>
              <td>0</td>
              <td>2.58 MB</td>
              <td>796.86 kB/s</td>
            </tr>
            <tr class="">
              <td>IdIndex</td>
              <td>317</td>
              <td>19.8%</td>
              <td>0</td>
              <td>3.73 MB</td>
              <td>1.72 MB/s</td>
            </tr>
            <tr class="">
              <td>js.test.names</td>
              <td>128</td>
              <td>13.9%</td>
              <td>0</td>
              <td>2.01 MB</td>
              <td>1.32 MB/s</td>
            </tr>
            <tr class="">
              <td>js.implicit.elements.index</td>
              <td>6</td>
              <td>12.5%</td>
              <td>0</td>
              <td>562.42 kB</td>
              <td>411.53 kB/s</td>
            </tr>
            <tr class="">
              <td>fileIncludes</td>
              <td>137</td>
              <td>10.9%</td>
              <td>0</td>
              <td>2.58 MB</td>
              <td>2.16 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>HashFragmentIndex</td>
              <td>127</td>
              <td>4.4%</td>
              <td>0</td>
              <td>868.59 kB</td>
              <td>1.8 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>CssIndex</td>
              <td>9</td>
              <td>2.2%</td>
              <td>0</td>
              <td>569.42 kB</td>
              <td>2.32 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>Trigram.Index</td>
              <td>318</td>
              <td>1.6%</td>
              <td>0</td>
              <td>3.73 MB</td>
              <td>21.27 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>html5.custom.attributes.index</td>
              <td>6</td>
              <td>1.1%</td>
              <td>0</td>
              <td>562.42 kB</td>
              <td>4.74 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>css.template.selectors</td>
              <td>6</td>
              <td>1.0%</td>
              <td>0</td>
              <td>562.42 kB</td>
              <td>5.03 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>WebComponentsIndex</td>
              <td>6</td>
              <td>&lt; 1%</td>
              <td>0</td>
              <td>562.42 kB</td>
              <td>6.92 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>HtmlTagIdIndex</td>
              <td>6</td>
              <td>&lt; 1%</td>
              <td>0</td>
              <td>562.42 kB</td>
              <td>7.5 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>HtmlScriptSrcIndex</td>
              <td>6</td>
              <td>&lt; 1%</td>
              <td>0</td>
              <td>562.42 kB</td>
              <td>7.86 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>json.file.root.values</td>
              <td>44</td>
              <td>&lt; 1%</td>
              <td>0</td>
              <td>672.84 kB</td>
              <td>9.63 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>js.string.literal.words.index</td>
              <td>128</td>
              <td>&lt; 1%</td>
              <td>0</td>
              <td>2.01 MB</td>
              <td>242.48 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>js.custom.single.entry.index</td>
              <td>134</td>
              <td>&lt; 1%</td>
              <td>0</td>
              <td>2.57 MB</td>
              <td>319.78 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>js.test.names.extensible</td>
              <td>128</td>
              <td>&lt; 1%</td>
              <td>0</td>
              <td>2.01 MB</td>
              <td>261.88 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>FrameworkDetectionIndex</td>
              <td>41</td>
              <td>&lt; 1%</td>
              <td>0</td>
              <td>671.44 kB</td>
              <td>94.24 MB/s</td>
            </tr>
            <tr class="minor-data invisible">
              <td>TypeScriptExternalPathCandidates</td>
              <td>2</td>
              <td>&lt; 1%</td>
              <td>0</td>
              <td>1.03 kB</td>
              <td>460.73 kB/s</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div id="id-indexing">
        <table class="table-with-margin activity-table">
          <thead>
            <tr>
              <th colspan="6">Indexing</th>
            </tr>
            <tr>
              <th>Provider name</th>
              <th>Total processing time</th>
              <th>Content loading time</th>
              <th>Number of indexed files</th>
              <th>Number of files indexed by shared indexes</th>
              <th>Number of too large for indexing files</th>
            </tr>
          </thead>
          <tbody>
            <tr class="">
              <td>Refreshed files</td>
              <td>7 s 865 ms</td>
              <td>65 ms</td>
              <td>319</td>
              <td>0</td>
              <td>1</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>
