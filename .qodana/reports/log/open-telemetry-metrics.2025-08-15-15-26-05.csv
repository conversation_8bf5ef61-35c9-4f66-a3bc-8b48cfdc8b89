# OpenTelemetry Metrics report: .csv, 4 fields: 
# <metric name>, <period start, nanoseconds>, <period end, nanoseconds>, <metric value>
# See CsvMetricsExporter for details.
# 
# NAME, PERIOD_START_NANOS, PERIOD_END_NANOS, VALUE
FilePageCache.capacityInBytes,1755271565444000000,1755271625459000000,629145600
MappedFileStorage.storages,1755271565444000000,1755271625459000000,16
StreamlinedBlobStorage.recordsAllocated,1755271565444000000,1755271625459000000,1492
DirectByteBufferAllocator.reclaimed,1755271565444000000,1755271625459000000,3
FilePageCache.maxRegisteredFiles,1755271565444000000,1755271625459000000,932
FilePageCache.totalPageLoadsUs,1755271565444000000,1755271625459000000,1410854
StorageLockContext.competingThreads.90P,1755271565444000000,1755271625459000000,0
StreamlinedBlobStorage.recordsRelocated,1755271565444000000,1755271625459000000,0
MappedFileStorage.totalPagesMapped,1755271565444000000,1755271625459000000,16
FilePageCache.maxCacheSizeInBytes,1755271565444000000,1755271625459000000,477102080
FilePageCache.uncachedFileAccess,1755271565444000000,1755271625459000000,548
FileChannelInterruptsRetryer.totalRetriedAttempts,1755271565444000000,1755271625459000000,0
DirectByteBufferAllocator.totalSizeOfBuffersCachedInBytes,1755271565444000000,1755271625459000000,0
DirectByteBufferAllocator.misses,1755271565444000000,1755271625459000000,455
FilePageCache.pageLoadsAboveSizeThreshold,1755271565444000000,1755271625459000000,0
DirectByteBufferAllocator.totalSizeOfBuffersAllocatedInBytes,1755271565444000000,1755271625459000000,477102080
FilePageCache.pageHits,1755271565444000000,1755271625459000000,0
StorageLockContext.competingThreads.avg,1755271565444000000,1755271625459000000,0.034482758620689655
DirectByteBufferAllocator.hits,1755271565444000000,1755271625459000000,3
FilePageCache.totalPageDisposalsUs,1755271565444000000,1755271625459000000,4781
DirectByteBufferAllocator.disposed,1755271565444000000,1755271625459000000,0
FilePageCache.disposedBuffers,1755271565444000000,1755271625459000000,3
FilePageCache.pageLoads,1755271565444000000,1755271625459000000,458
FilePageCache.pageFastCacheHits,1755271565444000000,1755271625459000000,788793
StreamlinedBlobStorage.totalLiveRecordsPayloadBytes,1755271565444000000,1755271625459000000,273159
StreamlinedBlobStorage.recordsDeleted,1755271565444000000,1755271625459000000,27
StreamlinedBlobStorage.totalLiveRecordsCapacityBytes,1755271565444000000,1755271625459000000,416748
MappedFileStorage.totalTimeSpentOnMappingUs,1755271565444000000,1755271625459000000,14974
StorageLockContext.competingThreads.max,1755271565444000000,1755271625459000000,2
MappedFileStorage.totalBytesMapped,1755271565444000000,1755271625459000000,202637312
workspaceModel.moduleManagerBridge.get.modules.ms,1755271565444000000,1755271625459000000,2
workspaceModel.mutableEntityStorage.to.snapshot.ms,1755271565444000000,1755271625459000000,3
workspaceModel.orphan.listener.update.ms,1755271565444000000,1755271625459000000,4
workspaceModel.mutableEntityStorage.put.entity.ms,1755271565444000000,1755271625459000000,28
workspaceModel.moduleBridge.before.changed.ms,1755271565444000000,1755271625459000000,0
workspaceModel.moduleManagerBridge.set.unloadedModules.ms,1755271565444000000,1755271625459000000,0
jps.artifact.entities.serializer.save.entities.ms,1755271565444000000,1755271625459000000,0
workspaceModel.moduleManagerBridge.new.nonPersistent.module.ms,1755271565444000000,1755271625459000000,0
workspaceModel.moduleManagerBridge.create.module.instance.ms,1755271565444000000,1755271625459000000,0
jps.global.initialize.library.bridges.after.loading.ms,1755271565444000000,1755271625459000000,1
workspaceModel.mutableEntityStorage.resolve.ms,1755271565444000000,1755271625459000000,0
workspaceModel.save.cache.to.file.ms,1755271565444000000,1755271625459000000,250
jps.facet.change.listener.process.change.events.ms,1755271565444000000,1755271625459000000,0
jps.artifact.entities.serializer.load.entities.ms,1755271565444000000,1755271625459000000,0
workspaceModel.moduleBridgeLoader.loading.modules.ms,1755271565444000000,1755271625459000000,210
jps.reload.project.entities.ms,1755271565444000000,1755271625459000000,97
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedChild.ms,1755271565444000000,1755271625459000000,2
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewParent.ms,1755271565444000000,1755271625459000000,7
workspaceModel.load.cache.metadata.from.file.ms,1755271565444000000,1755271625459000000,25
workspaceModel.mutableEntityStorage.mutable.vfurl.index.ms,1755271565444000000,1755271625459000000,0
workspaceModel.delayed.project.synchronizer.sync.ms,1755271565444000000,1755271625459000000,5443
jps.global.get.library.by.name.ms,1755271565444000000,1755271625459000000,0
workspaceModel.global.apply.state.to.project.builder.ms,1755271565444000000,1755271625459000000,0
jps.library.entities.serializer.save.entities.ms,1755271565444000000,1755271625459000000,0
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedParent.ms,1755271565444000000,1755271625459000000,1
workspaceModel.mutableEntityStorage.replace.by.source.ms,1755271565444000000,1755271625459000000,97
workspaceModel.mutableEntityStorage.entities.ms,1755271565444000000,1755271625459000000,0
jps.global.get.library.ms,1755271565444000000,1755271625459000000,0
jps.facet.change.listener.before.change.events.ms,1755271565444000000,1755271625459000000,0
workspaceModel.moduleManagerBridge.build.module.graph.ms,1755271565444000000,1755271625459000000,1
jps.app.storage.content.writer.save.component.ms,1755271565444000000,1755271625459000000,1
jps.app.storage.content.reader.load.component.ms,1755271565444000000,1755271625459000000,0
workspaceModel.mutableEntityStorage.remove.entity.ms,1755271565444000000,1755271625459000000,0
jps.module.iml.entities.serializer.save.entities.ms,1755271565444000000,1755271625459000000,0
jps.global.get.libraries.ms,1755271565444000000,1755271625459000000,0
jps.module.iml.entities.serializer.load.entities.ms,1755271565444000000,1755271625459000000,64
jps.project.serializers.save.ms,1755271565444000000,1755271625459000000,1
workspaceModel.moduleManagerBridge.load.all.modules.ms,1755271565444000000,1755271625459000000,46
jps.apply.loaded.storage.ms,1755271565444000000,1755271625459000000,5310
jps.global.handle.before.change.events.ms,1755271565444000000,1755271625459000000,0
workspaceModel.sync.entities.ms,1755271565444000000,1755271625459000000,0
jps.save.global.entities.ms,1755271565444000000,1755271625459000000,7
workspaceModel.moduleBridge.facet.initialization.ms,1755271565444000000,1755271625459000000,1
jps.library.entities.serializer.load.entities.ms,1755271565444000000,1755271625459000000,0
jps.storage.jps.conf.reader.load.component.ms,1755271565444000000,1755271625459000000,8
jps.load.initial.state.ms,1755271565444000000,1755271625459000000,17
workspaceModel.mutableEntityStorage.apply.changes.from.ms,1755271565444000000,1755271625459000000,33
workspaceModel.mutableEntityStorage.add.entity.ms,1755271565444000000,1755271625459000000,104
workspaceModel.mutableEntityStorage.mutable.ext.mapping.ms,1755271565444000000,1755271625459000000,1
workspaceModel.moduleManagerBridge.newModule.ms,1755271565444000000,1755271625459000000,0
jps.load.project.to.empty.storage.ms,1755271565444000000,1755271625459000000,123
workspaceModel.mutableEntityStorage.collect.changes.ms,1755271565444000000,1755271625459000000,1
workspaceModel.do.save.caches.ms,1755271565444000000,1755271625459000000,283
workspaceModel.mutableEntityStorage.referrers.ms,1755271565444000000,1755271625459000000,0
workspaceModel.moduleManagerBridge.load.module.ms,1755271565444000000,1755271625459000000,0
workspaceModel.moduleBridge.update.option.ms,1755271565444000000,1755271625459000000,0
jps.facet.change.listener.init.bridge.ms,1755271565444000000,1755271625459000000,0
workspaceModel.global.updates.count,1755271565444000000,1755271625459000000,1
workspaceModel.mutableEntityStorage.instances.count,1755271565444000000,1755271625459000000,50
jps.project.serializers.load.ms,1755271565444000000,1755271625459000000,118
workspaceModel.mutableEntityStorage.has.same.entities.ms,1755271565444000000,1755271625459000000,0
jps.global.initialize.library.bridges.ms,1755271565444000000,1755271625459000000,1
workspaceModel.mutableEntityStorage.entities.by.source.ms,1755271565444000000,1755271625459000000,0
workspaceModel.load.cache.from.file.ms,1755271565444000000,1755271625459000000,174
jps.global.handle.changed.events.ms,1755271565444000000,1755271625459000000,0
workspaceModel.mutableEntityStorage.modify.entity.ms,1755271565444000000,1755271625459000000,11
workspaceModel.global.updates.ms,1755271565444000000,1755271625459000000,7
workspaceModel.global.apply.state.to.project.ms,1755271565444000000,1755271625459000000,53
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewChild.ms,1755271565444000000,1755271625459000000,12
jps.module.dependency.index.workspace.model.listener.on.changed.ms,1755271565444000000,1755271625459000000,0
jps.save.changed.project.entities.ms,1755271565444000000,1755271625459000000,1
JVM.totalCpuTimeMs,1755271565444000000,1755271625459000000,117980
JVM.totalSafepointCount,1755271565444000000,1755271625459000000,240
JVM.threadCount,1755271565444000000,1755271625459000000,114
JVM.maxThreadCount,1755271565444000000,1755271625459000000,116
JVM.GC.collections,1755271565444000000,1755271625459000000,65
JVM.totalTimeToSafepointsMs,1755271565444000000,1755271625459000000,101
JVM.totalBytesAllocated,1755271565444000000,1755271625459000000,4784063152
JVM.totalTimeAtSafepointsMs,1755271565444000000,1755271625459000000,1787
MEM.ramMinusFileMappingsBytes,1755271565444000000,1755271625459000000,2247233536
JVM.newThreadsCount,1755271565444000000,1755271625459000000,147
JVM.totalDirectByteBuffersBytes,1755271565444000000,1755271625459000000,490160145
JVM.usedNativeBytes,1755271565444000000,1755271625459000000,392497552
MEM.ramBytes,1755271565444000000,1755271625459000000,2668703744
MEM.ramPlusSwapMinusFileMappingsBytes,1755271565444000000,1755271625459000000,2247233536
JVM.committedHeapBytes,1755271565444000000,1755271625459000000,1149239296
JVM.usedHeapBytes,1755271565444000000,1755271625459000000,884998144
JVM.maxHeapBytes,1755271565444000000,1755271625459000000,5834276864
OS.loadAverage,1755271565444000000,1755271625459000000,3.28466796875
MEM.fileMappingsRamBytes,1755271565444000000,1755271625459000000,421470208
JVM.GC.collectionTimesMs,1755271565444000000,1755271625459000000,1467
workspaceModel.initializing.ms,1755271565444000000,1755271625459000000,2
workspaceModel.loading.from.cache.ms,1755271565444000000,1755271625459000000,179
workspaceModel.workspaceFileIndexData.instances.count,1755271565444000000,1755271625459000000,1
workspaceModel.cachedValueWithParameters.from.cache.count,1755271565444000000,1755271625459000000,0
workspaceModel.cachedValueWithParameters.total.get.count,1755271565444000000,1755271625459000000,0
workspaceModel.workspaceFileIndexContributor.registerFileSets.ms,1755271565444000000,1755271625459000000,259
workspaceModel.collect.changes.ms,1755271565444000000,1755271625459000000,0
workspaceModel.on.changed.ms,1755271565444000000,1755271625459000000,36
workspaceModel.pre.handlers.ms,1755271565444000000,1755271625459000000,2
workspaceModel.replace.project.model.ms,1755271565444000000,1755271625459000000,0
workspaceModel.workspaceFileIndexData.visitFileSets.ms,1755271565444000000,1755271625459000000,8
workspaceModel.workspaceFileIndexData.init.ms,1755271565444000000,1755271625459000000,80
workspaceModel.cachedValueWithParameters.clear.count,1755271565444000000,1755271625459000000,0
workspaceModel.cachedValue.clear.count,1755271565444000000,1755271625459000000,2
workspaceModel.cachedValue.calculated.count,1755271565444000000,1755271625459000000,17
workspaceModel.updates.ms,1755271565444000000,1755271625459000000,206
workspaceModel.cachedValueWithParameters.calculated.count,1755271565444000000,1755271625459000000,0
workspaceModel.updates.precise.ms,1755271565444000000,1755271625459000000,19
workspaceModel.workspaceFileIndexData.getPackageName.ms,1755271565444000000,1755271625459000000,0
workspaceModel.entityStorageSnapshotImpl.instances.count,1755271565444000000,1755271625459000000,35
workspaceModel.update.unloaded.entities.ms,1755271565444000000,1755271625459000000,0
workspaceModel.to.snapshot.ms,1755271565444000000,1755271625459000000,0
workspaceModel.cachedValue.total.get.count,1755271565444000000,1755271625459000000,137
workspaceModel.workspaceFileIndexData.markDirty.ms,1755271565444000000,1755271625459000000,0
workspaceModel.loading.total.ms,1755271565444000000,1755271625459000000,190
workspaceModel.workspaceFileIndexData.getFileInfo.ms,1755271565444000000,1755271625459000000,391
workspaceModel.workspaceFileIndexData.updateDirtyEntities.ms,1755271565444000000,1755271625459000000,0
workspaceModel.updates.count,1755271565444000000,1755271625459000000,6
workspaceModel.workspaceFileIndexData.onEntitiesChanged.ms,1755271565444000000,1755271625459000000,106
workspaceModel.check.recursive.update.ms,1755271565444000000,1755271625459000000,2
workspaceModel.cachedValue.from.cache.count,1755271565444000000,1755271625459000000,120
workspaceModel.on.before.changed.ms,1755271565444000000,1755271625459000000,40
workspaceModel.workspaceFileIndexData.getDirectoriesByPackageName.ms,1755271565444000000,1755271625459000000,0
workspaceModel.full.replace.project.model.ms,1755271565444000000,1755271625459000000,14
workspaceModel.init.bridges.ms,1755271565444000000,1755271625459000000,2
workspaceModel.workspaceFileIndexData.processFileSets.ms,1755271565444000000,1755271625459000000,0
AWTEventQueue.dispatchTimeTotalNS,1755271565444000000,1755271625459000000,7091103724
AWTEventQueue.dispatchTime90PNs,1755271565444000000,1755271625459000000,14352383
AWTEventQueue.dispatchTimeAvgNs,1755271565444000000,1755271625459000000,2.864545393548387E7
FlushQueue.queueSizeMax,1755271565444000000,1755271625459000000,24
FlushQueue.executionTime90PNs,1755271565444000000,1755271625459000000,8060927
NonBlockingReadAction.finalizedExecutionTimeUs,1755271565444000000,1755271625459000000,627397
FlushQueue.executionTimeMaxNs,1755271565444000000,1755271625459000000,5469372415
FlushQueue.waitingTimeAvgNs,1755271565444000000,1755271625459000000,2.499324151202749E8
ReadAction.executionsCount,1755271565444000000,1755271625459000000,53188
AWTEventQueue.eventsDispatched,1755271565444000000,1755271625459000000,248
FlushQueue.waitingTime90PNs,1755271565444000000,1755271625459000000,89653247
WriteAction.executionsCount,1755271565444000000,1755271625459000000,37
AWTEventQueue.dispatchTimeMaxNs,1755271565444000000,1755271625459000000,5469372415
FlushQueue.waitingTimeMaxNs,1755271565444000000,1755271625459000000,5234491391
NonBlockingReadAction.failedExecutionTimeUs,1755271565444000000,1755271625459000000,36776
NonBlockingReadAction.failedExecutionsCount,1755271565444000000,1755271625459000000,2
NonBlockingReadAction.finalizedExecutionsCount,1755271565444000000,1755271625459000000,27
FlushQueue.tasksExecuted,1755271565444000000,1755271625459000000,291
FlushQueue.executionTimeAvgNs,1755271565444000000,1755271625459000000,2.4036338941580757E7
FlushQueue.queueSize90P,1755271565444000000,1755271625459000000,8
FlushQueue.queueSizeAvg,1755271565444000000,1755271625459000000,2.6323024054982818
Indexes.entries.lookups,1755271565444000000,1755271625459000000,1731
Indexing.writer_2.totalTimeSpentWritingMs,1755271565444000000,1755271625459000000,0
Indexing.writer_0.totalTimeSpentWritingMs,1755271565444000000,1755271625459000000,0
Indexes.allKeys.lookupDurationMaxMs,1755271565444000000,1755271625459000000,2.0
Indexing.writer_3.totalTimeSpentWritingMs,1755271565444000000,1755271625459000000,0
Indexing.cache.totalCacheEvicted,1755271565444000000,1755271625459000000,331941
Indexes.entries.lookupDuration90PMs,1755271565444000000,1755271625459000000,0.0
Indexing.writesQueuedAvg,1755271565444000000,1755271625459000000,0.0
Indexing.cache.totalCacheAccesses,1755271565444000000,1755271625459000000,1183053
Indexes.entries.lookupDurationMaxMs,1755271565444000000,1755271625459000000,22.0
Indexes.stubs.lookups,1755271565444000000,1755271625459000000,4749
Indexes.stubs.lookupDuration90PMs,1755271565444000000,1755271625459000000,0.0
Indexes.allKeys.lookupDuration90PMs,1755271565444000000,1755271625459000000,2.0
Indexes.allKeys.lookups,1755271565444000000,1755271625459000000,1
Indexes.stubs.lookupDurationAvgMs,1755271565444000000,1755271625459000000,0.20121951219512196
Indexes.entries.lookupDurationAvgMs,1755271565444000000,1755271625459000000,0.058857472590882864
Indexing.cache.totalCacheMisses,1755271565444000000,1755271625459000000,333599
Indexing.totalTimeIndexersSleptMs,1755271565444000000,1755271625459000000,0
Indexing.writesQueuedMin,1755271565444000000,1755271625459000000,0
Indexing.writer_1.totalTimeSpentWritingMs,1755271565444000000,1755271625459000000,0
Indexes.allKeys.lookupDurationAvgMs,1755271565444000000,1755271625459000000,2.0
Indexing.writesQueuedMax,1755271565444000000,1755271625459000000,0
Indexes.stubs.lookupDurationMaxMs,1755271565444000000,1755271625459000000,26.0
cacheStateStorage.set.counter,1755271565444000000,1755271625459000000,13
cacheStateStorage.get.counter,1755271565444000000,1755271625459000000,12
cacheStateStorage.set.duration,1755271565444000000,1755271625459000000,89
cacheStateStorage.get.duration,1755271565444000000,1755271625459000000,0
VFS.contentStorage.recordsDeduplicated,1755271565444000000,1755271625459000000,340
VFS.fileChildByName,1755271565444000000,1755271625459000000,165099
VFS.contentStorage.recordsStoredSize,1755271565444000000,1755271625459000000,773538
FileNameCache.totalMisses,1755271565444000000,1755271625459000000,704
VFS.contentStorage.recordsStored,1755271565444000000,1755271625459000000,126
FileNameCache.queries,1755271565444000000,1755271625459000000,2175424
VFS.invertedFileNameIndex.requests,1755271565444000000,1755271625459000000,0
VFS.contentStorage.recordsUncompressedSize,1755271565444000000,1755271625459000000,1834251
VFS.fileByIdCache.misses,1755271565444000000,1755271625459000000,20142
VFS.fileByIdCache.hits,1755271565444000000,1755271625459000000,65
VFS.contentStorage.recordsReadDecompressed,1755271565444000000,1755271625459000000,114
VFS.contentStorage.recordsStoredCompressed,1755271565444000000,1755271625459000000,23
VFS.contentStorage.recordsDecompressionTimeUs,1755271565444000000,1755271625459000000,72791
VFS.contentStorage.recordsRead,1755271565444000000,1755271625459000000,266
VFS.contentStorage.recordsReadSize,1755271565444000000,1755271625459000000,6412671
parser.HTML.size.bytes,1755271565444000000,1755271625459000000,1114738
writeAction.count,1755271565444000000,1755271625459000000,37
parser.yaml.time.ns,1755271565444000000,1755271625459000000,6889990
lexer.yaml.time.ns,1755271565444000000,1755271625459000000,1673397
parser.HTML.time.ns,1755271565444000000,1755271625459000000,50667308
writeAction.max.wait.ms,1755271565444000000,1755271625459000000,32
lexer.TypeScript.JSX.time.ns,1755271565444000000,1755271625459000000,70969330
lexer.CSS.size.bytes,1755271565444000000,1755271625459000000,753296
parser.Shell.Script.size.bytes,1755271565444000000,1755271625459000000,3898
parser.CSS.size.bytes,1755271565444000000,1755271625459000000,753296
lexer.Shell.Script.size.bytes,1755271565444000000,1755271625459000000,3898
DiskQueryRelay.tasksRequested,1755271565444000000,1755271625459000000,3592
DiskQueryRelay.taskExecutionTotalTimeUs,1755271565444000000,1755271625459000000,1269142
parser.TypeScript.size.bytes,1755271565444000000,1755271625459000000,21141704
lexer.TypeScript.JSX.size.bytes,1755271565444000000,1755271625459000000,228573
lexer.CSS.time.ns,1755271565444000000,1755271625459000000,90013449
parser.JSON.size.bytes,1755271565444000000,1755271625459000000,136676
writeAction.wait.ms,1755271565444000000,1755271625459000000,84
lexer.JSON.time.ns,1755271565444000000,1755271625459000000,8535286
lexer.TypeScript.size.bytes,1755271565444000000,1755271625459000000,18250135
lexer.HTML.size.bytes,1755271565444000000,1755271625459000000,1114738
DiskQueryRelay.tasksExecuted,1755271565444000000,1755271625459000000,3592
lexer.HTML.time.ns,1755271565444000000,1755271625459000000,142437484
parser.TypeScript.JSX.size.bytes,1755271565444000000,1755271625459000000,357185
DiskQueryRelay.taskWaitingTotalTimeUs,1755271565444000000,1755271625459000000,1557672
parser.yaml.size.bytes,1755271565444000000,1755271625459000000,7390
parser.Shell.Script.time.ns,1755271565444000000,1755271625459000000,37427664
parser.TypeScript.time.ns,1755271565444000000,1755271625459000000,4461244513
parser.CSS.time.ns,1755271565444000000,1755271625459000000,285313315
lexer.ECMAScript.6.size.bytes,1755271565444000000,1755271625459000000,2323002
writeAction.median.wait.ms,1755271565444000000,1755271625459000000,0
parser.ECMAScript.6.size.bytes,1755271565444000000,1755271625459000000,6325970
lexer.ECMAScript.6.time.ns,1755271565444000000,1755271625459000000,155720607
parser.TypeScript.JSX.time.ns,1755271565444000000,1755271625459000000,197731068
lexer.yaml.size.bytes,1755271565444000000,1755271625459000000,7390
lexer.Shell.Script.time.ns,1755271565444000000,1755271625459000000,640340
lexer.TypeScript.time.ns,1755271565444000000,1755271625459000000,937359919
parser.JSON.time.ns,1755271565444000000,1755271625459000000,110047727
parser.ECMAScript.6.time.ns,1755271565444000000,1755271625459000000,844588792
lexer.JSON.size.bytes,1755271565444000000,1755271625459000000,136676
FilePageCache.capacityInBytes,1755271625459000000,1755271685458000000,629145600
MappedFileStorage.storages,1755271625459000000,1755271685458000000,16
StreamlinedBlobStorage.recordsAllocated,1755271625459000000,1755271685458000000,212
DirectByteBufferAllocator.reclaimed,1755271625459000000,1755271685458000000,0
FilePageCache.maxRegisteredFiles,1755271625459000000,1755271685458000000,932
FilePageCache.totalPageLoadsUs,1755271625459000000,1755271685458000000,11541
StorageLockContext.competingThreads.90P,1755271625459000000,1755271685458000000,0
StreamlinedBlobStorage.recordsRelocated,1755271625459000000,1755271685458000000,0
MappedFileStorage.totalPagesMapped,1755271625459000000,1755271685458000000,16
FilePageCache.maxCacheSizeInBytes,1755271625459000000,1755271685458000000,482344960
FilePageCache.uncachedFileAccess,1755271625459000000,1755271685458000000,104
FileChannelInterruptsRetryer.totalRetriedAttempts,1755271625459000000,1755271685458000000,0
DirectByteBufferAllocator.totalSizeOfBuffersCachedInBytes,1755271625459000000,1755271685458000000,0
DirectByteBufferAllocator.misses,1755271625459000000,1755271685458000000,5
FilePageCache.pageLoadsAboveSizeThreshold,1755271625459000000,1755271685458000000,0
DirectByteBufferAllocator.totalSizeOfBuffersAllocatedInBytes,1755271625459000000,1755271685458000000,482344960
FilePageCache.pageHits,1755271625459000000,1755271685458000000,0
StorageLockContext.competingThreads.avg,1755271625459000000,1755271685458000000,0.0
DirectByteBufferAllocator.hits,1755271625459000000,1755271685458000000,0
FilePageCache.totalPageDisposalsUs,1755271625459000000,1755271685458000000,14
DirectByteBufferAllocator.disposed,1755271625459000000,1755271685458000000,0
FilePageCache.disposedBuffers,1755271625459000000,1755271685458000000,0
FilePageCache.pageLoads,1755271625459000000,1755271685458000000,5
FilePageCache.pageFastCacheHits,1755271625459000000,1755271685458000000,65887
StreamlinedBlobStorage.totalLiveRecordsPayloadBytes,1755271625459000000,1755271685458000000,199560
StreamlinedBlobStorage.recordsDeleted,1755271625459000000,1755271685458000000,1
StreamlinedBlobStorage.totalLiveRecordsCapacityBytes,1755271625459000000,1755271685458000000,265452
MappedFileStorage.totalTimeSpentOnMappingUs,1755271625459000000,1755271685458000000,14974
StorageLockContext.competingThreads.max,1755271625459000000,1755271685458000000,0
MappedFileStorage.totalBytesMapped,1755271625459000000,1755271685458000000,202637312
workspaceModel.moduleManagerBridge.get.modules.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.to.snapshot.ms,1755271625459000000,1755271685458000000,0
workspaceModel.orphan.listener.update.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.put.entity.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleBridge.before.changed.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleManagerBridge.set.unloadedModules.ms,1755271625459000000,1755271685458000000,0
jps.artifact.entities.serializer.save.entities.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleManagerBridge.new.nonPersistent.module.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleManagerBridge.create.module.instance.ms,1755271625459000000,1755271685458000000,0
jps.global.initialize.library.bridges.after.loading.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.resolve.ms,1755271625459000000,1755271685458000000,0
workspaceModel.save.cache.to.file.ms,1755271625459000000,1755271685458000000,0
jps.facet.change.listener.process.change.events.ms,1755271625459000000,1755271685458000000,0
jps.artifact.entities.serializer.load.entities.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleBridgeLoader.loading.modules.ms,1755271625459000000,1755271685458000000,0
jps.reload.project.entities.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedChild.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewParent.ms,1755271625459000000,1755271685458000000,0
workspaceModel.load.cache.metadata.from.file.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.mutable.vfurl.index.ms,1755271625459000000,1755271685458000000,0
workspaceModel.delayed.project.synchronizer.sync.ms,1755271625459000000,1755271685458000000,0
jps.global.get.library.by.name.ms,1755271625459000000,1755271685458000000,0
workspaceModel.global.apply.state.to.project.builder.ms,1755271625459000000,1755271685458000000,0
jps.library.entities.serializer.save.entities.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedParent.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.replace.by.source.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.entities.ms,1755271625459000000,1755271685458000000,0
jps.global.get.library.ms,1755271625459000000,1755271685458000000,0
jps.facet.change.listener.before.change.events.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleManagerBridge.build.module.graph.ms,1755271625459000000,1755271685458000000,0
jps.app.storage.content.writer.save.component.ms,1755271625459000000,1755271685458000000,0
jps.app.storage.content.reader.load.component.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.remove.entity.ms,1755271625459000000,1755271685458000000,0
jps.module.iml.entities.serializer.save.entities.ms,1755271625459000000,1755271685458000000,0
jps.global.get.libraries.ms,1755271625459000000,1755271685458000000,0
jps.module.iml.entities.serializer.load.entities.ms,1755271625459000000,1755271685458000000,0
jps.project.serializers.save.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleManagerBridge.load.all.modules.ms,1755271625459000000,1755271685458000000,0
jps.apply.loaded.storage.ms,1755271625459000000,1755271685458000000,0
jps.global.handle.before.change.events.ms,1755271625459000000,1755271685458000000,0
workspaceModel.sync.entities.ms,1755271625459000000,1755271685458000000,0
jps.save.global.entities.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleBridge.facet.initialization.ms,1755271625459000000,1755271685458000000,0
jps.library.entities.serializer.load.entities.ms,1755271625459000000,1755271685458000000,0
jps.storage.jps.conf.reader.load.component.ms,1755271625459000000,1755271685458000000,0
jps.load.initial.state.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.apply.changes.from.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.add.entity.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.mutable.ext.mapping.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleManagerBridge.newModule.ms,1755271625459000000,1755271685458000000,0
jps.load.project.to.empty.storage.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.collect.changes.ms,1755271625459000000,1755271685458000000,0
workspaceModel.do.save.caches.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.referrers.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleManagerBridge.load.module.ms,1755271625459000000,1755271685458000000,0
workspaceModel.moduleBridge.update.option.ms,1755271625459000000,1755271685458000000,0
jps.facet.change.listener.init.bridge.ms,1755271625459000000,1755271685458000000,0
workspaceModel.global.updates.count,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.instances.count,1755271625459000000,1755271685458000000,0
jps.project.serializers.load.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.has.same.entities.ms,1755271625459000000,1755271685458000000,0
jps.global.initialize.library.bridges.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.entities.by.source.ms,1755271625459000000,1755271685458000000,0
workspaceModel.load.cache.from.file.ms,1755271625459000000,1755271685458000000,0
jps.global.handle.changed.events.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.modify.entity.ms,1755271625459000000,1755271685458000000,0
workspaceModel.global.updates.ms,1755271625459000000,1755271685458000000,0
workspaceModel.global.apply.state.to.project.ms,1755271625459000000,1755271685458000000,0
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewChild.ms,1755271625459000000,1755271685458000000,0
jps.module.dependency.index.workspace.model.listener.on.changed.ms,1755271625459000000,1755271685458000000,0
jps.save.changed.project.entities.ms,1755271625459000000,1755271685458000000,0
JVM.totalCpuTimeMs,1755271625459000000,1755271685458000000,118790
JVM.totalSafepointCount,1755271625459000000,1755271685458000000,118
JVM.threadCount,1755271625459000000,1755271685458000000,52
JVM.maxThreadCount,1755271625459000000,1755271685458000000,116
JVM.GC.collections,1755271625459000000,1755271685458000000,29
JVM.totalTimeToSafepointsMs,1755271625459000000,1755271685458000000,28
JVM.totalBytesAllocated,1755271625459000000,1755271685458000000,8559414968
JVM.totalTimeAtSafepointsMs,1755271625459000000,1755271685458000000,1697
MEM.ramMinusFileMappingsBytes,1755271625459000000,1755271685458000000,2847268864
JVM.newThreadsCount,1755271625459000000,1755271685458000000,5
JVM.totalDirectByteBuffersBytes,1755271625459000000,1755271685458000000,492411660
JVM.usedNativeBytes,1755271625459000000,1755271685458000000,427095456
MEM.ramBytes,1755271625459000000,1755271685458000000,3280662528
MEM.ramPlusSwapMinusFileMappingsBytes,1755271625459000000,1755271685458000000,2847268864
JVM.committedHeapBytes,1755271625459000000,1755271685458000000,1669332992
JVM.usedHeapBytes,1755271625459000000,1755271685458000000,1163445808
JVM.maxHeapBytes,1755271625459000000,1755271685458000000,5834276864
OS.loadAverage,1755271625459000000,1755271685458000000,3.9638671875
MEM.fileMappingsRamBytes,1755271625459000000,1755271685458000000,433393664
JVM.GC.collectionTimesMs,1755271625459000000,1755271685458000000,1481
workspaceModel.initializing.ms,1755271625459000000,1755271685458000000,0
workspaceModel.loading.from.cache.ms,1755271625459000000,1755271685458000000,0
workspaceModel.workspaceFileIndexData.instances.count,1755271625459000000,1755271685458000000,0
workspaceModel.cachedValueWithParameters.from.cache.count,1755271625459000000,1755271685458000000,0
workspaceModel.cachedValueWithParameters.total.get.count,1755271625459000000,1755271685458000000,0
workspaceModel.workspaceFileIndexContributor.registerFileSets.ms,1755271625459000000,1755271685458000000,0
workspaceModel.collect.changes.ms,1755271625459000000,1755271685458000000,0
workspaceModel.on.changed.ms,1755271625459000000,1755271685458000000,0
workspaceModel.pre.handlers.ms,1755271625459000000,1755271685458000000,0
workspaceModel.replace.project.model.ms,1755271625459000000,1755271685458000000,0
workspaceModel.workspaceFileIndexData.visitFileSets.ms,1755271625459000000,1755271685458000000,0
workspaceModel.workspaceFileIndexData.init.ms,1755271625459000000,1755271685458000000,0
workspaceModel.cachedValueWithParameters.clear.count,1755271625459000000,1755271685458000000,0
workspaceModel.cachedValue.clear.count,1755271625459000000,1755271685458000000,0
workspaceModel.cachedValue.calculated.count,1755271625459000000,1755271685458000000,0
workspaceModel.updates.ms,1755271625459000000,1755271685458000000,0
workspaceModel.cachedValueWithParameters.calculated.count,1755271625459000000,1755271685458000000,0
workspaceModel.updates.precise.ms,1755271625459000000,1755271685458000000,0
workspaceModel.workspaceFileIndexData.getPackageName.ms,1755271625459000000,1755271685458000000,0
workspaceModel.entityStorageSnapshotImpl.instances.count,1755271625459000000,1755271685458000000,0
workspaceModel.update.unloaded.entities.ms,1755271625459000000,1755271685458000000,0
workspaceModel.to.snapshot.ms,1755271625459000000,1755271685458000000,0
workspaceModel.cachedValue.total.get.count,1755271625459000000,1755271685458000000,134
workspaceModel.workspaceFileIndexData.markDirty.ms,1755271625459000000,1755271685458000000,0
workspaceModel.loading.total.ms,1755271625459000000,1755271685458000000,0
workspaceModel.workspaceFileIndexData.getFileInfo.ms,1755271625459000000,1755271685458000000,831
workspaceModel.workspaceFileIndexData.updateDirtyEntities.ms,1755271625459000000,1755271685458000000,0
workspaceModel.updates.count,1755271625459000000,1755271685458000000,0
workspaceModel.workspaceFileIndexData.onEntitiesChanged.ms,1755271625459000000,1755271685458000000,0
workspaceModel.check.recursive.update.ms,1755271625459000000,1755271685458000000,0
workspaceModel.cachedValue.from.cache.count,1755271625459000000,1755271685458000000,134
workspaceModel.on.before.changed.ms,1755271625459000000,1755271685458000000,0
workspaceModel.workspaceFileIndexData.getDirectoriesByPackageName.ms,1755271625459000000,1755271685458000000,0
workspaceModel.full.replace.project.model.ms,1755271625459000000,1755271685458000000,0
workspaceModel.init.bridges.ms,1755271625459000000,1755271685458000000,0
workspaceModel.workspaceFileIndexData.processFileSets.ms,1755271625459000000,1755271685458000000,0
AWTEventQueue.dispatchTimeTotalNS,1755271625459000000,1755271685458000000,351185809
AWTEventQueue.dispatchTime90PNs,1755271625459000000,1755271685458000000,1302527
AWTEventQueue.dispatchTimeAvgNs,1755271625459000000,1755271685458000000,2721446.697674419
FlushQueue.queueSizeMax,1755271625459000000,1755271685458000000,3
FlushQueue.executionTime90PNs,1755271625459000000,1755271685458000000,159743
NonBlockingReadAction.finalizedExecutionTimeUs,1755271625459000000,1755271685458000000,17965
FlushQueue.executionTimeMaxNs,1755271625459000000,1755271685458000000,293601279
FlushQueue.waitingTimeAvgNs,1755271625459000000,1755271685458000000,3074328.222222222
ReadAction.executionsCount,1755271625459000000,1755271685458000000,94027
AWTEventQueue.eventsDispatched,1755271625459000000,1755271685458000000,129
FlushQueue.waitingTime90PNs,1755271625459000000,1755271685458000000,2490367
WriteAction.executionsCount,1755271625459000000,1755271685458000000,0
AWTEventQueue.dispatchTimeMaxNs,1755271625459000000,1755271685458000000,293601279
FlushQueue.waitingTimeMaxNs,1755271625459000000,1755271685458000000,291504127
NonBlockingReadAction.failedExecutionTimeUs,1755271625459000000,1755271685458000000,0
NonBlockingReadAction.failedExecutionsCount,1755271625459000000,1755271685458000000,0
NonBlockingReadAction.finalizedExecutionsCount,1755271625459000000,1755271685458000000,102
FlushQueue.tasksExecuted,1755271625459000000,1755271685458000000,144
FlushQueue.executionTimeAvgNs,1755271625459000000,1755271685458000000,2182362.4444444445
FlushQueue.queueSize90P,1755271625459000000,1755271685458000000,2
FlushQueue.queueSizeAvg,1755271625459000000,1755271685458000000,0.3541666666666667
Indexes.entries.lookups,1755271625459000000,1755271685458000000,17159
Indexing.writer_2.totalTimeSpentWritingMs,1755271625459000000,1755271685458000000,0
Indexing.writer_0.totalTimeSpentWritingMs,1755271625459000000,1755271685458000000,0
Indexes.allKeys.lookupDurationMaxMs,1755271625459000000,1755271685458000000,1.0
Indexing.writer_3.totalTimeSpentWritingMs,1755271625459000000,1755271685458000000,0
Indexing.cache.totalCacheEvicted,1755271625459000000,1755271685458000000,72017
Indexes.entries.lookupDuration90PMs,1755271625459000000,1755271685458000000,0.0
Indexing.writesQueuedAvg,1755271625459000000,1755271685458000000,0.0
Indexing.cache.totalCacheAccesses,1755271625459000000,1755271685458000000,669638
Indexes.entries.lookupDurationMaxMs,1755271625459000000,1755271685458000000,8.0
Indexes.stubs.lookups,1755271625459000000,1755271685458000000,195294
Indexes.stubs.lookupDuration90PMs,1755271625459000000,1755271685458000000,0.0
Indexes.allKeys.lookupDuration90PMs,1755271625459000000,1755271685458000000,1.0
Indexes.allKeys.lookups,1755271625459000000,1755271685458000000,3
Indexes.stubs.lookupDurationAvgMs,1755271625459000000,1755271685458000000,0.05573187176029655
Indexes.entries.lookupDurationAvgMs,1755271625459000000,1755271685458000000,0.02496937168193221
Indexing.cache.totalCacheMisses,1755271625459000000,1755271685458000000,77069
Indexing.totalTimeIndexersSleptMs,1755271625459000000,1755271685458000000,0
Indexing.writesQueuedMin,1755271625459000000,1755271685458000000,0
Indexing.writer_1.totalTimeSpentWritingMs,1755271625459000000,1755271685458000000,0
Indexes.allKeys.lookupDurationAvgMs,1755271625459000000,1755271685458000000,0.3333333333333333
Indexing.writesQueuedMax,1755271625459000000,1755271685458000000,0
Indexes.stubs.lookupDurationMaxMs,1755271625459000000,1755271685458000000,101.0
cacheStateStorage.set.counter,1755271625459000000,1755271685458000000,1
cacheStateStorage.get.counter,1755271625459000000,1755271685458000000,1
cacheStateStorage.set.duration,1755271625459000000,1755271685458000000,0
cacheStateStorage.get.duration,1755271625459000000,1755271685458000000,0
VFS.contentStorage.recordsDeduplicated,1755271625459000000,1755271685458000000,3
VFS.fileChildByName,1755271625459000000,1755271685458000000,1305184
VFS.contentStorage.recordsStoredSize,1755271625459000000,1755271685458000000,889676
FileNameCache.totalMisses,1755271625459000000,1755271685458000000,0
VFS.contentStorage.recordsStored,1755271625459000000,1755271685458000000,107
FileNameCache.queries,1755271625459000000,1755271685458000000,2199296
VFS.invertedFileNameIndex.requests,1755271625459000000,1755271685458000000,3
VFS.contentStorage.recordsUncompressedSize,1755271625459000000,1755271685458000000,2379437
VFS.fileByIdCache.misses,1755271625459000000,1755271685458000000,142100
VFS.fileByIdCache.hits,1755271625459000000,1755271685458000000,0
VFS.contentStorage.recordsReadDecompressed,1755271625459000000,1755271685458000000,102
VFS.contentStorage.recordsStoredCompressed,1755271625459000000,1755271685458000000,54
VFS.contentStorage.recordsDecompressionTimeUs,1755271625459000000,1755271685458000000,134005
VFS.contentStorage.recordsRead,1755271625459000000,1755271685458000000,183
VFS.contentStorage.recordsReadSize,1755271625459000000,1755271685458000000,39884097
parser.XML.time.ns,1755271625459000000,1755271685458000000,811408
lexer.yaml.time.ns,1755271625459000000,1755271685458000000,4019750
lexer.TypeScript.JSX.time.ns,1755271625459000000,1755271685458000000,6026429
parser.JSRegexp.size.bytes,1755271625459000000,1755271685458000000,778
parser.JSRegexp.time.ns,1755271625459000000,1755271685458000000,4249485
parser.Shell.Script.size.bytes,1755271625459000000,1755271685458000000,5868
parser.RELAX-NG.time.ns,1755271625459000000,1755271685458000000,1773243
lexer.RELAX-NG.size.bytes,1755271625459000000,1755271685458000000,117
lexer.Shell.Script.size.bytes,1755271625459000000,1755271685458000000,5868
DiskQueryRelay.taskExecutionTotalTimeUs,1755271625459000000,1755271685458000000,12171
parser.TypeScript.size.bytes,1755271625459000000,1755271685458000000,2830737
lexer.TypeScript.JSX.size.bytes,1755271625459000000,1755271685458000000,71748
parser.GenericSQL.size.bytes,1755271625459000000,1755271685458000000,3166
lexer.CSS.time.ns,1755271625459000000,1755271685458000000,12143525
lexer.IgnoreLang.size.bytes,1755271625459000000,1755271685458000000,313
lexer.TypeScript.size.bytes,1755271625459000000,1755271685458000000,2388483
lexer.HTML.size.bytes,1755271625459000000,1755271685458000000,532213
DiskQueryRelay.tasksExecuted,1755271625459000000,1755271685458000000,111
parser.textmate.size.bytes,1755271625459000000,1755271685458000000,25713
lexer.HTML.time.ns,1755271625459000000,1755271685458000000,102156437
parser.TypeScript.JSX.size.bytes,1755271625459000000,1755271685458000000,109496
DiskQueryRelay.taskWaitingTotalTimeUs,1755271625459000000,1755271685458000000,135011
lexer.GenericSQL.size.bytes,1755271625459000000,1755271685458000000,3163
parser.Shell.Script.time.ns,1755271625459000000,1755271685458000000,18796903
parser.IgnoreLang.time.ns,1755271625459000000,1755271685458000000,714116
lexer.IgnoreLang.time.ns,1755271625459000000,1755271685458000000,181056
parser.ECMAScript.6.size.bytes,1755271625459000000,1755271685458000000,2753585
lexer.RELAX-NG.time.ns,1755271625459000000,1755271685458000000,1294204
lexer.TypeScript.time.ns,1755271625459000000,1755271685458000000,154924281
parser.JSON.time.ns,1755271625459000000,1755271685458000000,197763467
parser.ECMAScript.6.time.ns,1755271625459000000,1755271685458000000,491236985
parser.HTML.size.bytes,1755271625459000000,1755271685458000000,532213
writeAction.count,1755271625459000000,1755271685458000000,0
parser.yaml.time.ns,1755271625459000000,1755271685458000000,8828908
parser.HTML.time.ns,1755271625459000000,1755271685458000000,22881547
writeAction.max.wait.ms,1755271625459000000,1755271685458000000,32
lexer.CSS.size.bytes,1755271625459000000,1755271685458000000,252193
parser.CSS.size.bytes,1755271625459000000,1755271685458000000,252193
parser.IgnoreLang.size.bytes,1755271625459000000,1755271685458000000,313
DiskQueryRelay.tasksRequested,1755271625459000000,1755271685458000000,111
lexer.XML.size.bytes,1755271625459000000,1755271685458000000,5
lexer.XML.time.ns,1755271625459000000,1755271685458000000,84988
parser.JSON.size.bytes,1755271625459000000,1755271685458000000,779339
writeAction.wait.ms,1755271625459000000,1755271685458000000,0
lexer.JSON.time.ns,1755271625459000000,1755271685458000000,20164145
parser.XML.size.bytes,1755271625459000000,1755271685458000000,5
parser.GenericSQL.time.ns,1755271625459000000,1755271685458000000,1513566637
lexer.GenericSQL.time.ns,1755271625459000000,1755271685458000000,20496642
parser.yaml.size.bytes,1755271625459000000,1755271685458000000,11680
parser.RELAX-NG.size.bytes,1755271625459000000,1755271685458000000,117
parser.TypeScript.time.ns,1755271625459000000,1755271685458000000,904736185
parser.CSS.time.ns,1755271625459000000,1755271685458000000,43117269
lexer.textmate.size.bytes,1755271625459000000,1755271685458000000,25713
lexer.ECMAScript.6.size.bytes,1755271625459000000,1755271685458000000,1272538
writeAction.median.wait.ms,1755271625459000000,1755271685458000000,0
lexer.ECMAScript.6.time.ns,1755271625459000000,1755271685458000000,101846835
lexer.JSRegexp.time.ns,1755271625459000000,1755271685458000000,11818886
parser.textmate.time.ns,1755271625459000000,1755271685458000000,299174
lexer.JSRegexp.size.bytes,1755271625459000000,1755271685458000000,778
parser.TypeScript.JSX.time.ns,1755271625459000000,1755271685458000000,43624658
lexer.yaml.size.bytes,1755271625459000000,1755271685458000000,11680
lexer.textmate.time.ns,1755271625459000000,1755271685458000000,36510
lexer.Shell.Script.time.ns,1755271625459000000,1755271685458000000,919357
lexer.JSON.size.bytes,1755271625459000000,1755271685458000000,779339
FilePageCache.capacityInBytes,1755271685458000000,1755271740217000000,629145600
DirectByteBufferAllocator.reclaimed,1755271685458000000,1755271740217000000,40
FilePageCache.maxRegisteredFiles,1755271685458000000,1755271740217000000,932
FilePageCache.totalPageLoadsUs,1755271685458000000,1755271740217000000,0
StorageLockContext.competingThreads.90P,1755271685458000000,1755271740217000000,0
FilePageCache.maxCacheSizeInBytes,1755271685458000000,1755271740217000000,482344960
FilePageCache.uncachedFileAccess,1755271685458000000,1755271740217000000,38
FileChannelInterruptsRetryer.totalRetriedAttempts,1755271685458000000,1755271740217000000,0
DirectByteBufferAllocator.totalSizeOfBuffersCachedInBytes,1755271685458000000,1755271740217000000,41943040
DirectByteBufferAllocator.misses,1755271685458000000,1755271740217000000,0
FilePageCache.pageLoadsAboveSizeThreshold,1755271685458000000,1755271740217000000,0
DirectByteBufferAllocator.totalSizeOfBuffersAllocatedInBytes,1755271685458000000,1755271740217000000,41943040
FilePageCache.pageHits,1755271685458000000,1755271740217000000,0
StorageLockContext.competingThreads.avg,1755271685458000000,1755271740217000000,0.0
DirectByteBufferAllocator.hits,1755271685458000000,1755271740217000000,0
FilePageCache.totalPageDisposalsUs,1755271685458000000,1755271740217000000,0
DirectByteBufferAllocator.disposed,1755271685458000000,1755271740217000000,420
FilePageCache.disposedBuffers,1755271685458000000,1755271740217000000,460
FilePageCache.pageLoads,1755271685458000000,1755271740217000000,0
FilePageCache.pageFastCacheHits,1755271685458000000,1755271740217000000,52404
StorageLockContext.competingThreads.max,1755271685458000000,1755271740217000000,0
workspaceModel.moduleManagerBridge.get.modules.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.to.snapshot.ms,1755271685458000000,1755271740217000000,0
workspaceModel.orphan.listener.update.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.put.entity.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleBridge.before.changed.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleManagerBridge.set.unloadedModules.ms,1755271685458000000,1755271740217000000,0
jps.artifact.entities.serializer.save.entities.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleManagerBridge.new.nonPersistent.module.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleManagerBridge.create.module.instance.ms,1755271685458000000,1755271740217000000,0
jps.global.initialize.library.bridges.after.loading.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.resolve.ms,1755271685458000000,1755271740217000000,0
workspaceModel.save.cache.to.file.ms,1755271685458000000,1755271740217000000,0
jps.facet.change.listener.process.change.events.ms,1755271685458000000,1755271740217000000,0
jps.artifact.entities.serializer.load.entities.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleBridgeLoader.loading.modules.ms,1755271685458000000,1755271740217000000,0
jps.reload.project.entities.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedChild.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewParent.ms,1755271685458000000,1755271740217000000,0
workspaceModel.load.cache.metadata.from.file.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.mutable.vfurl.index.ms,1755271685458000000,1755271740217000000,0
workspaceModel.delayed.project.synchronizer.sync.ms,1755271685458000000,1755271740217000000,0
jps.global.get.library.by.name.ms,1755271685458000000,1755271740217000000,0
workspaceModel.global.apply.state.to.project.builder.ms,1755271685458000000,1755271740217000000,0
jps.library.entities.serializer.save.entities.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForRemovedParent.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.replace.by.source.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.entities.ms,1755271685458000000,1755271740217000000,0
jps.global.get.library.ms,1755271685458000000,1755271740217000000,0
jps.facet.change.listener.before.change.events.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleManagerBridge.build.module.graph.ms,1755271685458000000,1755271740217000000,0
jps.app.storage.content.writer.save.component.ms,1755271685458000000,1755271740217000000,0
jps.app.storage.content.reader.load.component.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.remove.entity.ms,1755271685458000000,1755271740217000000,0
jps.module.iml.entities.serializer.save.entities.ms,1755271685458000000,1755271740217000000,0
jps.global.get.libraries.ms,1755271685458000000,1755271740217000000,0
jps.module.iml.entities.serializer.load.entities.ms,1755271685458000000,1755271740217000000,0
jps.project.serializers.save.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleManagerBridge.load.all.modules.ms,1755271685458000000,1755271740217000000,0
jps.apply.loaded.storage.ms,1755271685458000000,1755271740217000000,0
jps.global.handle.before.change.events.ms,1755271685458000000,1755271740217000000,0
workspaceModel.sync.entities.ms,1755271685458000000,1755271740217000000,0
jps.save.global.entities.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleBridge.facet.initialization.ms,1755271685458000000,1755271740217000000,0
jps.library.entities.serializer.load.entities.ms,1755271685458000000,1755271740217000000,0
jps.storage.jps.conf.reader.load.component.ms,1755271685458000000,1755271740217000000,0
jps.load.initial.state.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.apply.changes.from.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.add.entity.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.mutable.ext.mapping.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleManagerBridge.newModule.ms,1755271685458000000,1755271740217000000,0
jps.load.project.to.empty.storage.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.collect.changes.ms,1755271685458000000,1755271740217000000,0
workspaceModel.do.save.caches.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.referrers.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleManagerBridge.load.module.ms,1755271685458000000,1755271740217000000,0
workspaceModel.moduleBridge.update.option.ms,1755271685458000000,1755271740217000000,0
jps.facet.change.listener.init.bridge.ms,1755271685458000000,1755271740217000000,0
workspaceModel.global.updates.count,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.instances.count,1755271685458000000,1755271740217000000,0
jps.project.serializers.load.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.has.same.entities.ms,1755271685458000000,1755271740217000000,0
jps.global.initialize.library.bridges.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.entities.by.source.ms,1755271685458000000,1755271740217000000,0
workspaceModel.load.cache.from.file.ms,1755271685458000000,1755271740217000000,0
jps.global.handle.changed.events.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.modify.entity.ms,1755271685458000000,1755271740217000000,0
workspaceModel.global.updates.ms,1755271685458000000,1755271740217000000,0
workspaceModel.global.apply.state.to.project.ms,1755271685458000000,1755271740217000000,0
workspaceModel.mutableEntityStorage.changeLog.addReplaceEventForNewChild.ms,1755271685458000000,1755271740217000000,0
jps.module.dependency.index.workspace.model.listener.on.changed.ms,1755271685458000000,1755271740217000000,0
jps.save.changed.project.entities.ms,1755271685458000000,1755271740217000000,0
JVM.totalCpuTimeMs,1755271685458000000,1755271740217000000,102980
JVM.totalSafepointCount,1755271685458000000,1755271740217000000,79
JVM.threadCount,1755271685458000000,1755271740217000000,64
JVM.maxThreadCount,1755271685458000000,1755271740217000000,116
JVM.GC.collections,1755271685458000000,1755271740217000000,25
JVM.totalTimeToSafepointsMs,1755271685458000000,1755271740217000000,28
JVM.totalBytesAllocated,1755271685458000000,1755271740217000000,16881466640
JVM.totalTimeAtSafepointsMs,1755271685458000000,1755271740217000000,1929
MEM.ramMinusFileMappingsBytes,1755271685458000000,1755271740217000000,3563184128
JVM.newThreadsCount,1755271685458000000,1755271740217000000,30
JVM.totalDirectByteBuffersBytes,1755271685458000000,1755271740217000000,50886245
JVM.usedNativeBytes,1755271685458000000,1755271740217000000,450392320
MEM.ramBytes,1755271685458000000,1755271740217000000,4002344960
MEM.ramPlusSwapMinusFileMappingsBytes,1755271685458000000,1755271740217000000,3563184128
JVM.committedHeapBytes,1755271685458000000,1755271740217000000,2340421632
JVM.usedHeapBytes,1755271685458000000,1755271740217000000,998244344
JVM.maxHeapBytes,1755271685458000000,1755271740217000000,5834276864
OS.loadAverage,1755271685458000000,1755271740217000000,4.0205078125
MEM.fileMappingsRamBytes,1755271685458000000,1755271740217000000,439160832
JVM.GC.collectionTimesMs,1755271685458000000,1755271740217000000,1774
workspaceModel.initializing.ms,1755271685458000000,1755271740217000000,0
workspaceModel.loading.from.cache.ms,1755271685458000000,1755271740217000000,0
workspaceModel.workspaceFileIndexData.instances.count,1755271685458000000,1755271740217000000,0
workspaceModel.cachedValueWithParameters.from.cache.count,1755271685458000000,1755271740217000000,0
workspaceModel.cachedValueWithParameters.total.get.count,1755271685458000000,1755271740217000000,0
workspaceModel.workspaceFileIndexContributor.registerFileSets.ms,1755271685458000000,1755271740217000000,0
workspaceModel.collect.changes.ms,1755271685458000000,1755271740217000000,0
workspaceModel.on.changed.ms,1755271685458000000,1755271740217000000,0
workspaceModel.pre.handlers.ms,1755271685458000000,1755271740217000000,0
workspaceModel.replace.project.model.ms,1755271685458000000,1755271740217000000,0
workspaceModel.workspaceFileIndexData.visitFileSets.ms,1755271685458000000,1755271740217000000,1
workspaceModel.workspaceFileIndexData.init.ms,1755271685458000000,1755271740217000000,0
workspaceModel.cachedValueWithParameters.clear.count,1755271685458000000,1755271740217000000,0
workspaceModel.cachedValue.clear.count,1755271685458000000,1755271740217000000,0
workspaceModel.cachedValue.calculated.count,1755271685458000000,1755271740217000000,0
workspaceModel.updates.ms,1755271685458000000,1755271740217000000,0
workspaceModel.cachedValueWithParameters.calculated.count,1755271685458000000,1755271740217000000,0
workspaceModel.updates.precise.ms,1755271685458000000,1755271740217000000,0
workspaceModel.workspaceFileIndexData.getPackageName.ms,1755271685458000000,1755271740217000000,0
workspaceModel.entityStorageSnapshotImpl.instances.count,1755271685458000000,1755271740217000000,0
workspaceModel.update.unloaded.entities.ms,1755271685458000000,1755271740217000000,0
workspaceModel.to.snapshot.ms,1755271685458000000,1755271740217000000,0
workspaceModel.cachedValue.total.get.count,1755271685458000000,1755271740217000000,245
workspaceModel.workspaceFileIndexData.markDirty.ms,1755271685458000000,1755271740217000000,0
workspaceModel.loading.total.ms,1755271685458000000,1755271740217000000,0
workspaceModel.workspaceFileIndexData.getFileInfo.ms,1755271685458000000,1755271740217000000,584
workspaceModel.workspaceFileIndexData.updateDirtyEntities.ms,1755271685458000000,1755271740217000000,0
workspaceModel.updates.count,1755271685458000000,1755271740217000000,0
workspaceModel.workspaceFileIndexData.onEntitiesChanged.ms,1755271685458000000,1755271740217000000,0
workspaceModel.check.recursive.update.ms,1755271685458000000,1755271740217000000,0
workspaceModel.cachedValue.from.cache.count,1755271685458000000,1755271740217000000,245
workspaceModel.on.before.changed.ms,1755271685458000000,1755271740217000000,0
workspaceModel.workspaceFileIndexData.getDirectoriesByPackageName.ms,1755271685458000000,1755271740217000000,0
workspaceModel.full.replace.project.model.ms,1755271685458000000,1755271740217000000,0
workspaceModel.init.bridges.ms,1755271685458000000,1755271740217000000,0
workspaceModel.workspaceFileIndexData.processFileSets.ms,1755271685458000000,1755271740217000000,0
AWTEventQueue.dispatchTimeTotalNS,1755271685458000000,1755271740217000000,186026598
NonBlockingReadAction.finalizedExecutionTimeUs,1755271685458000000,1755271740217000000,291970
NonBlockingReadAction.failedExecutionTimeUs,1755271685458000000,1755271740217000000,0
NonBlockingReadAction.failedExecutionsCount,1755271685458000000,1755271740217000000,0
NonBlockingReadAction.finalizedExecutionsCount,1755271685458000000,1755271740217000000,1921
Indexes.entries.lookups,1755271685458000000,1755271740217000000,20550
Indexing.writer_2.totalTimeSpentWritingMs,1755271685458000000,1755271740217000000,0
Indexing.writer_0.totalTimeSpentWritingMs,1755271685458000000,1755271740217000000,0
Indexes.allKeys.lookupDurationMaxMs,1755271685458000000,1755271740217000000,1.0
Indexing.writer_3.totalTimeSpentWritingMs,1755271685458000000,1755271740217000000,0
Indexing.cache.totalCacheEvicted,1755271685458000000,1755271740217000000,70431
Indexes.entries.lookupDuration90PMs,1755271685458000000,1755271740217000000,0.0
Indexing.writesQueuedAvg,1755271685458000000,1755271740217000000,0.0
Indexing.cache.totalCacheAccesses,1755271685458000000,1755271740217000000,581686
Indexes.entries.lookupDurationMaxMs,1755271685458000000,1755271740217000000,7.0
Indexes.stubs.lookups,1755271685458000000,1755271740217000000,146894
Indexes.stubs.lookupDuration90PMs,1755271685458000000,1755271740217000000,0.0
Indexes.allKeys.lookupDuration90PMs,1755271685458000000,1755271740217000000,1.0
Indexes.allKeys.lookups,1755271685458000000,1755271740217000000,3
Indexes.stubs.lookupDurationAvgMs,1755271685458000000,1755271740217000000,0.04364341932662877
Indexes.entries.lookupDurationAvgMs,1755271685458000000,1755271740217000000,0.01812688821752266
Indexing.cache.totalCacheMisses,1755271685458000000,1755271740217000000,63721
Indexing.totalTimeIndexersSleptMs,1755271685458000000,1755271740217000000,0
Indexing.writesQueuedMin,1755271685458000000,1755271740217000000,0
Indexing.writer_1.totalTimeSpentWritingMs,1755271685458000000,1755271740217000000,0
Indexes.allKeys.lookupDurationAvgMs,1755271685458000000,1755271740217000000,0.6666666666666666
Indexing.writesQueuedMax,1755271685458000000,1755271740217000000,0
Indexes.stubs.lookupDurationMaxMs,1755271685458000000,1755271740217000000,89.0
cacheStateStorage.set.counter,1755271685458000000,1755271740217000000,5
cacheStateStorage.get.counter,1755271685458000000,1755271740217000000,0
cacheStateStorage.set.duration,1755271685458000000,1755271740217000000,0
cacheStateStorage.get.duration,1755271685458000000,1755271740217000000,0
VFS.fileChildByName,1755271685458000000,1755271740217000000,1098563
VFS.invertedFileNameIndex.requests,1755271685458000000,1755271740217000000,0
VFS.fileByIdCache.misses,1755271685458000000,1755271740217000000,153942
VFS.fileByIdCache.hits,1755271685458000000,1755271740217000000,0
lexer.yaml.time.ns,1755271685458000000,1755271740217000000,333259
lexer.TypeScript.JSX.time.ns,1755271685458000000,1755271740217000000,9796418
parser.JSRegexp.size.bytes,1755271685458000000,1755271740217000000,388
parser.JSRegexp.time.ns,1755271685458000000,1755271740217000000,7010278
parser.Shell.Script.size.bytes,1755271685458000000,1755271740217000000,29763
parser.SVG.time.ns,1755271685458000000,1755271740217000000,953352
lexer.Shell.Script.size.bytes,1755271685458000000,1755271740217000000,29763
DiskQueryRelay.taskExecutionTotalTimeUs,1755271685458000000,1755271740217000000,3564
parser.TypeScript.size.bytes,1755271685458000000,1755271740217000000,675934
lexer.TypeScript.JSX.size.bytes,1755271685458000000,1755271740217000000,102712
parser.GenericSQL.size.bytes,1755271685458000000,1755271740217000000,3301
parser.SVG.size.bytes,1755271685458000000,1755271740217000000,6461
lexer.CSS.time.ns,1755271685458000000,1755271740217000000,2860709
lexer.IgnoreLang.size.bytes,1755271685458000000,1755271740217000000,1511
lexer.TypeScript.size.bytes,1755271685458000000,1755271740217000000,503970
lexer.HTML.size.bytes,1755271685458000000,1755271740217000000,25156
DiskQueryRelay.tasksExecuted,1755271685458000000,1755271740217000000,37
parser.textmate.size.bytes,1755271685458000000,1755271740217000000,67590
lexer.HTML.time.ns,1755271685458000000,1755271740217000000,4871594
parser.TypeScript.JSX.size.bytes,1755271685458000000,1755271740217000000,161897
DiskQueryRelay.taskWaitingTotalTimeUs,1755271685458000000,1755271740217000000,56258
lexer.GenericSQL.size.bytes,1755271685458000000,1755271740217000000,3296
parser.Shell.Script.time.ns,1755271685458000000,1755271740217000000,236953171
lexer.SVG.size.bytes,1755271685458000000,1755271740217000000,6461
parser.IgnoreLang.time.ns,1755271685458000000,1755271740217000000,1907747
lexer.IgnoreLang.time.ns,1755271685458000000,1755271740217000000,548269
parser.ECMAScript.6.size.bytes,1755271685458000000,1755271740217000000,1473693
lexer.TypeScript.time.ns,1755271685458000000,1755271740217000000,31516280
parser.JSON.time.ns,1755271685458000000,1755271740217000000,747947541
parser.ECMAScript.6.time.ns,1755271685458000000,1755271740217000000,211390119
parser.HTML.size.bytes,1755271685458000000,1755271740217000000,25156
writeAction.count,1755271685458000000,1755271740217000000,3
parser.yaml.time.ns,1755271685458000000,1755271740217000000,811108
parser.HTML.time.ns,1755271685458000000,1755271740217000000,4380348
writeAction.max.wait.ms,1755271685458000000,1755271740217000000,32
lexer.CSS.size.bytes,1755271685458000000,1755271740217000000,39760
parser.CSS.size.bytes,1755271685458000000,1755271740217000000,39760
parser.IgnoreLang.size.bytes,1755271685458000000,1755271740217000000,1511
DiskQueryRelay.tasksRequested,1755271685458000000,1755271740217000000,37
parser.JSON.size.bytes,1755271685458000000,1755271740217000000,2668660
writeAction.wait.ms,1755271685458000000,1755271740217000000,2
lexer.JSON.time.ns,1755271685458000000,1755271740217000000,94488795
parser.GenericSQL.time.ns,1755271685458000000,1755271740217000000,51606125
lexer.GenericSQL.time.ns,1755271685458000000,1755271740217000000,10119992
lexer.SVG.time.ns,1755271685458000000,1755271740217000000,6495865
parser.yaml.size.bytes,1755271685458000000,1755271740217000000,2011
parser.TypeScript.time.ns,1755271685458000000,1755271740217000000,219321276
parser.CSS.time.ns,1755271685458000000,1755271740217000000,15818723
lexer.textmate.size.bytes,1755271685458000000,1755271740217000000,67590
lexer.ECMAScript.6.size.bytes,1755271685458000000,1755271740217000000,1131671
writeAction.median.wait.ms,1755271685458000000,1755271740217000000,0
lexer.ECMAScript.6.time.ns,1755271685458000000,1755271740217000000,97274047
lexer.JSRegexp.time.ns,1755271685458000000,1755271740217000000,707826
parser.textmate.time.ns,1755271685458000000,1755271740217000000,466457
lexer.JSRegexp.size.bytes,1755271685458000000,1755271740217000000,388
parser.TypeScript.JSX.time.ns,1755271685458000000,1755271740217000000,32883020
lexer.yaml.size.bytes,1755271685458000000,1755271740217000000,2011
lexer.textmate.time.ns,1755271685458000000,1755271740217000000,100337
lexer.Shell.Script.time.ns,1755271685458000000,1755271740217000000,9065413
lexer.JSON.size.bytes,1755271685458000000,1755271740217000000,2668660
