{"local": {"filename": "qodana.yaml", "content": "# Qodana Configuration for HeartSync v2\n# Simplified configuration to avoid unsupported keys\n\nversion: '1.0'\nlinter: 'jetbrains/qodana-js:2025.1'\n\n# Analysis profile\nprofile:\n  name: qodana.recommended\n\n# Exclude files and directories\nexclude:\n  # Directory exclusions\n  - name: \"backups\"\n    paths:\n      - \"backups/**\"\n  \n  - name: \"playwright-report\" \n    paths:\n      - \"playwright-report/**\"\n      \n  - name: \"scripts\"\n    paths:\n      - \"scripts/**\"\n      \n  - name: \"static\"\n    paths:\n      - \"static/**\"\n      \n  - name: \"streamline-landing-page\"\n    paths:\n      - \"streamline-landing-page/**\"\n      \n  - name: \"node_modules\"\n    paths:\n      - \"node_modules/**\"\n      \n  - name: \"build-output\"\n    paths:\n      - \"dist/**\"\n      - \"build/**\"\n      - \".wrangler/**\"\n      \n  # Individual file exclusions\n  - name: \"test-files\"\n    paths:\n      - \"finlight_test_page.html\"\n      - \"multi_api_test_page.html\"\n      - \"hsn-terminal-chat.js\"\n      - \"verify-fix.js\"\n      - \"playwright-debug-cli-test-b.js\"\n      \n  # Config and temporary files\n  - name: \"config-and-temp\"\n    paths:\n      - \"*.log\"\n      - \"*.tmp\"\n      - \"*.temp\"\n      - \".env*\"\n      - \".DS_Store\"\n      - \"Thumbs.db\"\n      - \"data/**\"\n\n# Include files and directories for analysis\ninclude:\n  - name: \"source-code\"\n    paths:\n      - \"src/**/*.js\"\n      - \"src/**/*.ts\"\n      - \"server.js\"\n      \n  - name: CheckDependencyLicenses\n\n# Bootstrap script for dependency installation\nbootstrap: |\n  npm ci --only=production || npm install --only=production || true\n"}}