{"modules": [{"name": "project", "contentEntries": [{"path": "file://$PROJECT_DIR$", "excludePatterns": [], "excludeFolders": [{"path": "file://$PROJECT_DIR$/.tmp", "type": "Exclude", "packagePrefix": ""}, {"path": "file://$PROJECT_DIR$/temp", "type": "Exclude", "packagePrefix": ""}, {"path": "file://$PROJECT_DIR$/tmp", "type": "Exclude", "packagePrefix": ""}], "sourceFolders": []}], "orderEntries": [{"type": "SDK"}, {"name": "<Module source>", "type": "Own sources"}]}]}