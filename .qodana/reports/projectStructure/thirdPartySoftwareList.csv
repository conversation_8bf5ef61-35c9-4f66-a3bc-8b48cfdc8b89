"Dependency","Version","License"
"@ampproject/remapping","2.3.0","Apache-2.0"
"@anthropic-ai/sdk","0.57.0","MIT"
"@emnapi/runtime","1.4.5","MIT"
"@hono/node-server","1.17.1","MIT"
"@isaacs/fs-minipass","4.0.1","ISC"
"@jridgewell/gen-mapping","0.3.12","MIT"
"@jridgewell/sourcemap-codec","1.5.4","MIT"
"@jridgewell/trace-mapping","0.3.29","MIT"
"@libsql/client","0.15.10","MIT"
"@libsql/core","0.15.10","MIT"
"@libsql/hrana-client","0.7.0","MIT"
"@libsql/isomorphic-fetch","0.3.1","MIT"
"@libsql/isomorphic-ws","0.1.5","MIT"
"@libsql/linux-x64-gnu","0.5.17","MIT"
"@libsql/linux-x64-musl","0.5.17","MIT"
"@neon-rs/load","0.0.4","MIT"
"@noble/ciphers","1.3.0","MIT"
"@noble/curves","1.9.4","MIT"
"@noble/hashes","1.8.0","MIT"
"@opentelemetry/api","1.9.0","Apache-2.0"
"@parcel/watcher-linux-x64-glibc","2.5.1","MIT"
"@parcel/watcher-linux-x64-musl","2.5.1","MIT"
"@parcel/watcher","2.5.1","MIT"
"@scure/base","1.2.1","MIT"
"@scure/base","1.2.6","MIT"
"@scure/bip39","1.6.0","MIT"
"@starfederation/datastar","1.0.0-beta.11","MIT"
"@tailwindcss/cli","4.1.11","MIT"
"@tailwindcss/node","4.1.11","MIT"
"@tailwindcss/oxide-linux-x64-gnu","4.1.11","MIT"
"@tailwindcss/oxide-linux-x64-musl","4.1.11","MIT"
"@tailwindcss/oxide","4.1.11","MIT"
"@types/node","24.1.0","MIT"
"@types/ws","8.18.1","MIT"
"base64-js","1.5.1","MIT"
"better-sqlite3","12.2.0","MIT"
"bindings","1.5.0","MIT"
"bl","4.1.0","MIT"
"braces","3.0.3","MIT"
"buffer","5.7.1","MIT"
"chownr","1.1.4","ISC"
"chownr","3.0.0","BlueOak-1.0.0"
"cojson","0.14.28","MIT"
"data-uri-to-buffer","4.0.1","MIT"
"decompress-response","6.0.0","MIT"
"deep-extend","0.6.0","MIT"
"detect-libc","1.0.3","Apache-2.0"
"detect-libc","2.0.2","Apache-2.0"
"detect-libc","2.0.4","Apache-2.0"
"dotenv","17.2.1","BSD-2-Clause"
"end-of-stream","1.4.5","MIT"
"enhanced-resolve","5.18.2","MIT"
"expand-template","2.0.3","MIT"
"fast-list","1.0.3","ISC"
"fast-myers-diff","3.2.0","MIT"
"fetch-blob","3.2.0","MIT"
"file-uri-to-path","1.0.0","MIT"
"fill-range","7.1.1","MIT"
"formdata-polyfill","4.0.10","MIT"
"fs-constants","1.0.0","MIT"
"github-from-package","0.0.0","MIT"
"hono","4.8.8","MIT"
"ieee754","1.2.1","BSD-3-Clause"
"inherits","2.0.4","ISC"
"ini","1.3.8","ISC"
"is-extglob","2.1.1","MIT"
"is-glob","4.0.3","MIT"
"is-number","7.0.0","MIT"
"jazz-crypto-rs","0.0.7","MIT"
"jazz-tools","0.14.28","MIT"
"jiti","2.5.0","MIT"
"js-base64","3.7.7","BSD-3-Clause"
"libsql","0.5.17","MIT"
"lightningcss-linux-x64-gnu","1.30.1","MPL-2.0"
"lightningcss-linux-x64-musl","1.30.1","MPL-2.0"
"lightningcss","1.30.1","MPL-2.0"
"lucide-react","0.525.0","ISC"
"magic-string","0.30.17","MIT"
"micromatch","4.0.8","MIT"
"mimic-response","3.1.0","MIT"
"minimist","1.2.8","MIT"
"minipass","7.1.2","ISC"
"minizlib","3.0.2","MIT"
"mkdirp-classic","0.5.3","MIT"
"mkdirp","3.0.1","MIT"
"mri","1.2.0","MIT"
"mutative","1.2.0","MIT"
"napi-build-utils","2.0.0","MIT"
"neverthrow","7.2.0","MIT"
"node-abi","3.75.0","MIT"
"node-addon-api","7.1.1","MIT"
"node-fetch","3.3.2","MIT"
"once","1.4.0","ISC"
"picocolors","1.1.1","ISC"
"picomatch","2.3.1","MIT"
"prebuild-install","7.1.3","MIT"
"promise-limit","2.7.0","ISC"
"pump","3.0.3","MIT"
"queueueue","4.1.2","MIT"
"rc","1.2.8","BSD-2-Clause"
"react","19.1.0","MIT"
"readable-stream","3.6.2","MIT"
"safe-buffer","5.2.1","MIT"
"semver","7.7.2","ISC"
"simple-concat","1.0.1","MIT"
"simple-get","4.0.1","MIT"
"source-map-js","1.2.1","BSD-3-Clause"
"strip-json-comments","2.0.1","MIT"
"tailwindcss","4.1.11","MIT"
"tar-fs","2.1.3","MIT"
"tar-stream","2.2.0","MIT"
"tar","7.4.3","ISC"
"to-regex-range","5.0.1","MIT"
"tslib","2.8.1","0BSD"
"tunnel-agent","0.6.0","Apache-2.0"
"undici-types","7.8.0","MIT"
"unicode-segmenter","0.12.0","MIT"
"uuid","11.1.0","MIT"
"ws","8.18.3","MIT"
"yallist","5.0.0","BlueOak-1.0.0"
"zod","3.25.28","MIT"