[{"key": "AFL-2.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "AFL-2.1", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "AFL-3.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "AGPL-3.0-only", "allowed": ["AGPL-3.0-only", "Apache-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "GPL-3.0-only", "GPL-3.0-or-later", "IBM-pibs", "ICU", "ISC", "LGPL-3.0-only", "LGPL-3.0-or-later", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-or-later", "Apache-1.0", "Apache-1.1", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "FTL", "GPL-2.0-only", "GPL-2.0-or-later", "IJG", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "AGPL-3.0-or-later", "allowed": ["AGPL-3.0-only", "AGPL-3.0-or-later", "Apache-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "GPL-3.0-only", "GPL-3.0-or-later", "IBM-pibs", "ICU", "ISC", "LGPL-3.0-only", "LGPL-3.0-or-later", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["Apache-1.0", "Apache-1.1", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "FTL", "GPL-2.0-only", "GPL-2.0-or-later", "IJG", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "Apache-1.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "Apache-1.1", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "Apache-2.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0", "MPL-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "Artistic-1.0-Perl", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "Artistic-2.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "BSD-2-<PERSON><PERSON>", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "BSD-2-<PERSON>e-Patent", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "BSD-3-<PERSON><PERSON>", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "BSD-4-<PERSON><PERSON>", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "BSD-4-<PERSON>e-UC", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "BSL-1.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "bzip2-1.0.6", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "CC0-1.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "CDDL-1.0", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "CDDL-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "CPL-1.0", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "CPL-1.0", "curl", "EFL-2.0", "EPL-1.0", "IBM-pibs", "ICU", "IPL-1.0", "ISC", "Libpng", "MIT", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "curl", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "EFL-2.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "EPL-1.0", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "CPL-1.0", "curl", "EFL-2.0", "EPL-1.0", "EUPL-1.1", "IBM-pibs", "ICU", "IPL-1.0", "ISC", "Libpng", "MIT", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "EPL-2.0", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "EPL-2.0", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "EPL-1.0", "EPL-2.0", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "EUPL-1.1", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "EUPL-1.1", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "Apache-1.0", "Apache-1.1", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "FTL", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IJG", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "FTL", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "GPL-2.0-only", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "IBM-pibs", "ICU", "ISC", "LGPL-2.1-only", "LGPL-2.1-or-later", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "Apache-1.0", "Apache-1.1", "Apache-2.0", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "FTL", "GPL-3.0-only", "GPL-3.0-or-later", "IJG", "IPL-1.0", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "GPL-2.0-or-later", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-or-later", "IBM-pibs", "ICU", "ISC", "LGPL-2.1-only", "LGPL-2.1-or-later", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "Apache-1.0", "Apache-1.1", "Apache-2.0", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "FTL", "GPL-3.0-only", "IJG", "IPL-1.0", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "GPL-3.0-only", "allowed": ["AGPL-3.0-only", "Apache-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IBM-pibs", "ICU", "ISC", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "Libpng", "MIT", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-or-later", "Apache-1.0", "Apache-1.1", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EUPL-1.1", "FTL", "GPL-2.0-only", "IJG", "IPL-1.0", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "GPL-3.0-or-later", "allowed": ["AGPL-3.0-or-later", "Apache-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "GPL-2.0-or-later", "GPL-3.0-or-later", "IBM-pibs", "ICU", "ISC", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-or-later", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "Apache-1.0", "Apache-1.1", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EUPL-1.1", "FTL", "GPL-2.0-only", "GPL-3.0-only", "IJG", "IPL-1.0", "LGPL-3.0-only", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "HPND", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "IBM-pibs", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "ICU", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "IJG", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "Info-ZIP", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "IPL-1.0", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "CPL-1.0", "curl", "EFL-2.0", "EPL-1.0", "IBM-pibs", "ICU", "IPL-1.0", "ISC", "Libpng", "MIT", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "ISC", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "LGPL-2.1-only", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "GPL-2.0-only", "GPL-2.0-or-later", "IBM-pibs", "ICU", "ISC", "LGPL-2.1-only", "LGPL-2.1-or-later", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "Apache-1.0", "Apache-1.1", "Apache-2.0", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EUPL-1.1", "FTL", "GPL-3.0-only", "GPL-3.0-or-later", "IJG", "IPL-1.0", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "LGPL-2.1-or-later", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "GPL-2.0-or-later", "IBM-pibs", "ICU", "ISC", "LGPL-2.1-or-later", "LGPL-3.0-or-later", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "Apache-1.0", "Apache-1.1", "Apache-2.0", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EUPL-1.1", "FTL", "GPL-2.0-only", "GPL-3.0-only", "GPL-3.0-or-later", "IJG", "IPL-1.0", "LGPL-2.1-only", "LGPL-3.0-only", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "LGPL-3.0-only", "allowed": ["Apache-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "LGPL-3.0-only", "LGPL-3.0-or-later", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "Apache-1.0", "Apache-1.1", "Artistic-2.0", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EUPL-1.1", "FTL", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IJG", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "LGPL-3.0-or-later", "allowed": ["Apache-2.0", "BSD-2-<PERSON><PERSON>", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "LGPL-3.0-or-later", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "Apache-1.0", "Apache-1.1", "Artistic-2.0", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EUPL-1.1", "FTL", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IJG", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OpenSSL", "OSL-3.0", "RPL-1.5", "XFree86-1.1", "zlib-acknowledgement"]}, {"key": "Libpng", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "libtiff", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "MirOS", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "MIT", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "MIT-CMU", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "MPL-1.1", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "MPL-1.1", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "MPL-2.0", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "MPL-2.0", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "MPL-2.0-no-copyleft-exception", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "MS-PL", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "MS-PL", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "MS-RL", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "MS-RL", "NTP", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "OSL-3.0", "RPL-1.5"]}, {"key": "NBPL-1.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "NTP", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "OpenSSL", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "NTP", "OpenSSL", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "OSL-3.0", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "EUPL-1.1", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "NTP", "OSL-3.0", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "RPL-1.5"]}, {"key": "Python-2.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "<PERSON><PERSON><PERSON>", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "RPL-1.5", "allowed": ["BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "IBM-pibs", "ICU", "ISC", "Libpng", "MIT", "NTP", "RPL-1.5", "UPL-1.0", "WTFPL", "X11", "<PERSON><PERSON><PERSON>", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0"]}, {"key": "SunPro", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "Unicode-DFS-2015", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "Unicode-DFS-2016", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "UPL-1.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "WTFPL", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "X11", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "XFree86-1.1", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "<PERSON><PERSON><PERSON>", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "zlib-acknowledgement", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "ZPL-2.0", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CDDL-1.0", "CPL-1.0", "EPL-1.0", "EPL-2.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MPL-1.1", "MPL-2.0", "MPL-2.0-no-copyleft-exception", "MS-RL", "OSL-3.0", "RPL-1.5"]}, {"key": "PROPRIETARY-LICENSE", "allowed": ["AFL-2.0", "AFL-2.1", "AFL-3.0", "Apache-1.0", "Apache-1.1", "Apache-2.0", "Artistic-1.0-Perl", "Artistic-2.0", "BSD-2-<PERSON><PERSON>", "BSD-2-<PERSON>e-Patent", "BSD-3-<PERSON><PERSON>", "BSD-4-<PERSON><PERSON>", "BSD-4-<PERSON>e-UC", "BSL-1.0", "bzip2-1.0.6", "CC0-1.0", "curl", "EFL-2.0", "FTL", "HPND", "IBM-pibs", "ICU", "IJG", "Info-ZIP", "ISC", "Libpng", "libtiff", "MirOS", "MIT", "MIT-CMU", "NBPL-1.0", "NTP", "Python-2.0", "<PERSON><PERSON><PERSON>", "SunPro", "Unicode-DFS-2015", "Unicode-DFS-2016", "UPL-1.0", "WTFPL", "X11", "XFree86-1.1", "<PERSON><PERSON><PERSON>", "zlib-acknowledgement", "ZPL-2.0", "ORACLE-OPENJDK-EXCEPTION-2.0", "BSD-3-<PERSON><PERSON>-<PERSON>", "AML", "0BSD", "BSD-3-CLAUSE-NO-CHANGE", "BSD-UNCHANGED", "CC-BY-2.5", "CC-BY-4.0", "MIT-open-group", "OpenSSL", "PDDL-1.0", "PROTOBUF", "PUBLIC-DOMAIN-DISCLAIMER", "Unlicense", "UNICODE-ICU-58", "UNICODE-MAPPINGS", "W3C", "W3C-19980720", "XFREE86-1.0", "JDOM", "BSD-2-<PERSON><PERSON>-Views", "FPL", "Classpath-exception-2.0"], "prohibited": ["AGPL-3.0-only", "AGPL-3.0-or-later", "CPL-1.0", "EPL-1.0", "EUPL-1.1", "GPL-2.0-only", "GPL-2.0-or-later", "GPL-3.0-only", "GPL-3.0-or-later", "IPL-1.0", "MS-RL", "OSL-3.0"]}, {"key": "ORACLE-OPENJDK-EXCEPTION-2.0", "allowed": ["ORACLE-OPENJDK-EXCEPTION-2.0", "Classpath-exception-2.0"], "prohibited": []}, {"key": "BSD-3-<PERSON><PERSON>-<PERSON>", "allowed": ["BSD-3-<PERSON><PERSON>-<PERSON>", "Classpath-exception-2.0"], "prohibited": []}, {"key": "AML", "allowed": ["AML", "Classpath-exception-2.0"], "prohibited": []}, {"key": "0BSD", "allowed": ["0BSD", "Classpath-exception-2.0"], "prohibited": []}, {"key": "BSD-3-CLAUSE-NO-CHANGE", "allowed": ["BSD-3-CLAUSE-NO-CHANGE", "Classpath-exception-2.0"], "prohibited": []}, {"key": "BSD-UNCHANGED", "allowed": ["BSD-UNCHANGED", "Classpath-exception-2.0"], "prohibited": []}, {"key": "CC-BY-2.5", "allowed": ["CC-BY-2.5", "Classpath-exception-2.0"], "prohibited": []}, {"key": "CC-BY-4.0", "allowed": ["CC-BY-4.0", "Classpath-exception-2.0"], "prohibited": []}, {"key": "MIT-open-group", "allowed": ["MIT-open-group", "Classpath-exception-2.0"], "prohibited": []}, {"key": "PDDL-1.0", "allowed": ["PDDL-1.0", "Classpath-exception-2.0"], "prohibited": []}, {"key": "PROTOBUF", "allowed": ["PROTOBUF", "Classpath-exception-2.0"], "prohibited": []}, {"key": "PUBLIC-DOMAIN-DISCLAIMER", "allowed": ["PUBLIC-DOMAIN-DISCLAIMER", "Classpath-exception-2.0"], "prohibited": []}, {"key": "Unlicense", "allowed": ["Unlicense", "Classpath-exception-2.0"], "prohibited": []}, {"key": "UNICODE-ICU-58", "allowed": ["UNICODE-ICU-58", "Classpath-exception-2.0"], "prohibited": []}, {"key": "UNICODE-MAPPINGS", "allowed": ["UNICODE-MAPPINGS", "Classpath-exception-2.0"], "prohibited": []}, {"key": "W3C", "allowed": ["W3C", "Classpath-exception-2.0"], "prohibited": []}, {"key": "W3C-19980720", "allowed": ["W3C-19980720", "Classpath-exception-2.0"], "prohibited": []}, {"key": "XFREE86-1.0", "allowed": ["XFREE86-1.0", "Classpath-exception-2.0"], "prohibited": []}, {"key": "JDOM", "allowed": ["JDOM", "Classpath-exception-2.0"], "prohibited": []}, {"key": "BSD-2-<PERSON><PERSON>-Views", "allowed": ["BSD-2-<PERSON><PERSON>-Views", "Classpath-exception-2.0"], "prohibited": []}, {"key": "FPL", "allowed": ["FPL", "Classpath-exception-2.0"], "prohibited": []}, {"key": "Classpath-exception-2.0", "allowed": [], "prohibited": []}]