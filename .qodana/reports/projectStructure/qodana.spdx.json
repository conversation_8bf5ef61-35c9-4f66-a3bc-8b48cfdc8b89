{"SPDXID": "SPDXRef-DOCUMENT", "name": "SPDXRef-project", "spdxVersion": "SPDX-2.3", "dataLicense": "CC0-1.0", "documentNamespace": "https://project-0056a630-29d3-1d4b-b242-4ef501868ae5", "creationInfo": {"creators": ["Tool: <PERSON><PERSON><PERSON>", "Organization: JetBrains s.r.o."], "created": "2025-08-15T15:28:57Z"}, "packages": [{"SPDXID": "SPDXRef-project", "name": "project", "downloadLocation": "https://github.com/QutritSystems/heartsync-v2", "filesAnalyzed": false, "licenseConcluded": "LicenseRef-PROPRIETARY-LICENSE", "licenseDeclared": "NOASSERTION", "licenseInfoFromFiles": ["NOASSERTION"], "copyrightText": "NOASSERTION", "supplier": "NOASSERTION", "checksums": [{"algorithm": "SHA1", "checksumValue": "d840c87511740d2340f76dddd85137ed80d95b3f"}], "primaryPackagePurpose": "SOURCE"}, {"SPDXID": "SPDXRef-project-JS", "name": "project:JS", "versionInfo": "1.0.0", "downloadLocation": "https://github.com/QutritSystems/heartsync-v2", "filesAnalyzed": false, "licenseConcluded": "LicenseRef-PROPRIETARY-LICENSE", "licenseDeclared": "NOASSERTION", "licenseInfoFromFiles": ["NOASSERTION"], "copyrightText": "NOASSERTION", "supplier": "NOASSERTION", "checksums": [{"algorithm": "SHA1", "checksumValue": "d840c87511740d2340f76dddd85137ed80d95b3f"}], "primaryPackagePurpose": "SOURCE"}, {"SPDXID": "SPDXRef-@ampproject-remapping-2.3.0", "name": "@ampproject/remapping", "versionInfo": "2.3.0", "downloadLocation": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@ampproject/remapping", "licenseConcluded": "Apache-2.0", "licenseDeclared": "Apache-2.0", "licenseInfoFromFiles": ["Apache-2.0"], "copyrightText": "NOASSERTION", "packageFileName": "remapping", "supplier": "Google LLC", "checksums": [{"algorithm": "SHA512", "checksumValue": "30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@anthropic-ai-sdk-0.57.0", "name": "@anthropic-ai/sdk", "versionInfo": "0.57.0", "downloadLocation": "https://registry.npmjs.org/@anthropic-ai/sdk/-/sdk-0.57.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@anthropic-ai/sdk", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "sdk", "supplier": "Person: Anthrop<PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "z5LMy0MWu0+w2hflUgj4RlJr1R+0BxKXL7ldXTO8FasU8fu599STghO+QKwId2dAD0d464aHtU+ChWuRHw4FNw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@emnapi-runtime-1.4.5", "name": "@emnapi/runtime", "versionInfo": "1.4.5", "downloadLocation": "https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.4.5.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@emnapi/runtime", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "runtime", "supplier": "Person: <PERSON><PERSON><PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "++LApOtY0pEEz1zrd9vy1/zXVaVJJ/EbAF3u0fXIzPJEDtnITsBGbbK0EkM72amhl/R5b+5xx0Y/QhcVOpuulg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@hono-node-server-1.17.1", "name": "@hono/node-server", "versionInfo": "1.17.1", "downloadLocation": "https://registry.npmjs.org/@hono/node-server/-/node-server-1.17.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@hono/node-server", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "node-server", "supplier": "Person: <PERSON><PERSON> <<EMAIL>> (https://github.com/yusukebe)", "checksums": [{"algorithm": "SHA512", "checksumValue": "SY79W/C+2b1MyAzmIcV32Q47vO1b5XwLRwj8S9N6Jr5n1QCkIfAIH6umOSgqWZ4/v67hg6qq8Ha5vZonVidGsg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@instantdb-core-0.20.12", "name": "@instantdb/core", "versionInfo": "0.20.12", "downloadLocation": "https://registry.npmjs.org/@instantdb/core/-/core-0.20.12.tgz", "filesAnalyzed": false, "homepage": "https://www.npmjs.com/package/@instantdb/core", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "licenseInfoFromFiles": ["NOASSERTION"], "copyrightText": "NOASSERTION", "packageFileName": "core", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "Mz0sAqElarKCsJxWRVhzrXEy6P9QrNQ1HQqYVVDuE/hYzj+ms9k2nPH82OFdyCpOzUC4VnuDyFRZCnCZQTMBzQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@instantdb-react-0.20.12", "name": "@instantdb/react", "versionInfo": "0.20.12", "downloadLocation": "https://registry.npmjs.org/@instantdb/react/-/react-0.20.12.tgz", "filesAnalyzed": false, "homepage": "https://www.npmjs.com/package/@instantdb/react", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "licenseInfoFromFiles": ["NOASSERTION"], "copyrightText": "NOASSERTION", "packageFileName": "react", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "gOosibMmNleJbWfVsRx5BUyrxe84MtQneT+OYcTV5QkSAWpJza9uumjsFbQ1wpTTUMZE7YnWfovGTAzRISwk6Q"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@isaacs-fs-minipass-4.0.1", "name": "@isaacs/fs-minipass", "versionInfo": "4.0.1", "downloadLocation": "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@isaacs/fs-minipass", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "fs-minipass", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@jridgewell-gen-mapping-0.3.12", "name": "@jridgewell/gen-mapping", "versionInfo": "0.3.12", "downloadLocation": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@jridgewell/gen-mapping", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "gen-mapping", "supplier": "Person: <PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@jridgewell-sourcemap-codec-1.5.4", "name": "@jridgewell/sourcemap-codec", "versionInfo": "1.5.4", "downloadLocation": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@jridgewell/sourcemap-codec", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "sourcemap-codec", "supplier": "Person: <PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@jridgewell-trace-mapping-0.3.29", "name": "@jridgewell/trace-mapping", "versionInfo": "0.3.29", "downloadLocation": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@jridgewell/trace-mapping", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "trace-mapping", "supplier": "Person: <PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@libsql-client-0.15.10", "name": "@libsql/client", "versionInfo": "0.15.10", "downloadLocation": "https://registry.npmjs.org/@libsql/client/-/client-0.15.10.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@libsql/client", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "client", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "J9cJQwrgH92JlPBYjUGxPIH5G9z3j/V/aPnQvcmmCgjatdVb/f7bzK3yNq15Phc+gVuKMwox3toXL+58qUMylg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@libsql-core-0.15.10", "name": "@libsql/core", "versionInfo": "0.15.10", "downloadLocation": "https://registry.npmjs.org/@libsql/core/-/core-0.15.10.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@libsql/core", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "core", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "fAMD+GnGQNdZ9zxeNC8AiExpKnou/97GJWkiDDZbTRHj3c9dvF1y4jsRQ0WE72m/CqTdbMGyU98yL0SJ9hQVeg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@libsql-hrana-client-0.7.0", "name": "@libsql/hrana-client", "versionInfo": "0.7.0", "downloadLocation": "https://registry.npmjs.org/@libsql/hrana-client/-/hrana-client-0.7.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@libsql/hrana-client", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "hrana-client", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "OF8fFQSkbL7vJY9rfuegK1R7sPgQ6kFMkDamiEccNUvieQ+3urzfDFI616oPl8V7T9zRmnTkSjMOImYCAVRVuw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@libsql-isomorphic-fetch-0.3.1", "name": "@libsql/isomorphic-fetch", "versionInfo": "0.3.1", "downloadLocation": "https://registry.npmjs.org/@libsql/isomorphic-fetch/-/isomorphic-fetch-0.3.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@libsql/isomorphic-fetch", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "isomorphic-fetch", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "6kK3SUK5Uu56zPq/Las620n5aS9xJq+jMBcNSOmjhNf/MUvdyji4vrMTqD7ptY7/4/CAVEAYDeotUz60LNQHtw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@libsql-isomorphic-ws-0.1.5", "name": "@libsql/isomorphic-ws", "versionInfo": "0.1.5", "downloadLocation": "https://registry.npmjs.org/@libsql/isomorphic-ws/-/isomorphic-ws-0.1.5.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@libsql/isomorphic-ws", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "isomorphic-ws", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "DtLWIH29onUYR00i0GlQ3UdcTRC6EP4u9w/h9LxpUZJWRMARk6dQwZ6Jkd+QdwVpuAOrdxt18v0K2uIYR3fwFg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@libsql-linux-x64-gnu-0.5.17", "name": "@libsql/linux-x64-gnu", "versionInfo": "0.5.17", "downloadLocation": "https://registry.npmjs.org/@libsql/linux-x64-gnu/-/linux-x64-gnu-0.5.17.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@libsql/linux-x64-gnu", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "linux-x64-gnu", "supplier": "Person: <PERSON><PERSON><PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "5l3XxWqUPVFrtX0xnZaXwqsXs0BFbP4w6ahRFTPSdXU50YBfUOajFznJRB6bJTMsCvraDSD0IkHhjSNfrE1CuQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@libsql-linux-x64-musl-0.5.17", "name": "@libsql/linux-x64-musl", "versionInfo": "0.5.17", "downloadLocation": "https://registry.npmjs.org/@libsql/linux-x64-musl/-/linux-x64-musl-0.5.17.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@libsql/linux-x64-musl", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "linux-x64-musl", "supplier": "Person: <PERSON><PERSON><PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "FvSpWlwc+dIeYIFYlsSv+UdQ/NiZWr+SstwVji+QZ//8NnvzwWQU9cgP+Vpps6Qiq4jyYQm9chJhTYOVT9Y3BA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@neon-rs-load-0.0.4", "name": "@neon-rs/load", "versionInfo": "0.0.4", "downloadLocation": "https://registry.npmjs.org/@neon-rs/load/-/load-0.0.4.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@neon-rs/load", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "load", "supplier": "Person: <PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "kTPhdZyTQxB+2wpiRcFWrDcejc4JI6tkPuS7UZCG4l6Zvc5kU/gGQ/ozvHTh1XR5tS+UlfAfGuPajjzQjCiHCw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@noble-ciphers-1.3.0", "name": "@noble/ciphers", "versionInfo": "1.3.0", "downloadLocation": "https://registry.npmjs.org/@noble/ciphers/-/ciphers-1.3.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@noble/ciphers", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "ciphers", "supplier": "Person: <PERSON> (https://paulmillr.com)", "checksums": [{"algorithm": "SHA512", "checksumValue": "2I0gnIVPtfnMw9ee9h1dJG7tp81+8Ob3OJb3Mv37rx5L40/b0i7djjCVvGOVqc9AEIQyvyu1i6ypKdFw8R8gQw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@noble-curves-1.9.4", "name": "@noble/curves", "versionInfo": "1.9.4", "downloadLocation": "https://registry.npmjs.org/@noble/curves/-/curves-1.9.4.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@noble/curves", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "curves", "supplier": "Person: <PERSON> (https://paulmillr.com)", "checksums": [{"algorithm": "SHA512", "checksumValue": "2bKONnuM53lINoDrSmK8qP8W271ms7pygDhZt4SiLOoLwBtoHqeCFi6RG42V8zd3mLHuJFhU/Bmaqo4nX0/kBw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@noble-hashes-1.8.0", "name": "@noble/hashes", "versionInfo": "1.8.0", "downloadLocation": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.8.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@noble/hashes", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "hashes", "supplier": "Person: <PERSON> (https://paulmillr.com)", "checksums": [{"algorithm": "SHA512", "checksumValue": "jCs9ldd7NwzpgXDIf6P3+NrHh9/sD6CQdxHyjQI+h/6rDNo88ypBxxz45UDuZHz9r3tNz7N/VInSVoVdtXEI4A"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@opentelemetry-api-1.9.0", "name": "@opentelemetry/api", "versionInfo": "1.9.0", "downloadLocation": "https://registry.npmjs.org/@opentelemetry/api/-/api-1.9.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@opentelemetry/api", "licenseConcluded": "Apache-2.0", "licenseDeclared": "Apache-2.0", "licenseInfoFromFiles": ["Apache-2.0"], "copyrightText": "NOASSERTION", "packageFileName": "api", "supplier": "Person: OpenTelemetry Authors", "checksums": [{"algorithm": "SHA512", "checksumValue": "3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@parcel-watcher-linux-x64-glibc-2.5.1", "name": "@parcel/watcher-linux-x64-glibc", "versionInfo": "2.5.1", "downloadLocation": "https://registry.npmjs.org/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.5.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@parcel/watcher-linux-x64-glibc", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "watcher-linux-x64-glibc", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "GcESn8NZySmfwlTsIur+49yDqSny2IhPeZfXunQi48DMugKeZ7uy1FX83pO0X22sHntJ4Ub+9k34XQCX+oHt2A"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@parcel-watcher-linux-x64-musl-2.5.1", "name": "@parcel/watcher-linux-x64-musl", "versionInfo": "2.5.1", "downloadLocation": "https://registry.npmjs.org/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.5.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@parcel/watcher-linux-x64-musl", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "watcher-linux-x64-musl", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "n0E2EQbatQ3bXhcH2D1XIAANAcTZkQICBPVaxMeaCVBtOpBZpWJuf7LwyWPSBDITb7In8mqQgJ7gH8CILCURXg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@parcel-watcher-2.5.1", "name": "@parcel/watcher", "versionInfo": "2.5.1", "downloadLocation": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.5.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@parcel/watcher", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "watcher", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@scure-base-1.2.1", "name": "@scure/base", "versionInfo": "1.2.1", "downloadLocation": "https://registry.npmjs.org/@scure/base/-/base-1.2.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@scure/base", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "base", "supplier": "Person: <PERSON> (https://paulmillr.com)", "checksums": [{"algorithm": "SHA512", "checksumValue": "DGmGtC8Tt63J5GfHgfl5CuAXh96VF/LD8K9Hr/Gv0J2lAoRGlPOMpqMpMbCTOoOJMZCk2Xt+DskdDyn6dEFdzQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@scure-base-1.2.6", "name": "@scure/base", "versionInfo": "1.2.6", "downloadLocation": "https://registry.npmjs.org/@scure/base/-/base-1.2.6.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@scure/base", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "base", "supplier": "Person: <PERSON> (https://paulmillr.com)", "checksums": [{"algorithm": "SHA512", "checksumValue": "g/nm5FgUa//MCj1gV09zTJTaM6KBAHqLN907YVQqf7zC49+DcO4B1so4ZX07Ef10Twr6nuqYEH9GEggFXA4Fmg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@scure-bip39-1.6.0", "name": "@scure/bip39", "versionInfo": "1.6.0", "downloadLocation": "https://registry.npmjs.org/@scure/bip39/-/bip39-1.6.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@scure/bip39", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "bip39", "supplier": "Person: <PERSON> (https://paulmillr.com)", "checksums": [{"algorithm": "SHA512", "checksumValue": "+lF0BbLiJNwVlev4eKelw1WWLaiKXw7sSl8T6FvBlWkdX+94aGJ4o8XjUdlyhTCjd8c+B3KT3JfS8P0bLRNU6A"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@starfederation-datastar-1.0.0-beta.11", "name": "@starfederation/datastar", "versionInfo": "1.0.0-beta.11", "downloadLocation": "https://registry.npmjs.org/@starfederation/datastar/-/datastar-1.0.0-beta.11.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@starfederation/datastar", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "datastar", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "62TtP/Rm8HVnWxZm1rqhZo+0F57V7A6bKE0FMFMP+1ZeRoDd3lBqYUEdcbSPtIYf9fjoPEUd4TU3bgWS0CGy9w"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@tailwindcss-cli-4.1.11", "name": "@tailwindcss/cli", "versionInfo": "4.1.11", "downloadLocation": "https://registry.npmjs.org/@tailwindcss/cli/-/cli-4.1.11.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@tailwindcss/cli", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "cli", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "7RAFOrVaXCFz5ooEG36Kbh+sMJiI2j4+Ozp71smgjnLfBRu7DTfoq8DsTvzse2/6nDeo2M3vS/FGaxfDgr3rtQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@tailwindcss-node-4.1.11", "name": "@tailwindcss/node", "versionInfo": "4.1.11", "downloadLocation": "https://registry.npmjs.org/@tailwindcss/node/-/node-4.1.11.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@tailwindcss/node", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "node", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "yzhzuGRmv5QyU9qLNg4GTlYI6STedBWRE7NjxP45CsFYYq9taI0zJXZBMqIC/c8fViNLhmrbpSFS57EoxUmD6Q"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@tailwindcss-oxide-linux-x64-gnu-4.1.11", "name": "@tailwindcss/oxide-linux-x64-gnu", "versionInfo": "4.1.11", "downloadLocation": "https://registry.npmjs.org/@tailwindcss/oxide-linux-x64-gnu/-/oxide-linux-x64-gnu-4.1.11.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@tailwindcss/oxide-linux-x64-gnu", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "oxide-linux-x64-gnu", "supplier": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "YW6sblI7xukSD2TdbbaeQVDysIm/UPJtObHJHKxDEcW2exAtY47j52f8jZXkqE1krdnkhCMGqP3dbniu1Te2Fg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@tailwindcss-oxide-linux-x64-musl-4.1.11", "name": "@tailwindcss/oxide-linux-x64-musl", "versionInfo": "4.1.11", "downloadLocation": "https://registry.npmjs.org/@tailwindcss/oxide-linux-x64-musl/-/oxide-linux-x64-musl-4.1.11.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@tailwindcss/oxide-linux-x64-musl", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "oxide-linux-x64-musl", "supplier": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "e3C/RRhGunWYNC3aSF7exsQkdXzQ/M+aYuZHKnw4U7KQwTJotnWsGOIVih0s2qQzmEzOFIJ3+xt7iq67K/p56Q"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@tailwindcss-oxide-4.1.11", "name": "@tailwindcss/oxide", "versionInfo": "4.1.11", "downloadLocation": "https://registry.npmjs.org/@tailwindcss/oxide/-/oxide-4.1.11.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@tailwindcss/oxide", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "oxide", "supplier": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "Q69XzrtAhuyfHo+5/HMgr1lAiPP/G40OMFAnws7xcFEYqcypZmdW8eGXaOUIeOl1dzPJBPENXgbjsOyhg2nkrg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@types-node-24.1.0", "name": "@types/node", "versionInfo": "24.1.0", "downloadLocation": "https://registry.npmjs.org/@types/node/-/node-24.1.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@types/node", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "node", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "ut5FthK5moxFKH2T1CUOC6ctR67rQRvvHdFLCD2Ql6KXmMuCrjsSsRI9UsLCm9M18BMwClv4pn327UvB7eeO1w"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-@types-ws-8.18.1", "name": "@types/ws", "versionInfo": "8.18.1", "downloadLocation": "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/@types/ws", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "ws", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-base64-js-1.5.1", "name": "base64-js", "versionInfo": "1.5.1", "downloadLocation": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/base64-js", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "base64-js", "supplier": "Person: <PERSON><PERSON> <PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-better-sqlite3-12.2.0", "name": "better-sqlite3", "versionInfo": "12.2.0", "downloadLocation": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-12.2.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/better-sqlite3", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "better-sqlite3", "supplier": "Person: <PERSON> <joshuathomas<PERSON>@gmail.com>", "checksums": [{"algorithm": "SHA512", "checksumValue": "eGbYq2CT+tos1fBwLQ/tkBt9J5M3JEHjku4hbvQUePCckkvVf14xWj+1m7dGoK81M/fOjFT7yM9UMeKT/+vFLQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-bindings-1.5.0", "name": "bindings", "versionInfo": "1.5.0", "downloadLocation": "https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/bindings", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "bindings", "supplier": "Person: <PERSON> <<EMAIL>> (http://tootallnate.net)", "checksums": [{"algorithm": "SHA512", "checksumValue": "p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-bl-4.1.0", "name": "bl", "versionInfo": "4.1.0", "downloadLocation": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/bl", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "bl", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-braces-3.0.3", "name": "braces", "versionInfo": "3.0.3", "downloadLocation": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/braces", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "braces", "supplier": "Person: <PERSON> (https://github.com/jonschlink<PERSON>)", "checksums": [{"algorithm": "SHA512", "checksumValue": "yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-buffer-5.7.1", "name": "buffer", "versionInfo": "5.7.1", "downloadLocation": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/buffer", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "buffer", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-chownr-1.1.4", "name": "chownr", "versionInfo": "1.1.4", "downloadLocation": "https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/chownr", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "chownr", "supplier": "Person: <PERSON> <<EMAIL>> (http://blog.izs.me/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-chownr-3.0.0", "name": "chownr", "versionInfo": "3.0.0", "downloadLocation": "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/chownr", "licenseConcluded": "BlueOak-1.0.0", "licenseDeclared": "BlueOak-1.0.0", "licenseInfoFromFiles": ["BlueOak-1.0.0"], "copyrightText": "NOASSERTION", "packageFileName": "chownr", "supplier": "Person: <PERSON> <<EMAIL>> (http://blog.izs.me/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-<PERSON><PERSON>son-0.14.28", "name": "co<PERSON><PERSON>", "versionInfo": "0.14.28", "downloadLocation": "https://registry.npmjs.org/cojson/-/cojson-0.14.28.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/cojson", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "co<PERSON><PERSON>", "supplier": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "aIp4/66KHG5/E/mTrpIB5w3iYklweKOK/d4phKOMndAQy+UawvaZqvEmUteQ8XbTDNp7KZZD5g4p0uIy8BAi8A"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-data-uri-to-buffer-4.0.1", "name": "data-uri-to-buffer", "versionInfo": "4.0.1", "downloadLocation": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/data-uri-to-buffer", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "data-uri-to-buffer", "supplier": "Person: <PERSON> <<EMAIL>> (http://n8.io/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-decompress-response-6.0.0", "name": "decompress-response", "versionInfo": "6.0.0", "downloadLocation": "https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/decompress-response", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "decompress-response", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-deep-extend-0.6.0", "name": "deep-extend", "versionInfo": "0.6.0", "downloadLocation": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/deep-extend", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "deep-extend", "supplier": "Person: <PERSON><PERSON><PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-detect-libc-1.0.3", "name": "detect-libc", "versionInfo": "1.0.3", "downloadLocation": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/detect-libc", "licenseConcluded": "Apache-2.0", "licenseDeclared": "Apache-2.0", "licenseInfoFromFiles": ["Apache-2.0"], "copyrightText": "NOASSERTION", "packageFileName": "detect-libc", "supplier": "Person: <PERSON><PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-detect-libc-2.0.2", "name": "detect-libc", "versionInfo": "2.0.2", "downloadLocation": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.2.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/detect-libc", "licenseConcluded": "Apache-2.0", "licenseDeclared": "Apache-2.0", "licenseInfoFromFiles": ["Apache-2.0"], "copyrightText": "NOASSERTION", "packageFileName": "detect-libc", "supplier": "Person: <PERSON><PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "UX6sGumvvqSaXgdKGUsgZWqcUyIXZ/vZTrlRT/iobiKhGL0zL4d3osHj3uqllWJK+i+sixDS/3COVEOFbupFyw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-detect-libc-2.0.4", "name": "detect-libc", "versionInfo": "2.0.4", "downloadLocation": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/detect-libc", "licenseConcluded": "Apache-2.0", "licenseDeclared": "Apache-2.0", "licenseInfoFromFiles": ["Apache-2.0"], "copyrightText": "NOASSERTION", "packageFileName": "detect-libc", "supplier": "Person: <PERSON><PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-dotenv-17.2.1", "name": "dotenv", "versionInfo": "17.2.1", "downloadLocation": "https://registry.npmjs.org/dotenv/-/dotenv-17.2.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/dotenv", "licenseConcluded": "BSD-2-<PERSON><PERSON>", "licenseDeclared": "BSD-2-<PERSON><PERSON>", "licenseInfoFromFiles": ["BSD-2-<PERSON><PERSON>"], "copyrightText": "NOASSERTION", "packageFileName": "dotenv", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "kQhDYKZecqnM0fCnzI5eIv5L4cAe/iRI+HqMbO/hbRdTAeXDG+M9FjipUxNfbARuEg4iHIbhnhs78BCHNbSxEQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-end-of-stream-1.4.5", "name": "end-of-stream", "versionInfo": "1.4.5", "downloadLocation": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/end-of-stream", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "end-of-stream", "supplier": "Person: <PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-enhanced-resolve-5.18.2", "name": "enhanced-resolve", "versionInfo": "5.18.2", "downloadLocation": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/enhanced-resolve", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "enhanced-resolve", "supplier": "Person: <PERSON> @sokra", "checksums": [{"algorithm": "SHA512", "checksumValue": "6Jw4sE1maoRJo3q8MsSIn2onJFbLTOjY9hlx4DZXmOKvLRd1Ok2kXmAGXaafL2+ijsJZ1ClYbl/pmqr9+k4iUQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-expand-template-2.0.3", "name": "expand-template", "versionInfo": "2.0.3", "downloadLocation": "https://registry.npmjs.org/expand-template/-/expand-template-2.0.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/expand-template", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "expand-template", "supplier": "Person: <PERSON><PERSON> <ralphthenin<PERSON>@riseup.net>", "checksums": [{"algorithm": "SHA512", "checksumValue": "XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-fast-list-1.0.3", "name": "fast-list", "versionInfo": "1.0.3", "downloadLocation": "https://registry.npmjs.org/fast-list/-/fast-list-1.0.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/fast-list", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "fast-list", "supplier": "Person: <PERSON> <<EMAIL>> (http://blog.izs.me/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "Lm56Ci3EqefHNdIneRFuzhpPcpVVBz9fgqVmG3UQIxAefJv1mEYsZ1WQLTWqmdqeGEwbI2t6fbZgp9TqTYARuA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-fast-myers-diff-3.2.0", "name": "fast-myers-diff", "versionInfo": "3.2.0", "downloadLocation": "https://registry.npmjs.org/fast-myers-diff/-/fast-myers-diff-3.2.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/fast-myers-diff", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "fast-myers-diff", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "DxNm4a3gnV35AluvqjeAV3Zx3CAySs0tsaDjnex5JhnJAa2rxEUVCwmaL2gpz42ea1B+IJorlo7XLThXUaTQog"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-fetch-blob-3.2.0", "name": "fetch-blob", "versionInfo": "3.2.0", "downloadLocation": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/fetch-blob", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "fetch-blob", "supplier": "Person: <PERSON> <<EMAIL>> (https://jimmy.warting.se)", "checksums": [{"algorithm": "SHA512", "checksumValue": "7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-file-uri-to-path-1.0.0", "name": "file-uri-to-path", "versionInfo": "1.0.0", "downloadLocation": "https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/file-uri-to-path", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "file-uri-to-path", "supplier": "Person: <PERSON> <<EMAIL>> (http://n8.io/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-fill-range-7.1.1", "name": "fill-range", "versionInfo": "7.1.1", "downloadLocation": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/fill-range", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "fill-range", "supplier": "Person: <PERSON> (https://github.com/jonschlink<PERSON>)", "checksums": [{"algorithm": "SHA512", "checksumValue": "YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-formdata-polyfill-4.0.10", "name": "formdata-polyfill", "versionInfo": "4.0.10", "downloadLocation": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/formdata-polyfill", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "formdata-polyfill", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-fs-constants-1.0.0", "name": "fs-constants", "versionInfo": "1.0.0", "downloadLocation": "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/fs-constants", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "fs-constants", "supplier": "Person: <PERSON> (@mafintosh)", "checksums": [{"algorithm": "SHA512", "checksumValue": "y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-github-from-package-0.0.0", "name": "github-from-package", "versionInfo": "0.0.0", "downloadLocation": "https://registry.npmjs.org/github-from-package/-/github-from-package-0.0.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/github-from-package", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "github-from-package", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-hono-4.8.8", "name": "hono", "versionInfo": "4.8.8", "downloadLocation": "https://registry.npmjs.org/hono/-/hono-4.8.8.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/hono", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "hono", "supplier": "Person: <PERSON><PERSON> <<EMAIL>> (https://github.com/yusukebe)", "checksums": [{"algorithm": "SHA512", "checksumValue": "GbxTGB93Y+MOwCL2tnf9nE6L2Bn3s9D0pS+Mh1FoU4gkuecMVmg8VvHHLO702tq8y9N2fcrlcP8eCm7/feucng"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-ieee754-1.2.1", "name": "ieee754", "versionInfo": "1.2.1", "downloadLocation": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/ieee754", "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseDeclared": "BSD-3-<PERSON><PERSON>", "licenseInfoFromFiles": ["BSD-3-<PERSON><PERSON>"], "copyrightText": "NOASSERTION", "packageFileName": "ieee754", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-inherits-2.0.4", "name": "inherits", "versionInfo": "2.0.4", "downloadLocation": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/inherits", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "inherits", "supplier": "Organization: null", "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-ini-1.3.8", "name": "ini", "versionInfo": "1.3.8", "downloadLocation": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/ini", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "ini", "supplier": "Person: <PERSON> <<EMAIL>> (http://blog.izs.me/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-is-extglob-2.1.1", "name": "is-extglob", "versionInfo": "2.1.1", "downloadLocation": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/is-extglob", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "is-extglob", "supplier": "Person: <PERSON> (https://github.com/jonschlink<PERSON>)", "checksums": [{"algorithm": "SHA512", "checksumValue": "SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-is-glob-4.0.3", "name": "is-glob", "versionInfo": "4.0.3", "downloadLocation": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/is-glob", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "is-glob", "supplier": "Person: <PERSON> (https://github.com/jonschlink<PERSON>)", "checksums": [{"algorithm": "SHA512", "checksumValue": "xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-is-number-7.0.0", "name": "is-number", "versionInfo": "7.0.0", "downloadLocation": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/is-number", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "is-number", "supplier": "Person: <PERSON> (https://github.com/jonschlink<PERSON>)", "checksums": [{"algorithm": "SHA512", "checksumValue": "41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-jazz-crypto-rs-0.0.7", "name": "jazz-crypto-rs", "versionInfo": "0.0.7", "downloadLocation": "https://registry.npmjs.org/jazz-crypto-rs/-/jazz-crypto-rs-0.0.7.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/jazz-crypto-rs", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "jazz-crypto-rs", "supplier": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "Pzs8Zu1zgKVURkBVvqqF1B2r78FaSZdwPbmeCWx0Nb3nibzPuB/kSKEO7LJsKnb3P5HO0v+lTIJ53mGC5H1urQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-jazz-tools-0.14.28", "name": "jazz-tools", "versionInfo": "0.14.28", "downloadLocation": "https://registry.npmjs.org/jazz-tools/-/jazz-tools-0.14.28.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/jazz-tools", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "jazz-tools", "supplier": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "T6HGV2zzDkITAfy5B/VcDbWyS9aZxihtqDVg5erU1VxfpH6SR0GKI9mTG+odCZ2zJVfzQDoanEY0StDmWNA5Ag"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-jiti-2.5.0", "name": "jiti", "versionInfo": "2.5.0", "downloadLocation": "https://registry.npmjs.org/jiti/-/jiti-2.5.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/jiti", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "jiti", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "NWDAhdnATItTnRhip9VTd8oXDjVcbhetRN6YzckApnXGxpGUooKMAaf0KVvlZG0+KlJMGkeLElVn4M1ReuxKUQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-js-base64-3.7.7", "name": "js-base64", "versionInfo": "3.7.7", "downloadLocation": "https://registry.npmjs.org/js-base64/-/js-base64-3.7.7.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/js-base64", "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseDeclared": "BSD-3-<PERSON><PERSON>", "licenseInfoFromFiles": ["BSD-3-<PERSON><PERSON>"], "copyrightText": "NOASSERTION", "packageFileName": "js-base64", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-libsql-0.5.17", "name": "libsql", "versionInfo": "0.5.17", "downloadLocation": "https://registry.npmjs.org/libsql/-/libsql-0.5.17.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/libsql", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "libsql", "supplier": "Person: <PERSON><PERSON><PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "RRlj5XQI9+Wq+/5UY8EnugSWfRmHEw4hn3DKlPrkUgZONsge1PwTtHcpStP6MSNi8ohcbsRgEHJaymA33a8cBw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-lightningcss-linux-x64-gnu-1.30.1", "name": "lightningcss-linux-x64-gnu", "versionInfo": "1.30.1", "downloadLocation": "https://registry.npmjs.org/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.30.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/lightningcss-linux-x64-gnu", "licenseConcluded": "MPL-2.0", "licenseDeclared": "MPL-2.0", "licenseInfoFromFiles": ["MPL-2.0"], "copyrightText": "NOASSERTION", "packageFileName": "lightningcss-linux-x64-gnu", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "piWx3z4wN8J8z3+O5kO74+yr6ze/dKmPnI7vLqfSqI8bccaTGY5xiSGVIJBDd5K5BHlvVLpUB3S2YCfelyJ1bw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-lightningcss-linux-x64-musl-1.30.1", "name": "lightningcss-linux-x64-musl", "versionInfo": "1.30.1", "downloadLocation": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.30.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/lightningcss-linux-x64-musl", "licenseConcluded": "MPL-2.0", "licenseDeclared": "MPL-2.0", "licenseInfoFromFiles": ["MPL-2.0"], "copyrightText": "NOASSERTION", "packageFileName": "lightningcss-linux-x64-musl", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-lightningcss-1.30.1", "name": "lightningcss", "versionInfo": "1.30.1", "downloadLocation": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.30.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/lightningcss", "licenseConcluded": "MPL-2.0", "licenseDeclared": "MPL-2.0", "licenseInfoFromFiles": ["MPL-2.0"], "copyrightText": "NOASSERTION", "packageFileName": "lightningcss", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-lucide-react-0.525.0", "name": "lucide-react", "versionInfo": "0.525.0", "downloadLocation": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.525.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/lucide-react", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "lucide-react", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "Tm1txJ2OkymCGkvwoHt33Y2JpN5xucVq1slHcgE6Lk0WjDfjgKWor5CdVER8U6DvcfMwh4M8XxmpTiyzfmfDYQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-magic-string-0.30.17", "name": "magic-string", "versionInfo": "0.30.17", "downloadLocation": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/magic-string", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "magic-string", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-micromatch-4.0.8", "name": "micromatch", "versionInfo": "4.0.8", "downloadLocation": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/micromatch", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "micromatch", "supplier": "Person: <PERSON> (https://github.com/jonschlink<PERSON>)", "checksums": [{"algorithm": "SHA512", "checksumValue": "PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-mimic-response-3.1.0", "name": "mimic-response", "versionInfo": "3.1.0", "downloadLocation": "https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/mimic-response", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "mimic-response", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-minimist-1.2.8", "name": "minimist", "versionInfo": "1.2.8", "downloadLocation": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/minimist", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "minimist", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-minipass-7.1.2", "name": "minipass", "versionInfo": "7.1.2", "downloadLocation": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/minipass", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "minipass", "supplier": "Person: <PERSON> <<EMAIL>> (http://blog.izs.me/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-minizlib-3.0.2", "name": "minizlib", "versionInfo": "3.0.2", "downloadLocation": "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/minizlib", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "minizlib", "supplier": "Person: <PERSON> <<EMAIL>> (http://blog.izs.me/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-mkdirp-classic-0.5.3", "name": "mkdirp-classic", "versionInfo": "0.5.3", "downloadLocation": "https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/mkdirp-classic", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "mkdirp-classic", "supplier": "Person: <PERSON> (@mafintosh)", "checksums": [{"algorithm": "SHA512", "checksumValue": "gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-mkdirp-3.0.1", "name": "mkdirp", "versionInfo": "3.0.1", "downloadLocation": "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/mkdirp", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "mkdirp", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-mri-1.2.0", "name": "mri", "versionInfo": "1.2.0", "downloadLocation": "https://registry.npmjs.org/mri/-/mri-1.2.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/mri", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "mri", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-mutative-1.2.0", "name": "mutative", "versionInfo": "1.2.0", "downloadLocation": "https://registry.npmjs.org/mutative/-/mutative-1.2.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/mutative", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "mutative", "supplier": "Person: unadlib", "checksums": [{"algorithm": "SHA512", "checksumValue": "1muFw45Lwjso6TSBGiXfbjKS01fVSD/qaqBfTo/gXgp79e8KM4Sa1XP/S4iN2/DvSdIZgjFJI+JIhC7eKf3GTg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-napi-build-utils-2.0.0", "name": "napi-build-utils", "versionInfo": "2.0.0", "downloadLocation": "https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-2.0.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/napi-build-utils", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "napi-build-utils", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "GEbrYkbfF7MoNaoh2iGG84Mnf/WZfB0GdGEsM8wz7Expx/LlWf5U8t9nvJKXSp3qr5IsEbK04cBGhol/KwOsWA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-neverthrow-7.2.0", "name": "neverthrow", "versionInfo": "7.2.0", "downloadLocation": "https://registry.npmjs.org/neverthrow/-/neverthrow-7.2.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/neverthrow", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "neverthrow", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "iGBUfFB7yPczHHtA8dksKTJ9E8TESNTAx1UQWW6TzMF280vo9jdPYpLUXrMN1BCkPdHFdNG3fxOt2CUad8KhAw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-node-abi-3.75.0", "name": "node-abi", "versionInfo": "3.75.0", "downloadLocation": "https://registry.npmjs.org/node-abi/-/node-abi-3.75.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/node-abi", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "node-abi", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "OhYaY5sDsIka7H7AtijtI9jwGYLyl29eQn/W623DiN/MIv5sUqc4g7BIDThX+gb7di9f6xK02nkp8sdfFWZLTg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-node-addon-api-7.1.1", "name": "node-addon-api", "versionInfo": "7.1.1", "downloadLocation": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-7.1.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/node-addon-api", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "node-addon-api", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-node-fetch-3.3.2", "name": "node-fetch", "versionInfo": "3.3.2", "downloadLocation": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/node-fetch", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "node-fetch", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-once-1.4.0", "name": "once", "versionInfo": "1.4.0", "downloadLocation": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/once", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "once", "supplier": "Person: <PERSON> <<EMAIL>> (http://blog.izs.me/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-picocolors-1.1.1", "name": "picocolors", "versionInfo": "1.1.1", "downloadLocation": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/picocolors", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "picocolors", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-picomatch-2.3.1", "name": "picomatch", "versionInfo": "2.3.1", "downloadLocation": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/picomatch", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "picomatch", "supplier": "Person: <PERSON> (https://github.com/jonschlink<PERSON>)", "checksums": [{"algorithm": "SHA512", "checksumValue": "JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-prebuild-install-7.1.3", "name": "prebuild-install", "versionInfo": "7.1.3", "downloadLocation": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.1.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/prebuild-install", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "prebuild-install", "supplier": "Person: <PERSON> (@mafintosh)", "checksums": [{"algorithm": "SHA512", "checksumValue": "8Mf2cbV7x1cXPUILADGI3wuhfqWvtiLA1iclTDbFRZkgRQS0NqsPZphna9V+HyTEadheuPmjaJMsbzKQFOzLug"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-promise-limit-2.7.0", "name": "promise-limit", "versionInfo": "2.7.0", "downloadLocation": "https://registry.npmjs.org/promise-limit/-/promise-limit-2.7.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/promise-limit", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "promise-limit", "supplier": "Person: <PERSON> <tim<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "checksums": [{"algorithm": "SHA512", "checksumValue": "7nJ6v5lnJsXwGprnGXga4wx6d1POjvi5Qmf1ivTRxTjH4Z/9Czja/UCMLVmB9N93GeWOU93XaFaEt6jbuoagNw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-pump-3.0.3", "name": "pump", "versionInfo": "3.0.3", "downloadLocation": "https://registry.npmjs.org/pump/-/pump-3.0.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/pump", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "pump", "supplier": "Person: <PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-queueueue-4.1.2", "name": "queueueue", "versionInfo": "4.1.2", "downloadLocation": "https://registry.npmjs.org/queueueue/-/queueueue-4.1.2.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/queueueue", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "queueueue", "supplier": "Person: s<PERSON><PERSON> <<EMAIL>> + <PERSON><PERSON><PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "YNOihQyk72FRrGQP+fVjUDVCSgxtHS5EdfgXjCYwPCTyhVWXriHfUt0XK0eT7vcBX0m91g0e0KVO8CxoNLh60g"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-rc-1.2.8", "name": "rc", "versionInfo": "1.2.8", "downloadLocation": "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/rc", "licenseConcluded": "BSD-2-<PERSON><PERSON>", "licenseDeclared": "BSD-2-<PERSON><PERSON>", "licenseInfoFromFiles": ["BSD-2-<PERSON><PERSON>"], "copyrightText": "NOASSERTION", "packageFileName": "rc", "supplier": "Person: <PERSON> <<EMAIL>> (dominictarr.com)", "checksums": [{"algorithm": "SHA512", "checksumValue": "y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-react-19.1.0", "name": "react", "versionInfo": "19.1.0", "downloadLocation": "https://registry.npmjs.org/react/-/react-19.1.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/react", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "react", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-readable-stream-3.6.2", "name": "readable-stream", "versionInfo": "3.6.2", "downloadLocation": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/readable-stream", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "readable-stream", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-safe-buffer-5.2.1", "name": "safe-buffer", "versionInfo": "5.2.1", "downloadLocation": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/safe-buffer", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "safe-buffer", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-semver-7.7.2", "name": "semver", "versionInfo": "7.7.2", "downloadLocation": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/semver", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "semver", "supplier": "Person: GitHub Inc.", "checksums": [{"algorithm": "SHA512", "checksumValue": "RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-simple-concat-1.0.1", "name": "simple-concat", "versionInfo": "1.0.1", "downloadLocation": "https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/simple-concat", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "simple-concat", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-simple-get-4.0.1", "name": "simple-get", "versionInfo": "4.0.1", "downloadLocation": "https://registry.npmjs.org/simple-get/-/simple-get-4.0.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/simple-get", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "simple-get", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-source-map-js-1.2.1", "name": "source-map-js", "versionInfo": "1.2.1", "downloadLocation": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/source-map-js", "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseDeclared": "BSD-3-<PERSON><PERSON>", "licenseInfoFromFiles": ["BSD-3-<PERSON><PERSON>"], "copyrightText": "NOASSERTION", "packageFileName": "source-map-js", "supplier": "Person: <PERSON><PERSON> 7rulnik Semirulnik <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-strip-json-comments-2.0.1", "name": "strip-json-comments", "versionInfo": "2.0.1", "downloadLocation": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/strip-json-comments", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "strip-json-comments", "supplier": "Person: <PERSON><PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-tailwindcss-4.1.11", "name": "tailwindcss", "versionInfo": "4.1.11", "downloadLocation": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.1.11.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/tailwindcss", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "tailwindcss", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "2E9TBm6MDD/xKYe+dvJZAmg3yxIEDNRc0jwlNyDg/4Fil2QcSLjFKGVff0lAf1jjeaArlG/M75Ey/EYr/OJtBA"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-tar-fs-2.1.3", "name": "tar-fs", "versionInfo": "2.1.3", "downloadLocation": "https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/tar-fs", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "tar-fs", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "090nwYJDmlhwFwEW3QQl+vaNnxsO2yVsd45eTKRBzSzu+hlb1w2K9inVq5b0ngXuLVqQ4ApvsUHHnu/zQNkWAg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-tar-stream-2.2.0", "name": "tar-stream", "versionInfo": "2.2.0", "downloadLocation": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/tar-stream", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "tar-stream", "supplier": "Person: <PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-tar-7.4.3", "name": "tar", "versionInfo": "7.4.3", "downloadLocation": "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/tar", "licenseConcluded": "ISC", "licenseDeclared": "ISC", "licenseInfoFromFiles": ["ISC"], "copyrightText": "NOASSERTION", "packageFileName": "tar", "supplier": "Person: <PERSON>", "checksums": [{"algorithm": "SHA512", "checksumValue": "5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-to-regex-range-5.0.1", "name": "to-regex-range", "versionInfo": "5.0.1", "downloadLocation": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/to-regex-range", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "to-regex-range", "supplier": "Person: <PERSON> (https://github.com/jonschlink<PERSON>)", "checksums": [{"algorithm": "SHA512", "checksumValue": "65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-tslib-2.8.1", "name": "tslib", "versionInfo": "2.8.1", "downloadLocation": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/tslib", "licenseConcluded": "0BSD", "licenseDeclared": "0BSD", "licenseInfoFromFiles": ["0BSD"], "copyrightText": "NOASSERTION", "packageFileName": "tslib", "supplier": "Microsoft Corporation", "checksums": [{"algorithm": "SHA512", "checksumValue": "oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-tunnel-agent-0.6.0", "name": "tunnel-agent", "versionInfo": "0.6.0", "downloadLocation": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/tunnel-agent", "licenseConcluded": "Apache-2.0", "licenseDeclared": "Apache-2.0", "licenseInfoFromFiles": ["Apache-2.0"], "copyrightText": "NOASSERTION", "packageFileName": "tunnel-agent", "supplier": "Person: <PERSON><PERSON> <<EMAIL>> (http://www.futurealoof.com)", "checksums": [{"algorithm": "SHA512", "checksumValue": "McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-undici-types-7.8.0", "name": "undici-types", "versionInfo": "7.8.0", "downloadLocation": "https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/undici-types", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "undici-types", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-unicode-segmenter-0.12.0", "name": "unicode-segmenter", "versionInfo": "0.12.0", "downloadLocation": "https://registry.npmjs.org/unicode-segmenter/-/unicode-segmenter-0.12.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/unicode-segmenter", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "unicode-segmenter", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "PK6M1A1tt3ky6cFAJW/JOO9ml/sbEvim+SL75FM8slck08ACbF0TZj+tdUd3Mh8Tk5XieqUDPwmLjiyRQvb4pw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-uuid-11.1.0", "name": "uuid", "versionInfo": "11.1.0", "downloadLocation": "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/uuid", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "uuid", "supplier": "Organization: null", "checksums": [{"algorithm": "SHA512", "checksumValue": "0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-ws-8.18.3", "name": "ws", "versionInfo": "8.18.3", "downloadLocation": "https://registry.npmjs.org/ws/-/ws-8.18.3.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/ws", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "ws", "supplier": "Person: <PERSON><PERSON> <<EMAIL>> (http://2x.io)", "checksums": [{"algorithm": "SHA512", "checksumValue": "PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-yallist-5.0.0", "name": "yallist", "versionInfo": "5.0.0", "downloadLocation": "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/yallist", "licenseConcluded": "BlueOak-1.0.0", "licenseDeclared": "BlueOak-1.0.0", "licenseInfoFromFiles": ["BlueOak-1.0.0"], "copyrightText": "NOASSERTION", "packageFileName": "yallist", "supplier": "Person: <PERSON> <<EMAIL>> (http://blog.izs.me/)", "checksums": [{"algorithm": "SHA512", "checksumValue": "YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw"}], "primaryPackagePurpose": "LIBRARY"}, {"SPDXID": "SPDXRef-zod-3.25.28", "name": "zod", "versionInfo": "3.25.28", "downloadLocation": "https://registry.npmjs.org/zod/-/zod-3.25.28.tgz", "filesAnalyzed": true, "homepage": "https://www.npmjs.com/package/zod", "licenseConcluded": "MIT", "licenseDeclared": "MIT", "licenseInfoFromFiles": ["MIT"], "copyrightText": "NOASSERTION", "packageFileName": "zod", "supplier": "Person: <PERSON> <<EMAIL>>", "checksums": [{"algorithm": "SHA512", "checksumValue": "/nt/67WYKnr5by3YS7LroZJbtcCBurDKKPBPWWzaxvVCGuG/NOsiKkrjoOhI8mJ+SQUXEbUzeB3S+6XDUEEj7Q"}], "primaryPackagePurpose": "LIBRARY"}], "files": [{"SPDXID": "SPDXRef-ampproject-remapping-2.3.0-Apache-2.0", "fileName": "./remapping/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "d7adce7c898a226d04cce02982864a724ba2a710efd5a0e42a3da49dcec84f5a"}], "fileTypes": ["OTHER"], "licenseConcluded": "Apache-2.0", "licenseInfoInFiles": ["Apache-2.0"], "noticeText": "Apache-2.0"}, {"SPDXID": "SPDXRef-anthropic-ai-sdk-0.57.0-MIT", "fileName": "./sdk/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "3c1f8d88ba865f9c48f3a2c01394a208f71870a7cb36211862f2e13c93e0fd8d"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-emnapi-runtime-1.4.5-MIT", "fileName": "./runtime/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "99b2dc298ce824490d3950a554d08fea1f810c8106b6381a47921ec890c65ca1"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-hono-node-server-1.17.1-MIT", "fileName": "./node-server/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "8b001d085d7fd4d249d5e96357ee461c51917ff72fa5c750d0987fd13440316f"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-isaacs-fs-minipass-4.0.1-ISC", "fileName": "./fs-minipass/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "ab8c1c2bce664e4c76b1937fa1a28f00ca5231a70cf0c1f78dd473101a9d2a88"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-jridgewell-gen-mapping-0.3.12-MIT", "fileName": "./gen-mapping/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "130a211784d97684746ef150a67d7fc60bf414519a6cefbdd23ce9e2bdff197d"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-j<PERSON>well-sourcemap-codec-1.5.4-MIT", "fileName": "./sourcemap-codec/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "1545bf6f0965eec61b81f094dec10088da7da2de595608612d8733da6fb6abfc"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-jridgewell-trace-mapping-0.3.29-MIT", "fileName": "./trace-mapping/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "77e24c48b5270746cdf1e3ba3f911ecde6f73d09c8568b4000a2eb4ce4ee28ce"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-libsql-client-0.15.10-MIT", "fileName": "./client/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "4208cc8a2e8ea1133c2ed5883f39e43cf5c7175038b31d0de3bd9de8080fbc60"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-libsql-core-0.15.10-MIT", "fileName": "./core/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "7b02330386604fef4b349e9477bc63604f42aaa77d4cc840552e910d40edc617"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-libsql-hrana-client-0.7.0-MIT", "fileName": "./hrana-client/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "1145034e7c6f46b596fe371a03712346068b10acc56ce49018c9e35178dfa620"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-libsql-isomorphic-fetch-0.3.1-MIT", "fileName": "./isomorphic-fetch/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "484ac7fc920e0fd3aff7e45ab1af6a4b09f4a30c742bb410563929777273b02d"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-libsql-isomorphic-ws-0.1.5-MIT", "fileName": "./isomorphic-ws/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "0fcfe73e854a10d24c665450986ec04edbc2b496271871eba07464528e7386d0"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-libsql-linux-x64-gnu-0.5.17-MIT", "fileName": "./linux-x64-gnu/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "d2428a84830bd09451da97228a687b110a53ad13f0d4e5e8745d2094a5e11c01"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-libsql-linux-x64-musl-0.5.17-MIT", "fileName": "./linux-x64-musl/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "30db73f9c64a6444241cc20c436834a09bde72e5102a77ec69c7f8fcdfe3e1df"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-neon-rs-load-0.0.4-MIT", "fileName": "./load/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "154ad669d2f2b3eaae2e23001ae7c08c3e1c70d7d3febe0026967767e6a1b2b2"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-noble-ciphers-1.3.0-MIT", "fileName": "./ciphers/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "d4fd92770dc4d2af454902aaea3e16f053e6f8b48ad2d95c016c662a5f1be2d4"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-noble-curves-1.9.4-MIT", "fileName": "./curves/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "464cc2d2845a57beb0d627671bb8ee41391d534b30e33546cd24e115aa7963ca"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-noble-hashes-1.8.0-MIT", "fileName": "./hashes/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "7c4657c5e616c22cefa2691cc30670824a639dc71d97bf83925be2d239dc60e3"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-opentelemetry-api-1.9.0-Apache-2.0", "fileName": "./api/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "cfa6a06e9bbf5086120c2e77665cbeb6dd04e7344858c011bf1e55330da88c32"}], "fileTypes": ["OTHER"], "licenseConcluded": "Apache-2.0", "licenseInfoInFiles": ["Apache-2.0"], "noticeText": "Apache-2.0"}, {"SPDXID": "SPDXRef-parcel-watcher-linux-x64-glibc-2.5.1-MIT", "fileName": "./watcher-linux-x64-glibc/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "507bd33fb3b2059e15c545ab44253ff92bb84128ff6020d0445daa60f8397c20"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-parcel-watcher-linux-x64-musl-2.5.1-MIT", "fileName": "./watcher-linux-x64-musl/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "b3f4324da03ba9243375bae12cc221da4bfbd35960bec85383274f8c7e39b1c5"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-parcel-watcher-2.5.1-MIT", "fileName": "./watcher/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "f9e24627c11385d6a28efad5fc500dd395fe85f20f35e96b28a32f75f0bf3eaa"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-scure-base-1.2.1-MIT", "fileName": "./base/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "71d39f4501e66c2008ae03a4da3e93466e510e1e4eed67e11384cf3e67604a91"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-scure-base-1.2.6-MIT", "fileName": "./base/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "55662e9d984231ed6fb2b4b36e4acb96075b44f8b1478c8bd51c6b03a888c25c"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-scure-bip39-1.6.0-MIT", "fileName": "./bip39/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "151886550749a81544445ab42710109941fb643a0ba8c674253d99fefa2c1b24"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-starfederation-datastar-1.0.0-beta.11-MIT", "fileName": "./datastar/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "562037cc2956b0f70053315babe28ed4197a56c2f76515818c9d1cf0791aabe7"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tailwindcss-cli-4.1.11-MIT", "fileName": "./cli/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "170d5cbe8dca6efaa3f7b753a823c6e5780daefaa321667b418c4c81e1fa803c"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tailwindcss-node-4.1.11-MIT", "fileName": "./node/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "6f4129bb232d2c35c54029d6289e145722ddf0563c873e455416cc3ee74a44d2"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tailwindcss-oxide-linux-x64-gnu-4.1.11-MIT", "fileName": "./oxide-linux-x64-gnu/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "584aa159ee2556bb3312103d32b0b5fc84fd2c23437b015cbb208791c5424127"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tailwindcss-oxide-linux-x64-musl-4.1.11-MIT", "fileName": "./oxide-linux-x64-musl/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "4ab3be9095c3db25dc9f472400eb6bb17c751633a0636e1c1628fc73476da27f"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tailwindcss-oxide-4.1.11-MIT", "fileName": "./oxide/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "4379fc143b8c99332232e0d134bc1cb988df8edb45c3c440f64e64d0c6827059"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-types-node-24.1.0-MIT", "fileName": "./node/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "1c6e77e1b4ffa934224f77a0c95e9d10297997747715b6b61add5372a57abf5a"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-types-ws-8.18.1-MIT", "fileName": "./ws/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "92f0a87117b71231a0864f7bb7c6a84b7fc91dbdd7d33379fc23d655980ad083"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-base64-js-1.5.1-MIT", "fileName": "./base64-js/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "9758f3ab8c45e07bb9a368e32f9a8b3729623bbf47cbbb205b32d674ab2a91f0"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-better-sqlite3-12.2.0-MIT", "fileName": "./better-sqlite3/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "f4b56f336d61ff93a284bc4e5dd110f924721c774fb0542cbc73b5c7ad9c767c"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-bindings-1.5.0-MIT", "fileName": "./bindings/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "a87721fe406e1f1798fef44d697b46ea1efe346fda118010334713346ee4207c"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-bl-4.1.0-MIT", "fileName": "./bl/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "fea66d3b8671e46f77e83db040837cf72da180b8d390d53c43d882fd553659df"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-braces-3.0.3-MIT", "fileName": "./braces/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "56f08b888a4f30dc7cf8a7dbb36ffe92b737912ba36abe9d069d32167c957ac7"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-buffer-5.7.1-MIT", "fileName": "./buffer/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "5af7b62dbf5575a301cd13e8616f1d7d9cff3c90f9fab7bc3d6a1f4fbfee94eb"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-chownr-1.1.4-ISC", "fileName": "./chownr/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "7c18e85ac9eff99dc6cb6c5626e11ebea658e69119e0bc8cb4bd799c2b48125a"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-chownr-3.0.0-BlueOak-1.0.0", "fileName": "./chownr/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "4300e90fdd91ec7035047473c60f880251a9801bd786302729d4277751d3b948"}], "fileTypes": ["OTHER"], "licenseConcluded": "BlueOak-1.0.0", "licenseInfoInFiles": ["BlueOak-1.0.0"], "noticeText": "BlueOak-1.0.0"}, {"SPDXID": "SPDXRef-<PERSON><PERSON><PERSON>-0.14.28-MIT", "fileName": "./cojson/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "143b8ad5031fff91892aeceace412b519080982cdcb1b980a7eb582c2f1d7d55"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-data-uri-to-buffer-4.0.1-MIT", "fileName": "./data-uri-to-buffer/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "ad4f90a737ab5d8af4dad265e9218456e3779ca5beb70df38dd5feecf80121dd"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-decompress-response-6.0.0-MIT", "fileName": "./decompress-response/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "8fb6b0b19d09cc1c5b2caad128ae39b2fd693a35bb3d440c4e1143bfb9d10080"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-deep-extend-0.6.0-MIT", "fileName": "./deep-extend/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "21b307891704eb34e9be621d7563d13734f75f41d8f97825862b05ca5bc2d217"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-detect-libc-1.0.3-Apache-2.0", "fileName": "./detect-libc/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "bb418f4fa450f8a418f8fc46c33727edef55aefd54c43ec6a9421a65ca8d9827"}], "fileTypes": ["OTHER"], "licenseConcluded": "Apache-2.0", "licenseInfoInFiles": ["Apache-2.0"], "noticeText": "Apache-2.0"}, {"SPDXID": "SPDXRef-detect-libc-2.0.2-Apache-2.0", "fileName": "./detect-libc/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "483e653a9d5885730013bd637a328d7fa9b9970c4af3db8075d414fb13134caf"}], "fileTypes": ["OTHER"], "licenseConcluded": "Apache-2.0", "licenseInfoInFiles": ["Apache-2.0"], "noticeText": "Apache-2.0"}, {"SPDXID": "SPDXRef-detect-libc-2.0.4-Apache-2.0", "fileName": "./detect-libc/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "95e29f682649caf233b4ac9729efa0c1b3718ef179e7b44e21ea01a0941af071"}], "fileTypes": ["OTHER"], "licenseConcluded": "Apache-2.0", "licenseInfoInFiles": ["Apache-2.0"], "noticeText": "Apache-2.0"}, {"SPDXID": "SPDXRef-dotenv-17.2.1-BSD-2-<PERSON><PERSON>", "fileName": "./dotenv/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "b9a0351d576d578f275098d568994b8b2f47fc511303564e5a8abd809dd3264f"}], "fileTypes": ["OTHER"], "licenseConcluded": "BSD-2-<PERSON><PERSON>", "licenseInfoInFiles": ["BSD-2-<PERSON><PERSON>"], "noticeText": "BSD-2-<PERSON><PERSON>"}, {"SPDXID": "SPDXRef-end-of-stream-1.4.5-MIT", "fileName": "./end-of-stream/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "da504d0bef5a5974590b62f58df0003b448eccde6b5fbeb044506b7e43368eb5"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-enhanced-resolve-5.18.2-MIT", "fileName": "./enhanced-resolve/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "c4e20b68962c28ca95e3a48a48fb75b5990976e44b94e22774c8b6204ea1230a"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-expand-template-2.0.3-MIT", "fileName": "./expand-template/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "90f652967155275d9fab2887a845454ee797340b28cc0c1b00f1889aaa65f771"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "(MIT OR WTFPL)"}, {"SPDXID": "SPDXRef-fast-list-1.0.3-ISC", "fileName": "./fast-list/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "2fb5d1a95325ff56aa5d1743bf298f17a6a8bf439c0333d335b209419fac7fc0"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-fast-myers-diff-3.2.0-MIT", "fileName": "./fast-myers-diff/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "c3d13823b31c3c241fdc9bd19016cc5b9f139b1b95fdd75101fbc64148483bdd"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-fetch-blob-3.2.0-MIT", "fileName": "./fetch-blob/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "ee47bb73168049962586a09317017c44b1b10b2a90fc74e5c4561efdb361411b"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-file-uri-to-path-1.0.0-MIT", "fileName": "./file-uri-to-path/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "71eb1e24bb9694f89c613fa0aa307f977dd43f41d11794c7b48fabf6c55f66b0"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-fill-range-7.1.1-MIT", "fileName": "./fill-range/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "87f5c78f9b47d21f9ca62b60cff1d250a3b468eab320dd13c3f3ab2464a02735"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-formdata-polyfill-4.0.10-MIT", "fileName": "./formdata-polyfill/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "09767e86a596171ef17e8ec4977eb11f530654740fa3105dbbd7cd545753421e"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-fs-constants-1.0.0-MIT", "fileName": "./fs-constants/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "9cefec768b4f8c52591b840367d077a46a5a2b72ea2778dcf037afd96b234671"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-github-from-package-0.0.0-MIT", "fileName": "./github-from-package/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "67742aa2b856e3523c2f89132e41cc5026c49ffe7df58bd3ea906caaa217bd79"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-hono-4.8.8-MIT", "fileName": "./hono/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "a1dace8941e04fa2a0a13c9463804021e16c5021cce9e4edf0ebec4ed61420c0"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-ieee754-1.2.1-BSD-3-<PERSON><PERSON>", "fileName": "./ieee754/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "a8004b9c8dffe2e1b01a058ecf968a5d50beabcabc43cc98c655184ba6afc050"}], "fileTypes": ["OTHER"], "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseInfoInFiles": ["BSD-3-<PERSON><PERSON>"], "noticeText": "BSD-3-<PERSON><PERSON>"}, {"SPDXID": "SPDXRef-inherits-2.0.4-ISC", "fileName": "./inherits/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "be645800bc94fd8de29c8ae91690549b316cc437100108aeea7b2f347693cc80"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-ini-1.3.8-ISC", "fileName": "./ini/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "8cfafc3521292bd7434d2f40762984f36eb12f03bde92b42a9df2ea17552f4a4"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-is-extglob-2.1.1-MIT", "fileName": "./is-extglob/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "8031afcd87f448d75ba8a5dd2cde9c68f982a94f406ebae8e5fb7cf6dfdeede2"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-is-glob-4.0.3-MIT", "fileName": "./is-glob/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "1cce62b80db12fc033d85250b5881b1a288476229abda8e1abd983724befd56f"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-is-number-7.0.0-MIT", "fileName": "./is-number/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "51c133f4e41df982aef69027249ff9d7262645029f437d079adc7c83328fb620"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-jazz-crypto-rs-0.0.7-MIT", "fileName": "./jazz-crypto-rs/LICENSE", "checksums": [{"algorithm": "SHA256", "checksumValue": "0fdc21d3fb11db5a636017f7bdc9b92dcf3daa41720ffedf89a8e91cb91d39bb"}], "fileTypes": ["TEXT"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT License\n\nCopyright (c) 2025 Garden Computing\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE."}, {"SPDXID": "SPDXRef-jazz-tools-0.14.28-MIT", "fileName": "./jazz-tools/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "b7aca1ec13850434f7540c4a04793948d2c070c9596cd987541efe7bf1034b69"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-jiti-2.5.0-MIT", "fileName": "./jiti/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "bc8ad22bd25a3ffa62426334a1c24ede84b7ce2f1bba3f69099578d91f10256a"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-js-base64-3.7.7-BSD-3-<PERSON><PERSON>", "fileName": "./js-base64/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "48ee6969afba4d24d341bcc444c312bf01de28c1ce436a7275fadfe2fcaa7aa2"}], "fileTypes": ["OTHER"], "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseInfoInFiles": ["BSD-3-<PERSON><PERSON>"], "noticeText": "BSD-3-<PERSON><PERSON>"}, {"SPDXID": "SPDXRef-libsql-0.5.17-MIT", "fileName": "./libsql/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "0f162af24e5a1743a2bf1096c899ea265be13c1d88ef179b15b894525923b91e"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-lightningcss-linux-x64-gnu-1.30.1-MPL-2.0", "fileName": "./lightningcss-linux-x64-gnu/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "5a0828631dae02f1e2896770a2e7b1ad925c63a4108669c3ab7aabe8bf258977"}], "fileTypes": ["OTHER"], "licenseConcluded": "MPL-2.0", "licenseInfoInFiles": ["MPL-2.0"], "noticeText": "MPL-2.0"}, {"SPDXID": "SPDXRef-lightningcss-linux-x64-musl-1.30.1-MPL-2.0", "fileName": "./lightningcss-linux-x64-musl/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "a4715dc0310885e5c5192b543ebd4e9f8b6a8223a0124c323600f6a15328b964"}], "fileTypes": ["OTHER"], "licenseConcluded": "MPL-2.0", "licenseInfoInFiles": ["MPL-2.0"], "noticeText": "MPL-2.0"}, {"SPDXID": "SPDXRef-lightningcss-1.30.1-MPL-2.0", "fileName": "./lightningcss/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "443b84f584e98c91dcfb657a3ac9f1dadc729cd0dcac5da1e9fa731d8924b569"}], "fileTypes": ["OTHER"], "licenseConcluded": "MPL-2.0", "licenseInfoInFiles": ["MPL-2.0"], "noticeText": "MPL-2.0"}, {"SPDXID": "SPDXRef-lucide-react-0.525.0-ISC", "fileName": "./lucide-react/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "1bb9ef3151f755f43287d55eab440ce74d0a84b9d5c714e7c8226d79d7699ca1"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-magic-string-0.30.17-MIT", "fileName": "./magic-string/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "3f8dc713ba9c551d94685cfeb663e54ad57295b7e2e7efbbdfa13d9f667c3481"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-micromatch-4.0.8-MIT", "fileName": "./micromatch/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "94dbc881c0329813f2481f35fcfa725a2285d45926d33e9b9b8b80b28487752b"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-mimic-response-3.1.0-MIT", "fileName": "./mimic-response/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "ff358428a418a46fb2431a2c48b396fb01c8291fec9e1f14434ade01196c8e9c"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-minimist-1.2.8-MIT", "fileName": "./minimist/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "252881f6a068a3e70386706d88a694b5e8e9e053b6d9f309fd9a329e651bbf81"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-minipass-7.1.2-ISC", "fileName": "./minipass/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "f6cfe184c40e1c08eac8f614d7ca6888fee9654259668ad0670bb6011f74a005"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-minizlib-3.0.2-MIT", "fileName": "./minizlib/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "e3be75cf1475cf676d2aeb5a91e3afa3975018565f654b61906c6ca5565e68e0"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-mkdirp-classic-0.5.3-MIT", "fileName": "./mkdirp-classic/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "dd81e4703dc7005247e7c2dc056c67c936923a40b4dc9b266cf8acf3442ba89c"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-mkdirp-3.0.1-MIT", "fileName": "./mkdirp/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "366d0eca09ca7018417907f67a0caf02bdc9ae492e90359f9df7cfe53e7a768e"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-mri-1.2.0-MIT", "fileName": "./mri/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "aa262c911b04e093f9293a3e01083d3e771e1253c1356a0dd2beee4159fb9e74"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-mutative-1.2.0-MIT", "fileName": "./mutative/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "38dcabed7170eb9fa6aee2d7aae7206e2aa4a61e4c3071c0ff9d0bab6e90ac0b"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-napi-build-utils-2.0.0-MIT", "fileName": "./napi-build-utils/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "001ba5640df99785e76b7d407ae35a24c490f145737cf3f194408fb17dfec411"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-neverthrow-7.2.0-MIT", "fileName": "./neverthrow/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "c2c80ef5b8bda386bb47afb6969e24e6ccfb1e6e03ec2c17f2da3b6e9ddb2ebb"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-node-abi-3.75.0-MIT", "fileName": "./node-abi/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "eff58348ce588f75caffde0f6b0e1c186ea67e62994de7d8280b1efa5b5672b8"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-node-addon-api-7.1.1-MIT", "fileName": "./node-addon-api/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "e4947cf10e432c42459e60a00947ccd1ad2501e16cf1a530b3d39f0c2de5c83f"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-node-fetch-3.3.2-MIT", "fileName": "./node-fetch/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "058acbc2474901ffa0b95c3f9d0903111426b118b55f928300e448d7252fc27d"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-once-1.4.0-ISC", "fileName": "./once/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "33840d74c14c94bfb75c76374765b635531b1eacb88d7f1f2f380c94d0ea1328"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-picocolors-1.1.1-ISC", "fileName": "./picocolors/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "ef2ac226c4811d312e12c64214c453878653e834482125ae475f27cea60de737"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-picomatch-2.3.1-MIT", "fileName": "./picomatch/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "39b4c548570b67f5a07fb596e2e102ad8b373f83a86d761d13df6d911f9a784c"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-prebuild-install-7.1.3-MIT", "fileName": "./prebuild-install/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "d8502f5ce977ea10cc7e91e95b595ce845e838cf412801230e9a2f211ad68039"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-promise-limit-2.7.0-ISC", "fileName": "./promise-limit/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "dc438c2d05deb0ba521b02cdfe4c7b840c94d11134353357d772dbaad53ff0a9"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-pump-3.0.3-MIT", "fileName": "./pump/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "adfb35f1acc31a99032086d259599397140da825f34924d31563b92eebdc3e33"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-queueueue-4.1.2-MIT", "fileName": "./queueueue/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "6fbb0302ed7651ff8c325ecfad98309d23e34de6a18617bb99f512a2a2de6545"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-rc-1.2.8-BSD-2-<PERSON><PERSON>", "fileName": "./rc/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "96f035349a8e6034d380348ec3d454ed8ea3a602c7a5700eb36ff48310bdbe9f"}], "fileTypes": ["OTHER"], "licenseConcluded": "BSD-2-<PERSON><PERSON>", "licenseInfoInFiles": ["BSD-2-<PERSON><PERSON>"], "noticeText": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)"}, {"SPDXID": "SPDXRef-react-19.1.0-MIT", "fileName": "./react/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "8f3eb41d5562d73fd0b367319a8b7f03b36740f2dbd58214581307e0b5aa7a04"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-readable-stream-3.6.2-MIT", "fileName": "./readable-stream/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "469ea81f64037d1f179bcd46412217903a2924dd2e7d7d9b728659b6f12c3e69"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-safe-buffer-5.2.1-MIT", "fileName": "./safe-buffer/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "e4018fdbfe625433371f48274fee26f520bbe865e0d8c85bf3aa07424134b90c"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-semver-7.7.2-ISC", "fileName": "./semver/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "bf2e091359d5870257cc8287a268e001bfb39abf19275f382276efe3c7785a4f"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-simple-concat-1.0.1-MIT", "fileName": "./simple-concat/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "d6ae2e35a0c64b4469f1e531a3244efd50be7c115ef42d1422e54d14d9359e99"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-simple-get-4.0.1-MIT", "fileName": "./simple-get/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "8337ba9774d6d8199a5861c6dfa57058f162bfb6210362c01db38f0a593f96d6"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-source-map-js-1.2.1-BSD-3-<PERSON><PERSON>", "fileName": "./source-map-js/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "58f8e94a461a5e461e0cb2bb2be681cbd89b9181989fd26fceb858e67711469a"}], "fileTypes": ["OTHER"], "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseInfoInFiles": ["BSD-3-<PERSON><PERSON>"], "noticeText": "BSD-3-<PERSON><PERSON>"}, {"SPDXID": "SPDXRef-strip-json-comments-2.0.1-MIT", "fileName": "./strip-json-comments/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "52fba89a1d2724dfeafa21ec58f8c4a8a210103cc6b65b4468ab3fb973ad1753"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tailwindcss-4.1.11-MIT", "fileName": "./tailwindcss/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "92c4ad3c8696ea4a22392cd2e5cdcee059be7ecb488617f7e802919a12fca679"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tar-fs-2.1.3-MIT", "fileName": "./tar-fs/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "2a7dbf07ccca955f0a9537016ed5c51dbef52fdffa2d2370dc8ec4e58bed9143"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tar-stream-2.2.0-MIT", "fileName": "./tar-stream/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "bf26c92d0b2d3b865681bac5cf3bca4cd405e33fcb47f45216899aaed4f81010"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tar-7.4.3-ISC", "fileName": "./tar/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "f2de5f4b3f05b8e441aae63ecf228125dc72175052099d918769edb3b8982f7c"}], "fileTypes": ["OTHER"], "licenseConcluded": "ISC", "licenseInfoInFiles": ["ISC"], "noticeText": "ISC"}, {"SPDXID": "SPDXRef-to-regex-range-5.0.1-MIT", "fileName": "./to-regex-range/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "766fc1bb34fffcd503c89f4194188ce059795c52fd0dc6753cd4b7dcfc3fe1ec"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-tslib-2.8.1-0BSD", "fileName": "./tslib/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "f02b4851df316f29bdfcd404aa1eb41e4e216b5859015f81fcd3e91caf8be931"}], "fileTypes": ["OTHER"], "licenseConcluded": "0BSD", "licenseInfoInFiles": ["0BSD"], "noticeText": "0BSD"}, {"SPDXID": "SPDXRef-tunnel-agent-0.6.0-Apache-2.0", "fileName": "./tunnel-agent/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "08a8560b650b49370af44148a8645c11675ba97d21ee7f347d8d434b13c51074"}], "fileTypes": ["OTHER"], "licenseConcluded": "Apache-2.0", "licenseInfoInFiles": ["Apache-2.0"], "noticeText": "Apache-2.0"}, {"SPDXID": "SPDXRef-undici-types-7.8.0-MIT", "fileName": "./undici-types/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "2d11a94d71962f63f234062ff20fd273e2ab7f66bdd931579e22c7e2c1cf5362"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-unicode-segmenter-0.12.0-MIT", "fileName": "./unicode-segmenter/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "aec2a232592afe0c3af4ef1ee1c19e035866c332e3dd401e608296ab0d9b45fe"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-uuid-11.1.0-MIT", "fileName": "./uuid/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "de14f7be721c673bd318656b3ff7cabfe79065c362e633976953c40665682dea"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-ws-8.18.3-MIT", "fileName": "./ws/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "aaedef2a72b60db8fb36d9b46c48d44986051785a2b6450c62994603c85dd959"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}, {"SPDXID": "SPDXRef-yallist-5.0.0-BlueOak-1.0.0", "fileName": "./yallist/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "1b9d47057ce39814531ff93f668823b4fa03e7d23945449c274a1ff6d4cc297f"}], "fileTypes": ["OTHER"], "licenseConcluded": "BlueOak-1.0.0", "licenseInfoInFiles": ["BlueOak-1.0.0"], "noticeText": "BlueOak-1.0.0"}, {"SPDXID": "SPDXRef-zod-3.25.28-MIT", "fileName": "./zod/package.json", "checksums": [{"algorithm": "SHA256", "checksumValue": "49621b6faa27c36dce2fad60b05a66185ce9283097ed49a26ff8332441a3ec7f"}], "fileTypes": ["OTHER"], "licenseConcluded": "MIT", "licenseInfoInFiles": ["MIT"], "noticeText": "MIT"}], "relationships": [{"spdxElementId": "SPDXRef-DOCUMENT", "relationshipType": "DESCRIBES", "relatedSpdxElement": "SPDXRef-project"}, {"spdxElementId": "SPDXRef-project", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-project-JS", "comment": "Module of the project"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@anthropic-ai-sdk-0.57.0"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@emnapi-runtime-1.4.5"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@hono-node-server-1.17.1"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@instantdb-react-0.20.12"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@libsql-client-0.15.10"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@starfederation-datastar-1.0.0-beta.11"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@tailwindcss-cli-4.1.11"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-better-sqlite3-12.2.0"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-dotenv-17.2.1"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-hono-4.8.8"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-jazz-tools-0.14.28"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-lucide-react-0.525.0"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-tailwindcss-4.1.11"}, {"spdxElementId": "SPDXRef-project-JS", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-tslib-2.8.1"}, {"spdxElementId": "SPDXRef-@emnapi-runtime-1.4.5", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-tslib-2.8.1"}, {"spdxElementId": "SPDXRef-@hono-node-server-1.17.1", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-hono-4.8.8"}, {"spdxElementId": "SPDXRef-@instantdb-react-0.20.12", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@instantdb-core-0.20.12"}, {"spdxElementId": "SPDXRef-@instantdb-react-0.20.12", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-react-19.1.0"}, {"spdxElementId": "SPDXRef-@libsql-client-0.15.10", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@libsql-core-0.15.10"}, {"spdxElementId": "SPDXRef-@libsql-client-0.15.10", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@libsql-hrana-client-0.7.0"}, {"spdxElementId": "SPDXRef-@libsql-client-0.15.10", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-js-base64-3.7.7"}, {"spdxElementId": "SPDXRef-@libsql-client-0.15.10", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-libsql-0.5.17"}, {"spdxElementId": "SPDXRef-@libsql-client-0.15.10", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-promise-limit-2.7.0"}, {"spdxElementId": "SPDXRef-@tailwindcss-cli-4.1.11", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@parcel-watcher-2.5.1"}, {"spdxElementId": "SPDXRef-@tailwindcss-cli-4.1.11", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@tailwindcss-node-4.1.11"}, {"spdxElementId": "SPDXRef-@tailwindcss-cli-4.1.11", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@tailwindcss-oxide-4.1.11"}, {"spdxElementId": "SPDXRef-@tailwindcss-cli-4.1.11", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-enhanced-resolve-5.18.2"}, {"spdxElementId": "SPDXRef-@tailwindcss-cli-4.1.11", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-mri-1.2.0"}, {"spdxElementId": "SPDXRef-@tailwindcss-cli-4.1.11", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-picocolors-1.1.1"}, {"spdxElementId": "SPDXRef-@tailwindcss-cli-4.1.11", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-tailwindcss-4.1.11"}, {"spdxElementId": "SPDXRef-better-sqlite3-12.2.0", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-bindings-1.5.0"}, {"spdxElementId": "SPDXRef-better-sqlite3-12.2.0", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-prebuild-install-7.1.3"}, {"spdxElementId": "SPDXRef-jazz-tools-0.14.28", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-@scure-bip39-1.6.0"}, {"spdxElementId": "SPDXRef-jazz-tools-0.14.28", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-<PERSON><PERSON>son-0.14.28"}, {"spdxElementId": "SPDXRef-jazz-tools-0.14.28", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-fast-myers-diff-3.2.0"}, {"spdxElementId": "SPDXRef-jazz-tools-0.14.28", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-zod-3.25.28"}, {"spdxElementId": "SPDXRef-lucide-react-0.525.0", "relationshipType": "DEPENDS_ON", "relatedSpdxElement": "SPDXRef-react-19.1.0"}, {"spdxElementId": "SPDXRef-@ampproject-remapping-2.3.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-ampproject-remapping-2.3.0-Apache-2.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@anthropic-ai-sdk-0.57.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-anthropic-ai-sdk-0.57.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@emnapi-runtime-1.4.5", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-emnapi-runtime-1.4.5-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@hono-node-server-1.17.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-hono-node-server-1.17.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@isaacs-fs-minipass-4.0.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-isaacs-fs-minipass-4.0.1-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@jridgewell-gen-mapping-0.3.12", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-jridgewell-gen-mapping-0.3.12-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@jridgewell-sourcemap-codec-1.5.4", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-j<PERSON>well-sourcemap-codec-1.5.4-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@jridgewell-trace-mapping-0.3.29", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-jridgewell-trace-mapping-0.3.29-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@libsql-client-0.15.10", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-libsql-client-0.15.10-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@libsql-core-0.15.10", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-libsql-core-0.15.10-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@libsql-hrana-client-0.7.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-libsql-hrana-client-0.7.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@libsql-isomorphic-fetch-0.3.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-libsql-isomorphic-fetch-0.3.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@libsql-isomorphic-ws-0.1.5", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-libsql-isomorphic-ws-0.1.5-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@libsql-linux-x64-gnu-0.5.17", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-libsql-linux-x64-gnu-0.5.17-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@libsql-linux-x64-musl-0.5.17", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-libsql-linux-x64-musl-0.5.17-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@neon-rs-load-0.0.4", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-neon-rs-load-0.0.4-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@noble-ciphers-1.3.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-noble-ciphers-1.3.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@noble-curves-1.9.4", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-noble-curves-1.9.4-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@noble-hashes-1.8.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-noble-hashes-1.8.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@opentelemetry-api-1.9.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-opentelemetry-api-1.9.0-Apache-2.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@parcel-watcher-linux-x64-glibc-2.5.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-parcel-watcher-linux-x64-glibc-2.5.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@parcel-watcher-linux-x64-musl-2.5.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-parcel-watcher-linux-x64-musl-2.5.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@parcel-watcher-2.5.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-parcel-watcher-2.5.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@scure-base-1.2.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-scure-base-1.2.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@scure-base-1.2.6", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-scure-base-1.2.6-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@scure-bip39-1.6.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-scure-bip39-1.6.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@starfederation-datastar-1.0.0-beta.11", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-starfederation-datastar-1.0.0-beta.11-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@tailwindcss-cli-4.1.11", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tailwindcss-cli-4.1.11-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@tailwindcss-node-4.1.11", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tailwindcss-node-4.1.11-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@tailwindcss-oxide-linux-x64-gnu-4.1.11", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tailwindcss-oxide-linux-x64-gnu-4.1.11-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@tailwindcss-oxide-linux-x64-musl-4.1.11", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tailwindcss-oxide-linux-x64-musl-4.1.11-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@tailwindcss-oxide-4.1.11", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tailwindcss-oxide-4.1.11-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@types-node-24.1.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-types-node-24.1.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-@types-ws-8.18.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-types-ws-8.18.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-base64-js-1.5.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-base64-js-1.5.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-better-sqlite3-12.2.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-better-sqlite3-12.2.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-bindings-1.5.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-bindings-1.5.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-bl-4.1.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-bl-4.1.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-braces-3.0.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-braces-3.0.3-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-buffer-5.7.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-buffer-5.7.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-chownr-1.1.4", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-chownr-1.1.4-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-chownr-3.0.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-chownr-3.0.0-BlueOak-1.0.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-<PERSON><PERSON>son-0.14.28", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-<PERSON><PERSON><PERSON>-0.14.28-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-data-uri-to-buffer-4.0.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-data-uri-to-buffer-4.0.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-decompress-response-6.0.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-decompress-response-6.0.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-deep-extend-0.6.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-deep-extend-0.6.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-detect-libc-1.0.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-detect-libc-1.0.3-Apache-2.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-detect-libc-2.0.2", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-detect-libc-2.0.2-Apache-2.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-detect-libc-2.0.4", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-detect-libc-2.0.4-Apache-2.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-dotenv-17.2.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-dotenv-17.2.1-BSD-2-<PERSON><PERSON>", "comment": "License mention"}, {"spdxElementId": "SPDXRef-end-of-stream-1.4.5", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-end-of-stream-1.4.5-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-enhanced-resolve-5.18.2", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-enhanced-resolve-5.18.2-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-expand-template-2.0.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-expand-template-2.0.3-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-fast-list-1.0.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-fast-list-1.0.3-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-fast-myers-diff-3.2.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-fast-myers-diff-3.2.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-fetch-blob-3.2.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-fetch-blob-3.2.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-file-uri-to-path-1.0.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-uri-to-path-1.0.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-fill-range-7.1.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-fill-range-7.1.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-formdata-polyfill-4.0.10", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-formdata-polyfill-4.0.10-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-fs-constants-1.0.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-fs-constants-1.0.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-github-from-package-0.0.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-github-from-package-0.0.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-hono-4.8.8", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-hono-4.8.8-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-ieee754-1.2.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-ieee754-1.2.1-BSD-3-<PERSON><PERSON>", "comment": "License mention"}, {"spdxElementId": "SPDXRef-inherits-2.0.4", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-inherits-2.0.4-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-ini-1.3.8", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-ini-1.3.8-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-is-extglob-2.1.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-is-extglob-2.1.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-is-glob-4.0.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-is-glob-4.0.3-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-is-number-7.0.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-is-number-7.0.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-jazz-crypto-rs-0.0.7", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-jazz-crypto-rs-0.0.7-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-jazz-tools-0.14.28", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-jazz-tools-0.14.28-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-jiti-2.5.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-jiti-2.5.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-js-base64-3.7.7", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-js-base64-3.7.7-BSD-3-<PERSON><PERSON>", "comment": "License mention"}, {"spdxElementId": "SPDXRef-libsql-0.5.17", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-libsql-0.5.17-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-lightningcss-linux-x64-gnu-1.30.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-lightningcss-linux-x64-gnu-1.30.1-MPL-2.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-lightningcss-linux-x64-musl-1.30.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-lightningcss-linux-x64-musl-1.30.1-MPL-2.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-lightningcss-1.30.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-lightningcss-1.30.1-MPL-2.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-lucide-react-0.525.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-lucide-react-0.525.0-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-magic-string-0.30.17", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-magic-string-0.30.17-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-micromatch-4.0.8", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-micromatch-4.0.8-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-mimic-response-3.1.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-mimic-response-3.1.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-minimist-1.2.8", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-minimist-1.2.8-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-minipass-7.1.2", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-minipass-7.1.2-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-minizlib-3.0.2", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-minizlib-3.0.2-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-mkdirp-classic-0.5.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-mkdirp-classic-0.5.3-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-mkdirp-3.0.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-mkdirp-3.0.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-mri-1.2.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-mri-1.2.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-mutative-1.2.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-mutative-1.2.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-napi-build-utils-2.0.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-napi-build-utils-2.0.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-neverthrow-7.2.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-neverthrow-7.2.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-node-abi-3.75.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-node-abi-3.75.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-node-addon-api-7.1.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-node-addon-api-7.1.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-node-fetch-3.3.2", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-node-fetch-3.3.2-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-once-1.4.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-once-1.4.0-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-picocolors-1.1.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-picocolors-1.1.1-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-picomatch-2.3.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-picomatch-2.3.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-prebuild-install-7.1.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-prebuild-install-7.1.3-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-promise-limit-2.7.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-promise-limit-2.7.0-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-pump-3.0.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-pump-3.0.3-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-queueueue-4.1.2", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-queueueue-4.1.2-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-rc-1.2.8", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-rc-1.2.8-BSD-2-<PERSON><PERSON>", "comment": "License mention"}, {"spdxElementId": "SPDXRef-react-19.1.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-react-19.1.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-readable-stream-3.6.2", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-readable-stream-3.6.2-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-safe-buffer-5.2.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-safe-buffer-5.2.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-semver-7.7.2", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-semver-7.7.2-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-simple-concat-1.0.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-simple-concat-1.0.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-simple-get-4.0.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-simple-get-4.0.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-source-map-js-1.2.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-source-map-js-1.2.1-BSD-3-<PERSON><PERSON>", "comment": "License mention"}, {"spdxElementId": "SPDXRef-strip-json-comments-2.0.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-strip-json-comments-2.0.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-tailwindcss-4.1.11", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tailwindcss-4.1.11-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-tar-fs-2.1.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tar-fs-2.1.3-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-tar-stream-2.2.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tar-stream-2.2.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-tar-7.4.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tar-7.4.3-ISC", "comment": "License mention"}, {"spdxElementId": "SPDXRef-to-regex-range-5.0.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-to-regex-range-5.0.1-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-tslib-2.8.1", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tslib-2.8.1-0BSD", "comment": "License mention"}, {"spdxElementId": "SPDXRef-tunnel-agent-0.6.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-tunnel-agent-0.6.0-Apache-2.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-undici-types-7.8.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-undici-types-7.8.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-unicode-segmenter-0.12.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-unicode-segmenter-0.12.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-uuid-11.1.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-uuid-11.1.0-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-ws-8.18.3", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-ws-8.18.3-MIT", "comment": "License mention"}, {"spdxElementId": "SPDXRef-yallist-5.0.0", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-yallist-5.0.0-BlueOak-1.0.0", "comment": "License mention"}, {"spdxElementId": "SPDXRef-zod-3.25.28", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-zod-3.25.28-MIT", "comment": "License mention"}], "hasExtractedLicensingInfos": [{"licenseId": "LicenseRef-PROPRIETARY-LICENSE", "extractedText": "<PERSON><PERSON><PERSON> didn't find a license in your project, that's why the project license is considered to be proprietary. Proprietary license rules are applied for dependency license compatibility checks."}]}