# Third-party software list

This page lists the third-party software dependencies used in project

| Dependency                                                                                           | Version       | Licenses                                                         |
|------------------------------------------------------------------------------------------------------|---------------|------------------------------------------------------------------|
| [@ampproject/remapping](https://www.npmjs.com/package/@ampproject/remapping)                         | 2.3.0         | [Apache-2.0](http://www.apache.org/licenses/)                    |
| [@anthropic-ai/sdk](https://www.npmjs.com/package/@anthropic-ai/sdk)                                 | 0.57.0        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@emnapi/runtime](https://www.npmjs.com/package/@emnapi/runtime)                                     | 1.4.5         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@hono/node-server](https://www.npmjs.com/package/@hono/node-server)                                 | 1.17.1        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@isaacs/fs-minipass](https://www.npmjs.com/package/@isaacs/fs-minipass)                             | 4.0.1         | [ISC](https://www.isc.org/licenses/)                             |
| [@jridgewell/gen-mapping](https://www.npmjs.com/package/@jridgewell/gen-mapping)                     | 0.3.12        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@jridgewell/sourcemap-codec](https://www.npmjs.com/package/@jridgewell/sourcemap-codec)             | 1.5.4         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@jridgewell/trace-mapping](https://www.npmjs.com/package/@jridgewell/trace-mapping)                 | 0.3.29        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@libsql/client](https://www.npmjs.com/package/@libsql/client)                                       | 0.15.10       | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@libsql/core](https://www.npmjs.com/package/@libsql/core)                                           | 0.15.10       | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@libsql/hrana-client](https://www.npmjs.com/package/@libsql/hrana-client)                           | 0.7.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@libsql/isomorphic-fetch](https://www.npmjs.com/package/@libsql/isomorphic-fetch)                   | 0.3.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@libsql/isomorphic-ws](https://www.npmjs.com/package/@libsql/isomorphic-ws)                         | 0.1.5         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@libsql/linux-x64-gnu](https://www.npmjs.com/package/@libsql/linux-x64-gnu)                         | 0.5.17        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@libsql/linux-x64-musl](https://www.npmjs.com/package/@libsql/linux-x64-musl)                       | 0.5.17        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@neon-rs/load](https://www.npmjs.com/package/@neon-rs/load)                                         | 0.0.4         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@noble/ciphers](https://www.npmjs.com/package/@noble/ciphers)                                       | 1.3.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@noble/curves](https://www.npmjs.com/package/@noble/curves)                                         | 1.9.4         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@noble/hashes](https://www.npmjs.com/package/@noble/hashes)                                         | 1.8.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@opentelemetry/api](https://www.npmjs.com/package/@opentelemetry/api)                               | 1.9.0         | [Apache-2.0](http://www.apache.org/licenses/)                    |
| [@parcel/watcher-linux-x64-glibc](https://www.npmjs.com/package/@parcel/watcher-linux-x64-glibc)     | 2.5.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@parcel/watcher-linux-x64-musl](https://www.npmjs.com/package/@parcel/watcher-linux-x64-musl)       | 2.5.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@parcel/watcher](https://www.npmjs.com/package/@parcel/watcher)                                     | 2.5.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@scure/base](https://www.npmjs.com/package/@scure/base)                                             | 1.2.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@scure/base](https://www.npmjs.com/package/@scure/base)                                             | 1.2.6         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@scure/bip39](https://www.npmjs.com/package/@scure/bip39)                                           | 1.6.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@starfederation/datastar](https://www.npmjs.com/package/@starfederation/datastar)                   | 1.0.0-beta.11 | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@tailwindcss/cli](https://www.npmjs.com/package/@tailwindcss/cli)                                   | 4.1.11        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@tailwindcss/node](https://www.npmjs.com/package/@tailwindcss/node)                                 | 4.1.11        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@tailwindcss/oxide-linux-x64-gnu](https://www.npmjs.com/package/@tailwindcss/oxide-linux-x64-gnu)   | 4.1.11        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@tailwindcss/oxide-linux-x64-musl](https://www.npmjs.com/package/@tailwindcss/oxide-linux-x64-musl) | 4.1.11        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@tailwindcss/oxide](https://www.npmjs.com/package/@tailwindcss/oxide)                               | 4.1.11        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@types/node](https://www.npmjs.com/package/@types/node)                                             | 24.1.0        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [@types/ws](https://www.npmjs.com/package/@types/ws)                                                 | 8.18.1        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [base64-js](https://www.npmjs.com/package/base64-js)                                                 | 1.5.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [better-sqlite3](https://www.npmjs.com/package/better-sqlite3)                                       | 12.2.0        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [bindings](https://www.npmjs.com/package/bindings)                                                   | 1.5.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [bl](https://www.npmjs.com/package/bl)                                                               | 4.1.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [braces](https://www.npmjs.com/package/braces)                                                       | 3.0.3         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [buffer](https://www.npmjs.com/package/buffer)                                                       | 5.7.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [chownr](https://www.npmjs.com/package/chownr)                                                       | 1.1.4         | [ISC](https://www.isc.org/licenses/)                             |
| [chownr](https://www.npmjs.com/package/chownr)                                                       | 3.0.0         | [BlueOak-1.0.0](https://blueoakcouncil.org/license/1.0.0)        |
| [cojson](https://www.npmjs.com/package/cojson)                                                       | 0.14.28       | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [data-uri-to-buffer](https://www.npmjs.com/package/data-uri-to-buffer)                               | 4.0.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [decompress-response](https://www.npmjs.com/package/decompress-response)                             | 6.0.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [deep-extend](https://www.npmjs.com/package/deep-extend)                                             | 0.6.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [detect-libc](https://www.npmjs.com/package/detect-libc)                                             | 1.0.3         | [Apache-2.0](http://www.apache.org/licenses/)                    |
| [detect-libc](https://www.npmjs.com/package/detect-libc)                                             | 2.0.2         | [Apache-2.0](http://www.apache.org/licenses/)                    |
| [detect-libc](https://www.npmjs.com/package/detect-libc)                                             | 2.0.4         | [Apache-2.0](http://www.apache.org/licenses/)                    |
| [dotenv](https://www.npmjs.com/package/dotenv)                                                       | 17.2.1        | [BSD-2-Clause](http://www.opensource.org/licenses/BSD-2-Clause)  |
| [end-of-stream](https://www.npmjs.com/package/end-of-stream)                                         | 1.4.5         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [enhanced-resolve](https://www.npmjs.com/package/enhanced-resolve)                                   | 5.18.2        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [expand-template](https://www.npmjs.com/package/expand-template)                                     | 2.0.3         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [fast-list](https://www.npmjs.com/package/fast-list)                                                 | 1.0.3         | [ISC](https://www.isc.org/licenses/)                             |
| [fast-myers-diff](https://www.npmjs.com/package/fast-myers-diff)                                     | 3.2.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [fetch-blob](https://www.npmjs.com/package/fetch-blob)                                               | 3.2.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [file-uri-to-path](https://www.npmjs.com/package/file-uri-to-path)                                   | 1.0.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [fill-range](https://www.npmjs.com/package/fill-range)                                               | 7.1.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [formdata-polyfill](https://www.npmjs.com/package/formdata-polyfill)                                 | 4.0.10        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [fs-constants](https://www.npmjs.com/package/fs-constants)                                           | 1.0.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [github-from-package](https://www.npmjs.com/package/github-from-package)                             | 0.0.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [hono](https://www.npmjs.com/package/hono)                                                           | 4.8.8         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [ieee754](https://www.npmjs.com/package/ieee754)                                                     | 1.2.1         | [BSD-3-Clause](http://www.opensource.org/licenses/BSD-3-Clause)  |
| [inherits](https://www.npmjs.com/package/inherits)                                                   | 2.0.4         | [ISC](https://www.isc.org/licenses/)                             |
| [ini](https://www.npmjs.com/package/ini)                                                             | 1.3.8         | [ISC](https://www.isc.org/licenses/)                             |
| [is-extglob](https://www.npmjs.com/package/is-extglob)                                               | 2.1.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [is-glob](https://www.npmjs.com/package/is-glob)                                                     | 4.0.3         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [is-number](https://www.npmjs.com/package/is-number)                                                 | 7.0.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [jazz-crypto-rs](https://www.npmjs.com/package/jazz-crypto-rs)                                       | 0.0.7         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [jazz-tools](https://www.npmjs.com/package/jazz-tools)                                               | 0.14.28       | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [jiti](https://www.npmjs.com/package/jiti)                                                           | 2.5.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [js-base64](https://www.npmjs.com/package/js-base64)                                                 | 3.7.7         | [BSD-3-Clause](http://www.opensource.org/licenses/BSD-3-Clause)  |
| [libsql](https://www.npmjs.com/package/libsql)                                                       | 0.5.17        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [lightningcss-linux-x64-gnu](https://www.npmjs.com/package/lightningcss-linux-x64-gnu)               | 1.30.1        | [MPL-2.0](http://mpl.mozilla.org/2012/01/03/announcing-mpl-2-0/) |
| [lightningcss-linux-x64-musl](https://www.npmjs.com/package/lightningcss-linux-x64-musl)             | 1.30.1        | [MPL-2.0](http://mpl.mozilla.org/2012/01/03/announcing-mpl-2-0/) |
| [lightningcss](https://www.npmjs.com/package/lightningcss)                                           | 1.30.1        | [MPL-2.0](http://mpl.mozilla.org/2012/01/03/announcing-mpl-2-0/) |
| [lucide-react](https://www.npmjs.com/package/lucide-react)                                           | 0.525.0       | [ISC](https://www.isc.org/licenses/)                             |
| [magic-string](https://www.npmjs.com/package/magic-string)                                           | 0.30.17       | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [micromatch](https://www.npmjs.com/package/micromatch)                                               | 4.0.8         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [mimic-response](https://www.npmjs.com/package/mimic-response)                                       | 3.1.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [minimist](https://www.npmjs.com/package/minimist)                                                   | 1.2.8         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [minipass](https://www.npmjs.com/package/minipass)                                                   | 7.1.2         | [ISC](https://www.isc.org/licenses/)                             |
| [minizlib](https://www.npmjs.com/package/minizlib)                                                   | 3.0.2         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [mkdirp-classic](https://www.npmjs.com/package/mkdirp-classic)                                       | 0.5.3         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [mkdirp](https://www.npmjs.com/package/mkdirp)                                                       | 3.0.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [mri](https://www.npmjs.com/package/mri)                                                             | 1.2.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [mutative](https://www.npmjs.com/package/mutative)                                                   | 1.2.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [napi-build-utils](https://www.npmjs.com/package/napi-build-utils)                                   | 2.0.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [neverthrow](https://www.npmjs.com/package/neverthrow)                                               | 7.2.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [node-abi](https://www.npmjs.com/package/node-abi)                                                   | 3.75.0        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [node-addon-api](https://www.npmjs.com/package/node-addon-api)                                       | 7.1.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [node-fetch](https://www.npmjs.com/package/node-fetch)                                               | 3.3.2         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [once](https://www.npmjs.com/package/once)                                                           | 1.4.0         | [ISC](https://www.isc.org/licenses/)                             |
| [picocolors](https://www.npmjs.com/package/picocolors)                                               | 1.1.1         | [ISC](https://www.isc.org/licenses/)                             |
| [picomatch](https://www.npmjs.com/package/picomatch)                                                 | 2.3.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [prebuild-install](https://www.npmjs.com/package/prebuild-install)                                   | 7.1.3         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [promise-limit](https://www.npmjs.com/package/promise-limit)                                         | 2.7.0         | [ISC](https://www.isc.org/licenses/)                             |
| [pump](https://www.npmjs.com/package/pump)                                                           | 3.0.3         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [queueueue](https://www.npmjs.com/package/queueueue)                                                 | 4.1.2         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [rc](https://www.npmjs.com/package/rc)                                                               | 1.2.8         | [BSD-2-Clause](http://www.opensource.org/licenses/BSD-2-Clause)  |
| [react](https://www.npmjs.com/package/react)                                                         | 19.1.0        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [readable-stream](https://www.npmjs.com/package/readable-stream)                                     | 3.6.2         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [safe-buffer](https://www.npmjs.com/package/safe-buffer)                                             | 5.2.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [semver](https://www.npmjs.com/package/semver)                                                       | 7.7.2         | [ISC](https://www.isc.org/licenses/)                             |
| [simple-concat](https://www.npmjs.com/package/simple-concat)                                         | 1.0.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [simple-get](https://www.npmjs.com/package/simple-get)                                               | 4.0.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [source-map-js](https://www.npmjs.com/package/source-map-js)                                         | 1.2.1         | [BSD-3-Clause](http://www.opensource.org/licenses/BSD-3-Clause)  |
| [strip-json-comments](https://www.npmjs.com/package/strip-json-comments)                             | 2.0.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [tailwindcss](https://www.npmjs.com/package/tailwindcss)                                             | 4.1.11        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [tar-fs](https://www.npmjs.com/package/tar-fs)                                                       | 2.1.3         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [tar-stream](https://www.npmjs.com/package/tar-stream)                                               | 2.2.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [tar](https://www.npmjs.com/package/tar)                                                             | 7.4.3         | [ISC](https://www.isc.org/licenses/)                             |
| [to-regex-range](https://www.npmjs.com/package/to-regex-range)                                       | 5.0.1         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [tslib](https://www.npmjs.com/package/tslib)                                                         | 2.8.1         | [0BSD](http://landley.net/toybox/license.html)                   |
| [tunnel-agent](https://www.npmjs.com/package/tunnel-agent)                                           | 0.6.0         | [Apache-2.0](http://www.apache.org/licenses/)                    |
| [undici-types](https://www.npmjs.com/package/undici-types)                                           | 7.8.0         | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [unicode-segmenter](https://www.npmjs.com/package/unicode-segmenter)                                 | 0.12.0        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [uuid](https://www.npmjs.com/package/uuid)                                                           | 11.1.0        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [ws](https://www.npmjs.com/package/ws)                                                               | 8.18.3        | [MIT](http://opensource.org/licenses/mit-license.php)            |
| [yallist](https://www.npmjs.com/package/yallist)                                                     | 5.0.0         | [BlueOak-1.0.0](https://blueoakcouncil.org/license/1.0.0)        |
| [zod](https://www.npmjs.com/package/zod)                                                             | 3.25.28       | [MIT](http://opensource.org/licenses/mit-license.php)            |