{"version": "2.0.0", "name": "project", "path": "/data/project", "projectLicenses": [{"key": "PROPRIETARY-LICENSE", "rules": [], "tags": []}], "modules": [{"name": "project:JS", "path": "/data/project", "licenses": [{"key": "PROPRIETARY-LICENSE", "rules": [], "tags": []}], "dependencies": [{"id": "@anthropic-ai/sdk", "name": "@anthropic-ai/sdk", "version": "0.57.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@anthropic-ai/sdk"], "homepageUrl": "https://www.npmjs.com/package/@anthropic-ai/sdk", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@anthropic-ai/sdk/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@emnapi/runtime", "name": "@emnapi/runtime", "version": "1.4.5", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@emnapi/runtime"], "homepageUrl": "https://www.npmjs.com/package/@emnapi/runtime", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@emnapi/runtime/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "tslib", "name": "tslib", "version": "2.8.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tslib"], "homepageUrl": "https://www.npmjs.com/package/tslib", "licenses": [{"key": "0BSD", "path": "/data/project/node_modules/tslib/package.json", "rules": ["0BSD"], "tags": []}], "dependencies": []}]}, {"id": "@hono/node-server", "name": "@hono/node-server", "version": "1.17.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@hono/node-server"], "homepageUrl": "https://www.npmjs.com/package/@hono/node-server", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@hono/node-server/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "hono", "name": "hono", "version": "4.8.8", "module": "project:JS", "type": "npm", "paths": ["./node_modules/hono"], "homepageUrl": "https://www.npmjs.com/package/hono", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/hono/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "@instantdb/react", "name": "@instantdb/react", "version": "0.20.12", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@instantdb/react"], "homepageUrl": "https://www.npmjs.com/package/@instantdb/react", "licenses": [{"key": "NONE", "rules": [], "tags": [{"type": "Unknown", "comment": ""}]}], "dependencies": [{"id": "@instantdb/core", "name": "@instantdb/core", "version": "0.20.12", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@instantdb/core"], "homepageUrl": "https://www.npmjs.com/package/@instantdb/core", "licenses": [{"key": "NONE", "rules": [], "tags": [{"type": "Unknown", "comment": ""}]}], "dependencies": [{"id": "mutative", "name": "mutative", "version": "1.2.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/mutative"], "homepageUrl": "https://www.npmjs.com/package/mutative", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/mutative/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "uuid", "name": "uuid", "version": "11.1.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/uuid"], "homepageUrl": "https://www.npmjs.com/package/uuid", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/uuid/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "react", "name": "react", "version": "19.1.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/react"], "homepageUrl": "https://www.npmjs.com/package/react", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/react/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "@libsql/client", "name": "@libsql/client", "version": "0.15.10", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@libsql/client"], "homepageUrl": "https://www.npmjs.com/package/@libsql/client", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@libsql/client/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@libsql/core", "name": "@libsql/core", "version": "0.15.10", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@libsql/core"], "homepageUrl": "https://www.npmjs.com/package/@libsql/core", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@libsql/core/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "js-base64", "name": "js-base64", "version": "3.7.7", "module": "project:JS", "type": "npm", "paths": ["./node_modules/js-base64"], "homepageUrl": "https://www.npmjs.com/package/js-base64", "licenses": [{"key": "BSD-3-<PERSON><PERSON>", "path": "/data/project/node_modules/js-base64/package.json", "rules": ["BSD-3-<PERSON><PERSON>"], "tags": []}], "dependencies": []}]}, {"id": "@libsql/hrana-client", "name": "@libsql/hrana-client", "version": "0.7.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@libsql/hrana-client"], "homepageUrl": "https://www.npmjs.com/package/@libsql/hrana-client", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@libsql/hrana-client/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@libsql/isomorphic-fetch", "name": "@libsql/isomorphic-fetch", "version": "0.3.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@libsql/isomorphic-fetch"], "homepageUrl": "https://www.npmjs.com/package/@libsql/isomorphic-fetch", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@libsql/isomorphic-fetch/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@libsql/isomorphic-ws", "name": "@libsql/isomorphic-ws", "version": "0.1.5", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@libsql/isomorphic-ws"], "homepageUrl": "https://www.npmjs.com/package/@libsql/isomorphic-ws", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@libsql/isomorphic-ws/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@types/ws", "name": "@types/ws", "version": "8.18.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@types/ws"], "homepageUrl": "https://www.npmjs.com/package/@types/ws", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@types/ws/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@types/node", "name": "@types/node", "version": "24.1.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@types/node"], "homepageUrl": "https://www.npmjs.com/package/@types/node", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@types/node/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "undici-types", "name": "undici-types", "version": "7.8.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/undici-types"], "homepageUrl": "https://www.npmjs.com/package/undici-types", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/undici-types/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}]}, {"id": "ws", "name": "ws", "version": "8.18.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/ws"], "homepageUrl": "https://www.npmjs.com/package/ws", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/ws/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "js-base64", "name": "js-base64", "version": "3.7.7", "module": "project:JS", "type": "npm", "paths": ["./node_modules/js-base64"], "homepageUrl": "https://www.npmjs.com/package/js-base64", "licenses": [{"key": "BSD-3-<PERSON><PERSON>", "path": "/data/project/node_modules/js-base64/package.json", "rules": ["BSD-3-<PERSON><PERSON>"], "tags": []}], "dependencies": []}, {"id": "node-fetch", "name": "node-fetch", "version": "3.3.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/node-fetch"], "homepageUrl": "https://www.npmjs.com/package/node-fetch", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/node-fetch/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "data-uri-to-buffer", "name": "data-uri-to-buffer", "version": "4.0.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/data-uri-to-buffer"], "homepageUrl": "https://www.npmjs.com/package/data-uri-to-buffer", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/data-uri-to-buffer/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "fetch-blob", "name": "fetch-blob", "version": "3.2.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/fetch-blob"], "homepageUrl": "https://www.npmjs.com/package/fetch-blob", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/fetch-blob/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "node-domexception", "name": "node-domexception", "version": "1.0.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/node-domexception"], "homepageUrl": "https://www.npmjs.com/package/node-domexception", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/node-domexception/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "web-streams-polyfill", "name": "web-streams-polyfill", "version": "3.3.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/web-streams-polyfill"], "homepageUrl": "https://www.npmjs.com/package/web-streams-polyfill", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/web-streams-polyfill/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "formdata-polyfill", "name": "formdata-polyfill", "version": "4.0.10", "module": "project:JS", "type": "npm", "paths": ["./node_modules/formdata-polyfill"], "homepageUrl": "https://www.npmjs.com/package/formdata-polyfill", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/formdata-polyfill/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "fetch-blob", "name": "fetch-blob", "version": "3.2.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/fetch-blob"], "homepageUrl": "https://www.npmjs.com/package/fetch-blob", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/fetch-blob/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}]}]}, {"id": "js-base64", "name": "js-base64", "version": "3.7.7", "module": "project:JS", "type": "npm", "paths": ["./node_modules/js-base64"], "homepageUrl": "https://www.npmjs.com/package/js-base64", "licenses": [{"key": "BSD-3-<PERSON><PERSON>", "path": "/data/project/node_modules/js-base64/package.json", "rules": ["BSD-3-<PERSON><PERSON>"], "tags": []}], "dependencies": []}, {"id": "libsql", "name": "libsql", "version": "0.5.17", "module": "project:JS", "type": "npm", "paths": ["./node_modules/libsql"], "homepageUrl": "https://www.npmjs.com/package/libsql", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/libsql/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@libsql/linux-x64-gnu", "name": "@libsql/linux-x64-gnu", "version": "0.5.17", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@libsql/linux-x64-gnu"], "homepageUrl": "https://www.npmjs.com/package/@libsql/linux-x64-gnu", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@libsql/linux-x64-gnu/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@libsql/linux-x64-musl", "name": "@libsql/linux-x64-musl", "version": "0.5.17", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@libsql/linux-x64-musl"], "homepageUrl": "https://www.npmjs.com/package/@libsql/linux-x64-musl", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@libsql/linux-x64-musl/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@neon-rs/load", "name": "@neon-rs/load", "version": "0.0.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@neon-rs/load"], "homepageUrl": "https://www.npmjs.com/package/@neon-rs/load", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@neon-rs/load/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "detect-libc", "name": "detect-libc", "version": "2.0.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/libsql/node_modules/detect-libc"], "homepageUrl": "https://www.npmjs.com/package/detect-libc", "licenses": [{"key": "Apache-2.0", "path": "/data/project/node_modules/libsql/node_modules/detect-libc/package.json", "rules": ["Apache-2.0"], "tags": []}], "dependencies": []}]}, {"id": "promise-limit", "name": "promise-limit", "version": "2.7.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/promise-limit"], "homepageUrl": "https://www.npmjs.com/package/promise-limit", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/promise-limit/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}]}, {"id": "@starfederation/datastar", "name": "@starfederation/datastar", "version": "1.0.0-beta.11", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@starfederation/datastar"], "homepageUrl": "https://www.npmjs.com/package/@starfederation/datastar", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@starfederation/datastar/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@tailwindcss/cli", "name": "@tailwindcss/cli", "version": "4.1.11", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@tailwindcss/cli"], "homepageUrl": "https://www.npmjs.com/package/@tailwindcss/cli", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@tailwindcss/cli/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@parcel/watcher", "name": "@parcel/watcher", "version": "2.5.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@parcel/watcher"], "homepageUrl": "https://www.npmjs.com/package/@parcel/watcher", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@parcel/watcher/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@parcel/watcher-linux-x64-glibc", "name": "@parcel/watcher-linux-x64-glibc", "version": "2.5.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@parcel/watcher-linux-x64-glibc"], "homepageUrl": "https://www.npmjs.com/package/@parcel/watcher-linux-x64-glibc", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@parcel/watcher-linux-x64-glibc/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@parcel/watcher-linux-x64-musl", "name": "@parcel/watcher-linux-x64-musl", "version": "2.5.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@parcel/watcher-linux-x64-musl"], "homepageUrl": "https://www.npmjs.com/package/@parcel/watcher-linux-x64-musl", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@parcel/watcher-linux-x64-musl/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "detect-libc", "name": "detect-libc", "version": "1.0.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/detect-libc"], "homepageUrl": "https://www.npmjs.com/package/detect-libc", "licenses": [{"key": "Apache-2.0", "path": "/data/project/node_modules/detect-libc/package.json", "rules": ["Apache-2.0"], "tags": []}], "dependencies": []}, {"id": "is-glob", "name": "is-glob", "version": "4.0.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/is-glob"], "homepageUrl": "https://www.npmjs.com/package/is-glob", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/is-glob/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "is-extglob", "name": "is-extglob", "version": "2.1.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/is-extglob"], "homepageUrl": "https://www.npmjs.com/package/is-extglob", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/is-extglob/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "micromatch", "name": "micromatch", "version": "4.0.8", "module": "project:JS", "type": "npm", "paths": ["./node_modules/micromatch"], "homepageUrl": "https://www.npmjs.com/package/micromatch", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/micromatch/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "braces", "name": "braces", "version": "3.0.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/braces"], "homepageUrl": "https://www.npmjs.com/package/braces", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/braces/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "fill-range", "name": "fill-range", "version": "7.1.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/fill-range"], "homepageUrl": "https://www.npmjs.com/package/fill-range", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/fill-range/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "to-regex-range", "name": "to-regex-range", "version": "5.0.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/to-regex-range"], "homepageUrl": "https://www.npmjs.com/package/to-regex-range", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/to-regex-range/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "is-number", "name": "is-number", "version": "7.0.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/is-number"], "homepageUrl": "https://www.npmjs.com/package/is-number", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/is-number/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}]}]}, {"id": "picomatch", "name": "picomatch", "version": "2.3.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/picomatch"], "homepageUrl": "https://www.npmjs.com/package/picomatch", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/picomatch/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "node-addon-api", "name": "node-addon-api", "version": "7.1.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/node-addon-api"], "homepageUrl": "https://www.npmjs.com/package/node-addon-api", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/node-addon-api/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "@tailwindcss/node", "name": "@tailwindcss/node", "version": "4.1.11", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@tailwindcss/node"], "homepageUrl": "https://www.npmjs.com/package/@tailwindcss/node", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@tailwindcss/node/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@ampproject/remapping", "name": "@ampproject/remapping", "version": "2.3.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@ampproject/remapping"], "homepageUrl": "https://www.npmjs.com/package/@ampproject/remapping", "licenses": [{"key": "Apache-2.0", "path": "/data/project/node_modules/@ampproject/remapping/package.json", "rules": ["Apache-2.0"], "tags": []}], "dependencies": [{"id": "@jridgewell/gen-mapping", "name": "@jridgewell/gen-mapping", "version": "0.3.12", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@jridgewell/gen-mapping"], "homepageUrl": "https://www.npmjs.com/package/@jridgewell/gen-mapping", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@jridgewell/gen-mapping/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@jridgewell/sourcemap-codec", "name": "@jridgewell/sourcemap-codec", "version": "1.5.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@jridgewell/sourcemap-codec"], "homepageUrl": "https://www.npmjs.com/package/@jridgewell/sourcemap-codec", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@jridgewell/sourcemap-codec/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@jridgewell/trace-mapping", "name": "@jridgewell/trace-mapping", "version": "0.3.29", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@jridgewell/trace-mapping"], "homepageUrl": "https://www.npmjs.com/package/@jridgewell/trace-mapping", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@jridgewell/trace-mapping/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "@jridgewell/trace-mapping", "name": "@jridgewell/trace-mapping", "version": "0.3.29", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@jridgewell/trace-mapping"], "homepageUrl": "https://www.npmjs.com/package/@jridgewell/trace-mapping", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@jridgewell/trace-mapping/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "enhanced-resolve", "name": "enhanced-resolve", "version": "5.18.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/enhanced-resolve"], "homepageUrl": "https://www.npmjs.com/package/enhanced-resolve", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/enhanced-resolve/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "jiti", "name": "jiti", "version": "2.5.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/jiti"], "homepageUrl": "https://www.npmjs.com/package/jiti", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/jiti/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "lightningcss", "name": "lightningcss", "version": "1.30.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/lightningcss"], "homepageUrl": "https://www.npmjs.com/package/lightningcss", "licenses": [{"key": "MPL-2.0", "path": "/data/project/node_modules/lightningcss/package.json", "rules": ["MPL-2.0"], "tags": [{"type": "Uncategorized", "comment": "PROPRIETARY-LICENSE"}]}], "dependencies": [{"id": "detect-libc", "name": "detect-libc", "version": "2.0.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/lightningcss/node_modules/detect-libc"], "homepageUrl": "https://www.npmjs.com/package/detect-libc", "licenses": [{"key": "Apache-2.0", "path": "/data/project/node_modules/lightningcss/node_modules/detect-libc/package.json", "rules": ["Apache-2.0"], "tags": []}], "dependencies": []}, {"id": "lightningcss-linux-x64-gnu", "name": "lightningcss-linux-x64-gnu", "version": "1.30.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/lightningcss-linux-x64-gnu"], "homepageUrl": "https://www.npmjs.com/package/lightningcss-linux-x64-gnu", "licenses": [{"key": "MPL-2.0", "path": "/data/project/node_modules/lightningcss-linux-x64-gnu/package.json", "rules": ["MPL-2.0"], "tags": [{"type": "Uncategorized", "comment": "PROPRIETARY-LICENSE"}]}], "dependencies": []}, {"id": "lightningcss-linux-x64-musl", "name": "lightningcss-linux-x64-musl", "version": "1.30.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/lightningcss-linux-x64-musl"], "homepageUrl": "https://www.npmjs.com/package/lightningcss-linux-x64-musl", "licenses": [{"key": "MPL-2.0", "path": "/data/project/node_modules/lightningcss-linux-x64-musl/package.json", "rules": ["MPL-2.0"], "tags": [{"type": "Uncategorized", "comment": "PROPRIETARY-LICENSE"}]}], "dependencies": []}]}, {"id": "magic-string", "name": "magic-string", "version": "0.30.17", "module": "project:JS", "type": "npm", "paths": ["./node_modules/magic-string"], "homepageUrl": "https://www.npmjs.com/package/magic-string", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/magic-string/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@jridgewell/sourcemap-codec", "name": "@jridgewell/sourcemap-codec", "version": "1.5.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@jridgewell/sourcemap-codec"], "homepageUrl": "https://www.npmjs.com/package/@jridgewell/sourcemap-codec", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@jridgewell/sourcemap-codec/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "source-map-js", "name": "source-map-js", "version": "1.2.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/source-map-js"], "homepageUrl": "https://www.npmjs.com/package/source-map-js", "licenses": [{"key": "BSD-3-<PERSON><PERSON>", "path": "/data/project/node_modules/source-map-js/package.json", "rules": ["BSD-3-<PERSON><PERSON>"], "tags": []}], "dependencies": []}, {"id": "tailwindcss", "name": "tailwindcss", "version": "4.1.11", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tailwindcss"], "homepageUrl": "https://www.npmjs.com/package/tailwindcss", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/tailwindcss/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "@tailwindcss/oxide", "name": "@tailwindcss/oxide", "version": "4.1.11", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@tailwindcss/oxide"], "homepageUrl": "https://www.npmjs.com/package/@tailwindcss/oxide", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@tailwindcss/oxide/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@tailwindcss/oxide-linux-x64-gnu", "name": "@tailwindcss/oxide-linux-x64-gnu", "version": "4.1.11", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@tailwindcss/oxide-linux-x64-gnu"], "homepageUrl": "https://www.npmjs.com/package/@tailwindcss/oxide-linux-x64-gnu", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@tailwindcss/oxide-linux-x64-gnu/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@tailwindcss/oxide-linux-x64-musl", "name": "@tailwindcss/oxide-linux-x64-musl", "version": "4.1.11", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@tailwindcss/oxide-linux-x64-musl"], "homepageUrl": "https://www.npmjs.com/package/@tailwindcss/oxide-linux-x64-musl", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@tailwindcss/oxide-linux-x64-musl/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "detect-libc", "name": "detect-libc", "version": "2.0.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@tailwindcss/oxide/node_modules/detect-libc"], "homepageUrl": "https://www.npmjs.com/package/detect-libc", "licenses": [{"key": "Apache-2.0", "path": "/data/project/node_modules/lightningcss/node_modules/detect-libc/package.json", "rules": ["Apache-2.0"], "tags": []}], "dependencies": []}, {"id": "tar", "name": "tar", "version": "7.4.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tar"], "homepageUrl": "https://www.npmjs.com/package/tar", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/tar/package.json", "rules": ["ISC"], "tags": []}], "dependencies": [{"id": "@isaacs/fs-minipass", "name": "@isaacs/fs-minipass", "version": "4.0.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@isaacs/fs-minipass"], "homepageUrl": "https://www.npmjs.com/package/@isaacs/fs-minipass", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/@isaacs/fs-minipass/package.json", "rules": ["ISC"], "tags": []}], "dependencies": [{"id": "minipass", "name": "minipass", "version": "7.1.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/minipass"], "homepageUrl": "https://www.npmjs.com/package/minipass", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/minipass/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}]}, {"id": "chownr", "name": "chownr", "version": "3.0.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/chownr"], "homepageUrl": "https://www.npmjs.com/package/chownr", "licenses": [{"key": "BlueOak-1.0.0", "path": "/data/project/node_modules/chownr/package.json", "rules": ["BlueOak-1.0.0"], "tags": [{"type": "Uncategorized", "comment": "PROPRIETARY-LICENSE"}]}], "dependencies": []}, {"id": "minipass", "name": "minipass", "version": "7.1.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/minipass"], "homepageUrl": "https://www.npmjs.com/package/minipass", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/minipass/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}, {"id": "minizlib", "name": "minizlib", "version": "3.0.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/minizlib"], "homepageUrl": "https://www.npmjs.com/package/minizlib", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/minizlib/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "minipass", "name": "minipass", "version": "7.1.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/minipass"], "homepageUrl": "https://www.npmjs.com/package/minipass", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/minipass/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}]}, {"id": "mkdirp", "name": "mkdirp", "version": "3.0.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/mkdirp"], "homepageUrl": "https://www.npmjs.com/package/mkdirp", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/mkdirp/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "yallist", "name": "yallist", "version": "5.0.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/yallist"], "homepageUrl": "https://www.npmjs.com/package/yallist", "licenses": [{"key": "BlueOak-1.0.0", "path": "/data/project/node_modules/yallist/package.json", "rules": ["BlueOak-1.0.0"], "tags": [{"type": "Uncategorized", "comment": "PROPRIETARY-LICENSE"}]}], "dependencies": []}]}]}, {"id": "enhanced-resolve", "name": "enhanced-resolve", "version": "5.18.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/enhanced-resolve"], "homepageUrl": "https://www.npmjs.com/package/enhanced-resolve", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/enhanced-resolve/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "mri", "name": "mri", "version": "1.2.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/mri"], "homepageUrl": "https://www.npmjs.com/package/mri", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/mri/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "picocolors", "name": "picocolors", "version": "1.1.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/picocolors"], "homepageUrl": "https://www.npmjs.com/package/picocolors", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/picocolors/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}, {"id": "tailwindcss", "name": "tailwindcss", "version": "4.1.11", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tailwindcss"], "homepageUrl": "https://www.npmjs.com/package/tailwindcss", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/tailwindcss/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "better-sqlite3", "name": "better-sqlite3", "version": "12.2.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/better-sqlite3"], "homepageUrl": "https://www.npmjs.com/package/better-sqlite3", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/better-sqlite3/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "bindings", "name": "bindings", "version": "1.5.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/bindings"], "homepageUrl": "https://www.npmjs.com/package/bindings", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/bindings/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "file-uri-to-path", "name": "file-uri-to-path", "version": "1.0.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/file-uri-to-path"], "homepageUrl": "https://www.npmjs.com/package/file-uri-to-path", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/file-uri-to-path/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "prebuild-install", "name": "prebuild-install", "version": "7.1.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/prebuild-install"], "homepageUrl": "https://www.npmjs.com/package/prebuild-install", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/prebuild-install/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "detect-libc", "name": "detect-libc", "version": "2.0.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/prebuild-install/node_modules/detect-libc"], "homepageUrl": "https://www.npmjs.com/package/detect-libc", "licenses": [{"key": "Apache-2.0", "path": "/data/project/node_modules/lightningcss/node_modules/detect-libc/package.json", "rules": ["Apache-2.0"], "tags": []}], "dependencies": []}, {"id": "expand-template", "name": "expand-template", "version": "2.0.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/expand-template"], "homepageUrl": "https://www.npmjs.com/package/expand-template", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/expand-template/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "github-from-package", "name": "github-from-package", "version": "0.0.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/github-from-package"], "homepageUrl": "https://www.npmjs.com/package/github-from-package", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/github-from-package/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "minimist", "name": "minimist", "version": "1.2.8", "module": "project:JS", "type": "npm", "paths": ["./node_modules/minimist"], "homepageUrl": "https://www.npmjs.com/package/minimist", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/minimist/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "mkdirp-classic", "name": "mkdirp-classic", "version": "0.5.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/mkdirp-classic"], "homepageUrl": "https://www.npmjs.com/package/mkdirp-classic", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/mkdirp-classic/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "napi-build-utils", "name": "napi-build-utils", "version": "2.0.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/napi-build-utils"], "homepageUrl": "https://www.npmjs.com/package/napi-build-utils", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/napi-build-utils/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "node-abi", "name": "node-abi", "version": "3.75.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/node-abi"], "homepageUrl": "https://www.npmjs.com/package/node-abi", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/node-abi/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "semver", "name": "semver", "version": "7.7.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/semver"], "homepageUrl": "https://www.npmjs.com/package/semver", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/semver/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}]}, {"id": "pump", "name": "pump", "version": "3.0.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/pump"], "homepageUrl": "https://www.npmjs.com/package/pump", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/pump/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "end-of-stream", "name": "end-of-stream", "version": "1.4.5", "module": "project:JS", "type": "npm", "paths": ["./node_modules/end-of-stream"], "homepageUrl": "https://www.npmjs.com/package/end-of-stream", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/end-of-stream/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "once", "name": "once", "version": "1.4.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/once"], "homepageUrl": "https://www.npmjs.com/package/once", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/once/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}]}, {"id": "once", "name": "once", "version": "1.4.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/once"], "homepageUrl": "https://www.npmjs.com/package/once", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/once/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}]}, {"id": "rc", "name": "rc", "version": "1.2.8", "module": "project:JS", "type": "npm", "paths": ["./node_modules/rc"], "homepageUrl": "https://www.npmjs.com/package/rc", "licenses": [{"key": "BSD-2-<PERSON><PERSON>", "path": "/data/project/node_modules/rc/package.json", "rules": ["BSD-2-<PERSON><PERSON>"], "tags": []}], "dependencies": [{"id": "deep-extend", "name": "deep-extend", "version": "0.6.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/deep-extend"], "homepageUrl": "https://www.npmjs.com/package/deep-extend", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/deep-extend/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "ini", "name": "ini", "version": "1.3.8", "module": "project:JS", "type": "npm", "paths": ["./node_modules/ini"], "homepageUrl": "https://www.npmjs.com/package/ini", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/ini/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}, {"id": "minimist", "name": "minimist", "version": "1.2.8", "module": "project:JS", "type": "npm", "paths": ["./node_modules/minimist"], "homepageUrl": "https://www.npmjs.com/package/minimist", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/minimist/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "strip-json-comments", "name": "strip-json-comments", "version": "2.0.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/strip-json-comments"], "homepageUrl": "https://www.npmjs.com/package/strip-json-comments", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/strip-json-comments/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "simple-get", "name": "simple-get", "version": "4.0.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/simple-get"], "homepageUrl": "https://www.npmjs.com/package/simple-get", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/simple-get/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "decompress-response", "name": "decompress-response", "version": "6.0.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/decompress-response"], "homepageUrl": "https://www.npmjs.com/package/decompress-response", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/decompress-response/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "mimic-response", "name": "mimic-response", "version": "3.1.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/mimic-response"], "homepageUrl": "https://www.npmjs.com/package/mimic-response", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/mimic-response/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "once", "name": "once", "version": "1.4.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/once"], "homepageUrl": "https://www.npmjs.com/package/once", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/once/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}, {"id": "simple-concat", "name": "simple-concat", "version": "1.0.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/simple-concat"], "homepageUrl": "https://www.npmjs.com/package/simple-concat", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/simple-concat/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "tar-fs", "name": "tar-fs", "version": "2.1.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tar-fs"], "homepageUrl": "https://www.npmjs.com/package/tar-fs", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/tar-fs/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "chownr", "name": "chownr", "version": "1.1.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tar-fs/node_modules/chownr"], "homepageUrl": "https://www.npmjs.com/package/chownr", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/tar-fs/node_modules/chownr/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}, {"id": "mkdirp-classic", "name": "mkdirp-classic", "version": "0.5.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/mkdirp-classic"], "homepageUrl": "https://www.npmjs.com/package/mkdirp-classic", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/mkdirp-classic/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "pump", "name": "pump", "version": "3.0.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/pump"], "homepageUrl": "https://www.npmjs.com/package/pump", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/pump/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "tar-stream", "name": "tar-stream", "version": "2.2.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tar-stream"], "homepageUrl": "https://www.npmjs.com/package/tar-stream", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/tar-stream/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "bl", "name": "bl", "version": "4.1.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/bl"], "homepageUrl": "https://www.npmjs.com/package/bl", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/bl/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "buffer", "name": "buffer", "version": "5.7.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/buffer"], "homepageUrl": "https://www.npmjs.com/package/buffer", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/buffer/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "base64-js", "name": "base64-js", "version": "1.5.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/base64-js"], "homepageUrl": "https://www.npmjs.com/package/base64-js", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/base64-js/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "ieee754", "name": "ieee754", "version": "1.2.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/ieee754"], "homepageUrl": "https://www.npmjs.com/package/ieee754", "licenses": [{"key": "BSD-3-<PERSON><PERSON>", "path": "/data/project/node_modules/ieee754/package.json", "rules": ["BSD-3-<PERSON><PERSON>"], "tags": []}], "dependencies": []}]}, {"id": "inherits", "name": "inherits", "version": "2.0.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/inherits"], "homepageUrl": "https://www.npmjs.com/package/inherits", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/inherits/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}, {"id": "readable-stream", "name": "readable-stream", "version": "3.6.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/readable-stream"], "homepageUrl": "https://www.npmjs.com/package/readable-stream", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/readable-stream/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "end-of-stream", "name": "end-of-stream", "version": "1.4.5", "module": "project:JS", "type": "npm", "paths": ["./node_modules/end-of-stream"], "homepageUrl": "https://www.npmjs.com/package/end-of-stream", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/end-of-stream/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "fs-constants", "name": "fs-constants", "version": "1.0.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/fs-constants"], "homepageUrl": "https://www.npmjs.com/package/fs-constants", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/fs-constants/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "inherits", "name": "inherits", "version": "2.0.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/inherits"], "homepageUrl": "https://www.npmjs.com/package/inherits", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/inherits/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}, {"id": "readable-stream", "name": "readable-stream", "version": "3.6.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/readable-stream"], "homepageUrl": "https://www.npmjs.com/package/readable-stream", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/readable-stream/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}]}, {"id": "tunnel-agent", "name": "tunnel-agent", "version": "0.6.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tunnel-agent"], "homepageUrl": "https://www.npmjs.com/package/tunnel-agent", "licenses": [{"key": "Apache-2.0", "path": "/data/project/node_modules/tunnel-agent/package.json", "rules": ["Apache-2.0"], "tags": []}], "dependencies": [{"id": "safe-buffer", "name": "safe-buffer", "version": "5.2.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/safe-buffer"], "homepageUrl": "https://www.npmjs.com/package/safe-buffer", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/safe-buffer/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}]}]}, {"id": "dotenv", "name": "dotenv", "version": "17.2.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/dotenv"], "homepageUrl": "https://www.npmjs.com/package/dotenv", "licenses": [{"key": "BSD-2-<PERSON><PERSON>", "path": "/data/project/node_modules/dotenv/package.json", "rules": ["BSD-2-<PERSON><PERSON>"], "tags": []}], "dependencies": []}, {"id": "hono", "name": "hono", "version": "4.8.8", "module": "project:JS", "type": "npm", "paths": ["./node_modules/hono"], "homepageUrl": "https://www.npmjs.com/package/hono", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/hono/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "jazz-tools", "name": "jazz-tools", "version": "0.14.28", "module": "project:JS", "type": "npm", "paths": ["./node_modules/jazz-tools"], "homepageUrl": "https://www.npmjs.com/package/jazz-tools", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/jazz-tools/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@scure/bip39", "name": "@scure/bip39", "version": "1.6.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@scure/bip39"], "homepageUrl": "https://www.npmjs.com/package/@scure/bip39", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@scure/bip39/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@noble/hashes", "name": "@noble/hashes", "version": "1.8.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@noble/hashes"], "homepageUrl": "https://www.npmjs.com/package/@noble/hashes", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@noble/hashes/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@scure/base", "name": "@scure/base", "version": "1.2.6", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@scure/base"], "homepageUrl": "https://www.npmjs.com/package/@scure/base", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@scure/base/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "co<PERSON><PERSON>", "name": "co<PERSON><PERSON>", "version": "0.14.28", "module": "project:JS", "type": "npm", "paths": ["./node_modules/cojson"], "homepageUrl": "https://www.npmjs.com/package/cojson", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/cojson/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@noble/ciphers", "name": "@noble/ciphers", "version": "1.3.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@noble/ciphers"], "homepageUrl": "https://www.npmjs.com/package/@noble/ciphers", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@noble/ciphers/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@noble/curves", "name": "@noble/curves", "version": "1.9.4", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@noble/curves"], "homepageUrl": "https://www.npmjs.com/package/@noble/curves", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@noble/curves/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "@noble/hashes", "name": "@noble/hashes", "version": "1.8.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@noble/hashes"], "homepageUrl": "https://www.npmjs.com/package/@noble/hashes", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@noble/hashes/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "@noble/hashes", "name": "@noble/hashes", "version": "1.8.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@noble/hashes"], "homepageUrl": "https://www.npmjs.com/package/@noble/hashes", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/@noble/hashes/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "@opentelemetry/api", "name": "@opentelemetry/api", "version": "1.9.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/@opentelemetry/api"], "homepageUrl": "https://www.npmjs.com/package/@opentelemetry/api", "licenses": [{"key": "Apache-2.0", "path": "/data/project/node_modules/@opentelemetry/api/package.json", "rules": ["Apache-2.0"], "tags": []}], "dependencies": []}, {"id": "@scure/base", "name": "@scure/base", "version": "1.2.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/cojson/node_modules/@scure/base"], "homepageUrl": "https://www.npmjs.com/package/@scure/base", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/cojson/node_modules/@scure/base/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "jazz-crypto-rs", "name": "jazz-crypto-rs", "version": "0.0.7", "module": "project:JS", "type": "npm", "paths": ["./node_modules/jazz-crypto-rs"], "homepageUrl": "https://www.npmjs.com/package/jazz-crypto-rs", "licenses": [{"key": "MIT", "confidence": 0.9705882, "path": "/data/project/node_modules/jazz-crypto-rs/LICENSE", "rules": [], "tags": []}], "dependencies": []}, {"id": "neverthrow", "name": "neverthrow", "version": "7.2.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/neverthrow"], "homepageUrl": "https://www.npmjs.com/package/neverthrow", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/neverthrow/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "queueueue", "name": "queueueue", "version": "4.1.2", "module": "project:JS", "type": "npm", "paths": ["./node_modules/queueueue"], "homepageUrl": "https://www.npmjs.com/package/queueueue", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/queueueue/package.json", "rules": ["MIT"], "tags": []}], "dependencies": [{"id": "fast-list", "name": "fast-list", "version": "1.0.3", "module": "project:JS", "type": "npm", "paths": ["./node_modules/fast-list"], "homepageUrl": "https://www.npmjs.com/package/fast-list", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/fast-list/package.json", "rules": ["ISC"], "tags": []}], "dependencies": []}]}, {"id": "unicode-segmenter", "name": "unicode-segmenter", "version": "0.12.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/unicode-segmenter"], "homepageUrl": "https://www.npmjs.com/package/unicode-segmenter", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/unicode-segmenter/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "fast-myers-diff", "name": "fast-myers-diff", "version": "3.2.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/fast-myers-diff"], "homepageUrl": "https://www.npmjs.com/package/fast-myers-diff", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/fast-myers-diff/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "zod", "name": "zod", "version": "3.25.28", "module": "project:JS", "type": "npm", "paths": ["./node_modules/zod"], "homepageUrl": "https://www.npmjs.com/package/zod", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/zod/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "lucide-react", "name": "lucide-react", "version": "0.525.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/lucide-react"], "homepageUrl": "https://www.npmjs.com/package/lucide-react", "licenses": [{"key": "ISC", "path": "/data/project/node_modules/lucide-react/package.json", "rules": ["ISC"], "tags": []}], "dependencies": [{"id": "react", "name": "react", "version": "19.1.0", "module": "project:JS", "type": "npm", "paths": ["./node_modules/react"], "homepageUrl": "https://www.npmjs.com/package/react", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/react/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}]}, {"id": "tailwindcss", "name": "tailwindcss", "version": "4.1.11", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tailwindcss"], "homepageUrl": "https://www.npmjs.com/package/tailwindcss", "licenses": [{"key": "MIT", "path": "/data/project/node_modules/tailwindcss/package.json", "rules": ["MIT"], "tags": []}], "dependencies": []}, {"id": "tslib", "name": "tslib", "version": "2.8.1", "module": "project:JS", "type": "npm", "paths": ["./node_modules/tslib"], "homepageUrl": "https://www.npmjs.com/package/tslib", "licenses": [{"key": "0BSD", "path": "/data/project/node_modules/tslib/package.json", "rules": ["0BSD"], "tags": []}], "dependencies": []}]}], "licenses": [], "dependencies": []}