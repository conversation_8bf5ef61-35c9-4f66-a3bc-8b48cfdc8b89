[{"name": "@ampproject/remapping", "version": "2.3.0", "url": "https://www.npmjs.com/package/@ampproject/remapping", "license": "Apache-2.0", "licenseUrl": "http://www.apache.org/licenses/"}, {"name": "@anthropic-ai/sdk", "version": "0.57.0", "url": "https://www.npmjs.com/package/@anthropic-ai/sdk", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@emnapi/runtime", "version": "1.4.5", "url": "https://www.npmjs.com/package/@emnapi/runtime", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@hono/node-server", "version": "1.17.1", "url": "https://www.npmjs.com/package/@hono/node-server", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@isaacs/fs-minipass", "version": "4.0.1", "url": "https://www.npmjs.com/package/@isaacs/fs-minipass", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "@jridgewell/gen-mapping", "version": "0.3.12", "url": "https://www.npmjs.com/package/@jridgewell/gen-mapping", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@jridgewell/sourcemap-codec", "version": "1.5.4", "url": "https://www.npmjs.com/package/@jridgewell/sourcemap-codec", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@jridgewell/trace-mapping", "version": "0.3.29", "url": "https://www.npmjs.com/package/@jridgewell/trace-mapping", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@libsql/client", "version": "0.15.10", "url": "https://www.npmjs.com/package/@libsql/client", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@libsql/core", "version": "0.15.10", "url": "https://www.npmjs.com/package/@libsql/core", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@libsql/hrana-client", "version": "0.7.0", "url": "https://www.npmjs.com/package/@libsql/hrana-client", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@libsql/isomorphic-fetch", "version": "0.3.1", "url": "https://www.npmjs.com/package/@libsql/isomorphic-fetch", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@libsql/isomorphic-ws", "version": "0.1.5", "url": "https://www.npmjs.com/package/@libsql/isomorphic-ws", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@libsql/linux-x64-gnu", "version": "0.5.17", "url": "https://www.npmjs.com/package/@libsql/linux-x64-gnu", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@libsql/linux-x64-musl", "version": "0.5.17", "url": "https://www.npmjs.com/package/@libsql/linux-x64-musl", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@neon-rs/load", "version": "0.0.4", "url": "https://www.npmjs.com/package/@neon-rs/load", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@noble/ciphers", "version": "1.3.0", "url": "https://www.npmjs.com/package/@noble/ciphers", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@noble/curves", "version": "1.9.4", "url": "https://www.npmjs.com/package/@noble/curves", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@noble/hashes", "version": "1.8.0", "url": "https://www.npmjs.com/package/@noble/hashes", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@opentelemetry/api", "version": "1.9.0", "url": "https://www.npmjs.com/package/@opentelemetry/api", "license": "Apache-2.0", "licenseUrl": "http://www.apache.org/licenses/"}, {"name": "@parcel/watcher-linux-x64-glibc", "version": "2.5.1", "url": "https://www.npmjs.com/package/@parcel/watcher-linux-x64-glibc", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@parcel/watcher-linux-x64-musl", "version": "2.5.1", "url": "https://www.npmjs.com/package/@parcel/watcher-linux-x64-musl", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@parcel/watcher", "version": "2.5.1", "url": "https://www.npmjs.com/package/@parcel/watcher", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@scure/base", "version": "1.2.1", "url": "https://www.npmjs.com/package/@scure/base", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@scure/base", "version": "1.2.6", "url": "https://www.npmjs.com/package/@scure/base", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@scure/bip39", "version": "1.6.0", "url": "https://www.npmjs.com/package/@scure/bip39", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@starfederation/datastar", "version": "1.0.0-beta.11", "url": "https://www.npmjs.com/package/@starfederation/datastar", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@tailwindcss/cli", "version": "4.1.11", "url": "https://www.npmjs.com/package/@tailwindcss/cli", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@tailwindcss/node", "version": "4.1.11", "url": "https://www.npmjs.com/package/@tailwindcss/node", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@tailwindcss/oxide-linux-x64-gnu", "version": "4.1.11", "url": "https://www.npmjs.com/package/@tailwindcss/oxide-linux-x64-gnu", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@tailwindcss/oxide-linux-x64-musl", "version": "4.1.11", "url": "https://www.npmjs.com/package/@tailwindcss/oxide-linux-x64-musl", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@tailwindcss/oxide", "version": "4.1.11", "url": "https://www.npmjs.com/package/@tailwindcss/oxide", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@types/node", "version": "24.1.0", "url": "https://www.npmjs.com/package/@types/node", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "@types/ws", "version": "8.18.1", "url": "https://www.npmjs.com/package/@types/ws", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "base64-js", "version": "1.5.1", "url": "https://www.npmjs.com/package/base64-js", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "better-sqlite3", "version": "12.2.0", "url": "https://www.npmjs.com/package/better-sqlite3", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "bindings", "version": "1.5.0", "url": "https://www.npmjs.com/package/bindings", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "bl", "version": "4.1.0", "url": "https://www.npmjs.com/package/bl", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "braces", "version": "3.0.3", "url": "https://www.npmjs.com/package/braces", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "buffer", "version": "5.7.1", "url": "https://www.npmjs.com/package/buffer", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "chownr", "version": "1.1.4", "url": "https://www.npmjs.com/package/chownr", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "chownr", "version": "3.0.0", "url": "https://www.npmjs.com/package/chownr", "license": "BlueOak-1.0.0", "licenseUrl": "https://blueoakcouncil.org/license/1.0.0"}, {"name": "co<PERSON><PERSON>", "version": "0.14.28", "url": "https://www.npmjs.com/package/cojson", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "data-uri-to-buffer", "version": "4.0.1", "url": "https://www.npmjs.com/package/data-uri-to-buffer", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "decompress-response", "version": "6.0.0", "url": "https://www.npmjs.com/package/decompress-response", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "deep-extend", "version": "0.6.0", "url": "https://www.npmjs.com/package/deep-extend", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "detect-libc", "version": "1.0.3", "url": "https://www.npmjs.com/package/detect-libc", "license": "Apache-2.0", "licenseUrl": "http://www.apache.org/licenses/"}, {"name": "detect-libc", "version": "2.0.2", "url": "https://www.npmjs.com/package/detect-libc", "license": "Apache-2.0", "licenseUrl": "http://www.apache.org/licenses/"}, {"name": "detect-libc", "version": "2.0.4", "url": "https://www.npmjs.com/package/detect-libc", "license": "Apache-2.0", "licenseUrl": "http://www.apache.org/licenses/"}, {"name": "dotenv", "version": "17.2.1", "url": "https://www.npmjs.com/package/dotenv", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "http://www.opensource.org/licenses/BSD-2-Clause"}, {"name": "end-of-stream", "version": "1.4.5", "url": "https://www.npmjs.com/package/end-of-stream", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "enhanced-resolve", "version": "5.18.2", "url": "https://www.npmjs.com/package/enhanced-resolve", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "expand-template", "version": "2.0.3", "url": "https://www.npmjs.com/package/expand-template", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "fast-list", "version": "1.0.3", "url": "https://www.npmjs.com/package/fast-list", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "fast-myers-diff", "version": "3.2.0", "url": "https://www.npmjs.com/package/fast-myers-diff", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "fetch-blob", "version": "3.2.0", "url": "https://www.npmjs.com/package/fetch-blob", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "file-uri-to-path", "version": "1.0.0", "url": "https://www.npmjs.com/package/file-uri-to-path", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "fill-range", "version": "7.1.1", "url": "https://www.npmjs.com/package/fill-range", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "formdata-polyfill", "version": "4.0.10", "url": "https://www.npmjs.com/package/formdata-polyfill", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "fs-constants", "version": "1.0.0", "url": "https://www.npmjs.com/package/fs-constants", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "github-from-package", "version": "0.0.0", "url": "https://www.npmjs.com/package/github-from-package", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "hono", "version": "4.8.8", "url": "https://www.npmjs.com/package/hono", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "ieee754", "version": "1.2.1", "url": "https://www.npmjs.com/package/ieee754", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "http://www.opensource.org/licenses/BSD-3-Clause"}, {"name": "inherits", "version": "2.0.4", "url": "https://www.npmjs.com/package/inherits", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "ini", "version": "1.3.8", "url": "https://www.npmjs.com/package/ini", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "is-extglob", "version": "2.1.1", "url": "https://www.npmjs.com/package/is-extglob", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "is-glob", "version": "4.0.3", "url": "https://www.npmjs.com/package/is-glob", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "is-number", "version": "7.0.0", "url": "https://www.npmjs.com/package/is-number", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "jazz-crypto-rs", "version": "0.0.7", "url": "https://www.npmjs.com/package/jazz-crypto-rs", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "jazz-tools", "version": "0.14.28", "url": "https://www.npmjs.com/package/jazz-tools", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "jiti", "version": "2.5.0", "url": "https://www.npmjs.com/package/jiti", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "js-base64", "version": "3.7.7", "url": "https://www.npmjs.com/package/js-base64", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "http://www.opensource.org/licenses/BSD-3-Clause"}, {"name": "libsql", "version": "0.5.17", "url": "https://www.npmjs.com/package/libsql", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "lightningcss-linux-x64-gnu", "version": "1.30.1", "url": "https://www.npmjs.com/package/lightningcss-linux-x64-gnu", "license": "MPL-2.0", "licenseUrl": "http://mpl.mozilla.org/2012/01/03/announcing-mpl-2-0/"}, {"name": "lightningcss-linux-x64-musl", "version": "1.30.1", "url": "https://www.npmjs.com/package/lightningcss-linux-x64-musl", "license": "MPL-2.0", "licenseUrl": "http://mpl.mozilla.org/2012/01/03/announcing-mpl-2-0/"}, {"name": "lightningcss", "version": "1.30.1", "url": "https://www.npmjs.com/package/lightningcss", "license": "MPL-2.0", "licenseUrl": "http://mpl.mozilla.org/2012/01/03/announcing-mpl-2-0/"}, {"name": "lucide-react", "version": "0.525.0", "url": "https://www.npmjs.com/package/lucide-react", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "magic-string", "version": "0.30.17", "url": "https://www.npmjs.com/package/magic-string", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "micromatch", "version": "4.0.8", "url": "https://www.npmjs.com/package/micromatch", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "mimic-response", "version": "3.1.0", "url": "https://www.npmjs.com/package/mimic-response", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "minimist", "version": "1.2.8", "url": "https://www.npmjs.com/package/minimist", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "minipass", "version": "7.1.2", "url": "https://www.npmjs.com/package/minipass", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "minizlib", "version": "3.0.2", "url": "https://www.npmjs.com/package/minizlib", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "mkdirp-classic", "version": "0.5.3", "url": "https://www.npmjs.com/package/mkdirp-classic", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "mkdirp", "version": "3.0.1", "url": "https://www.npmjs.com/package/mkdirp", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "mri", "version": "1.2.0", "url": "https://www.npmjs.com/package/mri", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "mutative", "version": "1.2.0", "url": "https://www.npmjs.com/package/mutative", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "napi-build-utils", "version": "2.0.0", "url": "https://www.npmjs.com/package/napi-build-utils", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "neverthrow", "version": "7.2.0", "url": "https://www.npmjs.com/package/neverthrow", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "node-abi", "version": "3.75.0", "url": "https://www.npmjs.com/package/node-abi", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "node-addon-api", "version": "7.1.1", "url": "https://www.npmjs.com/package/node-addon-api", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "node-fetch", "version": "3.3.2", "url": "https://www.npmjs.com/package/node-fetch", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "once", "version": "1.4.0", "url": "https://www.npmjs.com/package/once", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "picocolors", "version": "1.1.1", "url": "https://www.npmjs.com/package/picocolors", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "picomatch", "version": "2.3.1", "url": "https://www.npmjs.com/package/picomatch", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "prebuild-install", "version": "7.1.3", "url": "https://www.npmjs.com/package/prebuild-install", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "promise-limit", "version": "2.7.0", "url": "https://www.npmjs.com/package/promise-limit", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "pump", "version": "3.0.3", "url": "https://www.npmjs.com/package/pump", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "queueueue", "version": "4.1.2", "url": "https://www.npmjs.com/package/queueueue", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "rc", "version": "1.2.8", "url": "https://www.npmjs.com/package/rc", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "http://www.opensource.org/licenses/BSD-2-Clause"}, {"name": "react", "version": "19.1.0", "url": "https://www.npmjs.com/package/react", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "readable-stream", "version": "3.6.2", "url": "https://www.npmjs.com/package/readable-stream", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "safe-buffer", "version": "5.2.1", "url": "https://www.npmjs.com/package/safe-buffer", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "semver", "version": "7.7.2", "url": "https://www.npmjs.com/package/semver", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "simple-concat", "version": "1.0.1", "url": "https://www.npmjs.com/package/simple-concat", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "simple-get", "version": "4.0.1", "url": "https://www.npmjs.com/package/simple-get", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "source-map-js", "version": "1.2.1", "url": "https://www.npmjs.com/package/source-map-js", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "http://www.opensource.org/licenses/BSD-3-Clause"}, {"name": "strip-json-comments", "version": "2.0.1", "url": "https://www.npmjs.com/package/strip-json-comments", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "tailwindcss", "version": "4.1.11", "url": "https://www.npmjs.com/package/tailwindcss", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "tar-fs", "version": "2.1.3", "url": "https://www.npmjs.com/package/tar-fs", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "tar-stream", "version": "2.2.0", "url": "https://www.npmjs.com/package/tar-stream", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "tar", "version": "7.4.3", "url": "https://www.npmjs.com/package/tar", "license": "ISC", "licenseUrl": "https://www.isc.org/licenses/"}, {"name": "to-regex-range", "version": "5.0.1", "url": "https://www.npmjs.com/package/to-regex-range", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "tslib", "version": "2.8.1", "url": "https://www.npmjs.com/package/tslib", "license": "0BSD", "licenseUrl": "http://landley.net/toybox/license.html"}, {"name": "tunnel-agent", "version": "0.6.0", "url": "https://www.npmjs.com/package/tunnel-agent", "license": "Apache-2.0", "licenseUrl": "http://www.apache.org/licenses/"}, {"name": "undici-types", "version": "7.8.0", "url": "https://www.npmjs.com/package/undici-types", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "unicode-segmenter", "version": "0.12.0", "url": "https://www.npmjs.com/package/unicode-segmenter", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "uuid", "version": "11.1.0", "url": "https://www.npmjs.com/package/uuid", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "ws", "version": "8.18.3", "url": "https://www.npmjs.com/package/ws", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}, {"name": "yallist", "version": "5.0.0", "url": "https://www.npmjs.com/package/yallist", "license": "BlueOak-1.0.0", "licenseUrl": "https://blueoakcouncil.org/license/1.0.0"}, {"name": "zod", "version": "3.25.28", "url": "https://www.npmjs.com/package/zod", "license": "MIT", "licenseUrl": "http://opensource.org/licenses/mit-license.php"}]