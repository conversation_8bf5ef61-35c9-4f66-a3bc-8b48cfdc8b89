# 🎯 Qodana Baseline Configuration

## 📈 什麼是 Baseline？

Baseline 是 Qodana 的一個重要功能，它允許我們：

- ✅ **逐步改善代碼品質**：將現有問題設為基準線，只關注新問題
- ✅ **避免被舊問題淹沒**：專注於新代碼的品質
- ✅ **持續改進**：可以逐步修復基準線中的問題

## 📁 文件說明

### `baseline.sarif.json`
- **用途**：存儲當前代碼庫的所有已知問題作為基準線
- **更新**：每次 Qodana 掃描時會自動更新
- **版本控制**：應該提交到 Git 以便團隊共享基準線

### `.gitignore`
- **用途**：管理 Qodana 生成的文件
- **保留**：baseline.sarif.json（需要版本控制）
- **忽略**：緩存和臨時文件

## 🔄 工作流程

### 1. 初始設置
```bash
# 第一次運行會生成完整的 baseline
git add .qodana/baseline.sarif.json
git commit -m "feat: Add Qodana baseline"
```

### 2. 日常開發
- ✅ 新代碼問題會被檢測並報告
- ✅ 基準線中的舊問題不會導致 CI 失敗
- ✅ 可以逐步修復基準線問題

### 3. 改善基準線
```bash
# 修復一些基準線問題後，更新基準線
# Qodana 會自動更新 baseline.sarif.json
git add .qodana/baseline.sarif.json
git commit -m "improve: Update Qodana baseline after fixes"
```

## 🎛️ 配置選項

### `qodana.yaml` 中的設置：
```yaml
baseline:
  include-absent: false  # 不包含已修復的問題
  path: ".qodana/baseline.sarif.json"  # 基準線文件路徑
```

### GitHub Actions 中的設置：
```yaml
baseline: .qodana/baseline.sarif.json
baseline-include-absent: false
```

## 📊 監控和改進

### 查看進度
- 在 Qodana 報告中查看新問題 vs 基準線問題
- 定期檢查基準線文件的變化

### 逐步改善
1. 選擇一類問題（如未使用變量）
2. 批量修復這類問題
3. 提交更新後的基準線
4. 重複此過程

## 🔗 相關資源

- [Qodana Baseline 文檔](https://www.jetbrains.com/help/qodana/baseline.html)
- [SARIF 格式說明](https://sarifweb.azurewebsites.net/)
- [HeartSync 代碼品質指南](../docs/code-quality.md)

---

**注意**：baseline.sarif.json 文件應該提交到版本控制系統，以確保團隊成員使用相同的基準線。
