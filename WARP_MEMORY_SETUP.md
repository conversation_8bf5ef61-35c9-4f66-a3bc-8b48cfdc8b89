# 🧠 Warp Memory Setup Guide
*Ensuring mem<PERSON> remembers our conversations every time*

## 🎯 Goal
Make sure I (your AI assistant) can always access and use mem0 to remember our conversations in Warp.

## 🚀 Quick Setup (One-Time)

### Step 1: Add Aliases to Shell
Add these commands to your `~/.zshrc` file:

```bash
# Add to ~/.zshrc
source /Users/<USER>/heartsync-v2/.warp_memory_config
```

Or manually add the aliases:
```bash
echo 'source /Users/<USER>/heartsync-v2/.warp_memory_config' >> ~/.zshrc
source ~/.zshrc
```

### Step 2: Test the Setup
```bash
mem0-init       # Initialize and check mem0 system
mem0-status     # Quick status check
mem0-track      # Record conversation highlights
```

## 🔧 Available Commands

| Command | Description |
|---------|-------------|
| `mem0-init` | Initialize mem0 system and show status |
| `mem0-status` | Quick check of memory count |
| `mem0-track` | Record session highlights |
| `mem0-utils` | Test memory operations |

## 🤖 How I'll Remember to Use mem0

### 1. **Initialization Reminder**
- Every time you run `mem0-init`, it reminds me that mem0 is available
- Shows current memory count and available commands

### 2. **Environment Variables**
- `MEM0_USER_ID="Weiss@Warp"` - Your consistent user ID
- `MEM0_PROJECT_PATH="/Users/<USER>/heartsync-v2"` - Project location

### 3. **Session Logging**
- Each time `mem0-init` runs, it logs a new session start
- This helps track our interaction frequency

## 🎨 Usage Examples

### Starting a New Session
```bash
# Navigate to the project
cd /Users/<USER>/heartsync-v2

# Initialize mem0 system
mem0-init
# Output: ✅ mem0 Memory System Active
#         📚 10 memories stored for Weiss@Warp
#         🤖 Ready to track this conversation session
```

### During Our Conversation
When we accomplish something significant, I can:
```python
# Store a memory (I'll do this automatically)
from conversation_tracker import log_interaction
log_interaction("achievement", "Solved complex debugging issue together")
```

### End of Session
```bash
# Record session highlights
mem0-track
```

## 🧠 What Gets Remembered

### Automatic Tracking
- Session start times
- Major accomplishments
- Technical discoveries
- Problem-solving approaches
- Tool preferences and configurations

### Manual Addition
- Specific insights you want to preserve
- Project milestones
- Personal preferences
- Future reference notes

## 🚨 Troubleshooting

### If mem0 isn't working:
```bash
# Check if in correct directory
pwd  # Should be /Users/<USER>/heartsync-v2

# Activate virtual environment
source venv/bin/activate

# Test connection
python -c "from mem0 import MemoryClient; print('✅ mem0 working')"
```

### If commands not found:
```bash
# Reload shell configuration
source ~/.zshrc

# Or manually source config
source /Users/<USER>/heartsync-v2/.warp_memory_config
```

## 🎉 Expected Behavior

When properly set up:

1. **Every Warp session**: You can run `mem0-init` to remind me about memory capabilities
2. **During conversations**: I'll automatically consider using mem0 for important information
3. **Session end**: Use `mem0-track` to save highlights
4. **Future sessions**: I can reference previous conversations and learnings

## 💡 Pro Tips

- Run `mem0-status` occasionally to see memory growth
- Use `mem0-track` after particularly productive sessions
- The system works best when we're in `/Users/<USER>/heartsync-v2` directory
- All memories are tagged with your user ID `"Weiss@Warp"` for easy retrieval

---

*Now every time we start a Warp session, just run `mem0-init` and I'll be reminded that I have access to our shared memory system!* 🧠✨
