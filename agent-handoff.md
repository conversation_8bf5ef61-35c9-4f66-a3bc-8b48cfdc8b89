# Agent 模式任務移交提示

## 任務概述
請在 Linear 中創建一個新任務，用於擴展 Cohere API 的測試覆蓋範圍。

## 任務詳情
- 標題: 擴展 Cohere API 測試覆蓋範圍
- 優先級: 中 (2)
- 估計時間: 3-4 小時

## 任務描述
```
## 描述
擴展現有的 Cohere API 測試集合，添加更多功能性測試和錯誤處理測試，以確保與 Cohere API 的集成更加穩定可靠。

## 任務
- [x] 審查現有的 Cohere API 測試
- [ ] 添加 Embeddings API 測試
- [ ] 添加 Classify API 測試
- [ ] 添加 Summarize API 測試
- [ ] 添加錯誤處理測試（無效 API 密鑰、無效參數等）
- [ ] 更新測試文檔

## 相關資源
- [Cohere API 文檔](https://docs.cohere.com/reference/about)
- [Bruno 文檔](https://docs.usebruno.com/)
- 現有測試: `api-tests/collections/cohere-api/`
```

## 執行方式
請使用 Linear MCP 創建此任務。您可以使用以下 MCP 命令：

```javascript
await mcp.Linear.createIssue({
  title: "擴展 Cohere API 測試覆蓋範圍",
  description: `## 描述\n擴展現有的 Cohere API 測試集合，添加更多功能性測試和錯誤處理測試，以確保與 Cohere API 的集成更加穩定可靠。\n\n## 任務\n- [x] 審查現有的 Cohere API 測試\n- [ ] 添加 Embeddings API 測試\n- [ ] 添加 Classify API 測試\n- [ ] 添加 Summarize API 測試\n- [ ] 添加錯誤處理測試（無效 API 密鑰、無效參數等）\n- [ ] 更新測試文檔\n\n## 相關資源\n- [Cohere API 文檔](https://docs.cohere.com/reference/about)\n- [Bruno 文檔](https://docs.usebruno.com/)\n- 現有測試: \`api-tests/collections/cohere-api/\``,
  priority: 2
});
```

## 完成後
請確認任務已成功創建，並提供任務 ID 或 URL。