[{"iterationIndex": 0, "summary": {"totalRequests": 4, "passedRequests": 3, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 1, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 7, "passedTests": 7, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/claude-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "headers": {"Content-Type": "application/json", "x-api-key": "************************************************************************************************************", "anthropic-version": "2023-06-01"}, "data": "{\n  \"model\": \"claude-3-haiku-20240307\",\n  \"max_tokens\": 100,\n  \"messages\": [\n    {\n      \"role\": \"user\", \n      \"content\": \"Hello! This is a health check from HSN Bruno tests. Please respond with a simple confirmation.\"\n    }\n  ]\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tue, 08 Jul 2025 16:05:04 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "anthropic-ratelimit-input-tokens-limit": "50000", "anthropic-ratelimit-input-tokens-remaining": "50000", "anthropic-ratelimit-input-tokens-reset": "2025-07-08T16:05:04Z", "anthropic-ratelimit-output-tokens-limit": "10000", "anthropic-ratelimit-output-tokens-remaining": "10000", "anthropic-ratelimit-output-tokens-reset": "2025-07-08T16:05:04Z", "anthropic-ratelimit-requests-limit": "50", "anthropic-ratelimit-requests-remaining": "49", "anthropic-ratelimit-requests-reset": "2025-07-08T16:05:04Z", "anthropic-ratelimit-tokens-limit": "60000", "anthropic-ratelimit-tokens-remaining": "60000", "anthropic-ratelimit-tokens-reset": "2025-07-08T16:05:04Z", "request-id": "req_011CQuqUM6bC1Dtt2quNshY8", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "anthropic-organization-id": "0b819686-9153-481c-bcf3-22aaa36b4309", "via": "1.1 google", "cf-cache-status": "DYNAMIC", "x-robots-tag": "none", "server": "cloudflare", "cf-ray": "95c0d224db48494e-KIX"}, "data": {"id": "msg_01RWzy3FHRzTqs4kNydjXnvu", "type": "message", "role": "assistant", "model": "claude-3-haiku-20240307", "content": [{"type": "text", "text": "Confirmed."}], "stop_reason": "end_turn", "stop_sequence": null, "usage": {"input_tokens": 27, "cache_creation_input_tokens": 0, "cache_read_input_tokens": 0, "output_tokens": 6, "service_tier": "standard"}}, "responseTime": 1876}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "etAqkJxCguEyL3ffgXhYE"}, {"description": "Response should contain content", "status": "pass", "uid": "zojqaGErGmeQLn55NfSzi"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.937940333, "name": "Claude API Health Check", "path": "collections/claude-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/hsn-health/app-health.bru"}, "request": {"method": "GET", "url": "http://localhost:8787/api/health", "headers": {}}, "response": {"status": "error", "statusText": null, "headers": null, "data": null, "responseTime": 0}, "error": "connect ECONNREFUSED ::1:8787", "status": "error", "assertionResults": [], "testResults": [], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.004397542, "name": "HSN Application Health Check", "path": "collections/hsn-health/app-health", "iterationIndex": 0}, {"test": {"filename": "collections/instantdb-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.instantdb.com/admin/query", "headers": {"Content-Type": "application/json", "app-id": "a24fb821-52fb-4b35-b9bf-35d561508a68", "authorization": "Bearer a24fb821-52fb-4b35-b9bf-35d561508a68"}, "data": "{\n  \"query\": {\n    \"health\": {}\n  }\n}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-type": "application/json; charset=utf-8", "content-length": "266", "connection": "keep-alive", "date": "Tue, 08 Jul 2025 16:05:05 GMT", "x-cache": "Error from cloudfront", "via": "1.1 1b2ec020d55b8b35f77724dc49853982.cloudfront.net (CloudFront)", "x-amz-cf-pop": "NRT57-P2", "alt-svc": "h3=\":443\"; ma=86400", "x-amz-cf-id": "vUcpqLeKOTUlF4vrYHJhWfNhgZTh5gxLfkS9RCfpa-_i49KWYyQorg=="}, "data": {"type": "record-not-found", "message": "Record not found: app-admin-token", "hint": {"args": [{"app-id": "a24fb821-52fb-4b35-b9bf-35d561508a68", "token": "a24fb821-52fb-4b35-b9bf-35d561508a68"}], "record-type": "app-admin-token"}, "trace-id": "72d46fe6cc2c267e859c78f014371a1c"}, "responseTime": 597}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be valid API response", "status": "pass", "uid": "KKuCT9zE3DMh0cVF0q5Pi"}, {"description": "Response should be JSON", "status": "pass", "uid": "vTUcmOUPuTqKWP-0i27ZG"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.612146375, "name": "InstantDB API Health Check", "path": "collections/instantdb-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/tavily-api/search-test.bru"}, "request": {"method": "POST", "url": "https://api.tavily.com/search", "headers": {"Content-Type": "application/json"}, "data": "{\n  \"api_key\": \"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\",\n  \"query\": \"AI assistant health check test\",\n  \"max_results\": 3,\n  \"include_answer\": true\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tu<PERSON>, 08 Jul 2025 16:05:07 GMT", "content-type": "application/json", "content-length": "1363", "connection": "keep-alive", "server": "u<PERSON><PERSON>", "content-security-policy": "default-src 'none'; script-src 'self'; connect-src 'self'; img-src 'self'; style-src 'self';base-uri 'self';form-action 'self'; require-trusted-types-for 'script'; upgrade-insecure-requests;"}, "data": {"query": "AI assistant health check test", "follow_up_questions": null, "answer": "An AI assistant health check test evaluates the functionality and accuracy of AI-driven health advice and symptom analysis. It assesses the AI's ability to provide personalized medical recommendations and insights. These tests ensure the AI's reliability and effectiveness in health-related queries.", "images": [], "results": [{"url": "https://docus.ai/ai-health-assistant", "title": "AI Health Assistant: Your 24/7 Virtual Medical Advisor - Docus.ai", "content": "The AI model is designed to provide personalized virtual health assistance and recommendations tailored to an individual's medical history, and symptoms.", "score": 0.48016238, "raw_content": null}, {"url": "https://docus.ai/symptom-checker", "title": "Docus AI Symptom Checker: Quick and Accurate Health Insights", "content": "Our AI analyzes your symptoms and instantly provides you with a comprehensive list of potential health conditions and insights. Start Symptom Assessment. Who", "score": 0.36412215, "raw_content": null}, {"url": "https://ubiehealth.com/", "title": "Ubie | Check Symptoms & Find Causes by AI", "content": "<PERSON><PERSON> Symptom Checker - Answer quiz about your symptoms to find out possible causes, types, severity, and treatment for free by AI. Developed by doctors.", "score": 0.14842972, "raw_content": null}], "response_time": 1.07}, "responseTime": 2564}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "TX-NDGx_qTL1q5zeEt9Mw"}, {"description": "Response should contain results", "status": "pass", "uid": "2ivIcfUYFT8adMhJCT6Pq"}, {"description": "Response should contain query", "status": "pass", "uid": "zh4Zp3wd0UFMjYxaRIqLK"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 2.578607708, "name": "Tavily API Search Test", "path": "collections/tavily-api/search-test", "iterationIndex": 0}]}]