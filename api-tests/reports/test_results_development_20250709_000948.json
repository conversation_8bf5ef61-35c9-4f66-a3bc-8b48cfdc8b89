[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 1, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 2, "passedTests": 2, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/openrouter-api/health-check.bru"}, "request": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "headers": {"Content-Type": "application/json", "Authorization": "Bearer 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"}, "data": "{\n  \"model\": \"openai/gpt-4o-mini\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.\"\n    }\n  ],\n  \"max_tokens\": 150,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tu<PERSON>, 08 Jul 2025 16:09:50 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "access-control-allow-origin": "*", "vary": "Accept-Encoding", "permissions-policy": "payment=(self \"https://checkout.stripe.com\" \"https://connect-js.stripe.com\" \"https://js.stripe.com\" \"https://*.js.stripe.com\" \"https://hooks.stripe.com\")", "referrer-policy": "no-referrer, strict-origin-when-cross-origin", "x-content-type-options": "nosniff", "server": "cloudflare", "cf-ray": "95c0d928da96fc9b-KIX"}, "data": {"id": "gen-**********-BsMVF6pJQ91PDambElGh", "provider": "OpenAI", "model": "openai/gpt-4o-mini", "object": "chat.completion", "created": **********, "choices": [{"logprobs": null, "finish_reason": "stop", "native_finish_reason": "stop", "index": 0, "message": {"role": "assistant", "content": "Hello! I'm here and working. How can I assist you today?", "refusal": null, "reasoning": null}}], "system_fingerprint": "fp_34a54ae93c", "usage": {"prompt_tokens": 30, "completion_tokens": 14, "total_tokens": 44, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}, "responseTime": 1136}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "PBeAXzPCryxth52V4Mf22"}, {"description": "Response should be JSON", "status": "pass", "uid": "SHugXv3tII1NdoJwUsxxX"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.195932708, "name": "OpenRouter API Health Check", "path": "collections/openrouter-api/health-check", "iterationIndex": 0}]}]