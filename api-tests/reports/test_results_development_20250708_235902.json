[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 1, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 2, "passedTests": 2, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/claude-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "headers": {"Content-Type": "application/json", "x-api-key": "************************************************************************************************************", "anthropic-version": "2023-06-01"}, "data": "{\n  \"model\": \"claude-3-haiku-20240307\",\n  \"max_tokens\": 100,\n  \"messages\": [\n    {\n      \"role\": \"user\", \n      \"content\": \"Hello! This is a health check from HSN Bruno tests. Please respond with a simple confirmation.\"\n    }\n  ]\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "<PERSON><PERSON>, 08 Jul 2025 15:59:05 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "anthropic-ratelimit-input-tokens-limit": "50000", "anthropic-ratelimit-input-tokens-remaining": "50000", "anthropic-ratelimit-input-tokens-reset": "2025-07-08T15:59:05Z", "anthropic-ratelimit-output-tokens-limit": "10000", "anthropic-ratelimit-output-tokens-remaining": "10000", "anthropic-ratelimit-output-tokens-reset": "2025-07-08T15:59:05Z", "anthropic-ratelimit-requests-limit": "50", "anthropic-ratelimit-requests-remaining": "49", "anthropic-ratelimit-requests-reset": "2025-07-08T15:59:05Z", "anthropic-ratelimit-tokens-limit": "60000", "anthropic-ratelimit-tokens-remaining": "60000", "anthropic-ratelimit-tokens-reset": "2025-07-08T15:59:05Z", "request-id": "req_011CQuq1vm96pP4D1B5Jby6m", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "anthropic-organization-id": "0b819686-9153-481c-bcf3-22aaa36b4309", "via": "1.1 google", "cf-cache-status": "DYNAMIC", "x-robots-tag": "none", "server": "cloudflare", "cf-ray": "95c0c964cf9c00ba-KIX"}, "data": {"id": "msg_013QdRJHbr8BvckketedEnTq", "type": "message", "role": "assistant", "model": "claude-3-haiku-20240307", "content": [{"type": "text", "text": "Hello, this is <PERSON>, an AI assistant created by <PERSON><PERSON><PERSON>. I can confirm that I have received your health check request."}], "stop_reason": "end_turn", "stop_sequence": null, "usage": {"input_tokens": 27, "cache_creation_input_tokens": 0, "cache_read_input_tokens": 0, "output_tokens": 30, "service_tier": "standard"}}, "responseTime": 1481}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "NWvMxtG6mhlzxLoPFtim_"}, {"description": "Response should contain content", "status": "pass", "uid": "L68Qgx8iUNnZsAn1FeIFU"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.535049083, "name": "Claude API Health Check", "path": "collections/claude-api/health-check", "iterationIndex": 0}]}]