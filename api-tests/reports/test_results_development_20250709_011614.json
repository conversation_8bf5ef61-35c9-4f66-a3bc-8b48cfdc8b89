[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 1, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 3, "passedTests": 3, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/cohere-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"prompt\": \"Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.\",\n  \"max_tokens\": 100,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "233", "num_tokens": "50", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-debug-trace-id": "693168b464841503a30fe5f30dc1cfda", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "40", "x-trial-endpoint-call-remaining": "38", "date": "Tue, 08 Jul 2025 17:16:17 GMT", "content-length": "482", "x-envoy-upstream-service-time": "789", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "1921f26c-0a67-44d8-946d-b4736376788f", "generations": [{"id": "840a70d6-a0eb-41bc-9a8f-61ea0278e9d6", "text": " Hello! I am working and ready to respond. Please go ahead and provide the details of the health check to proceed. ", "finish_reason": "COMPLETE"}], "prompt": "Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.", "meta": {"api_version": {"version": "1"}, "billed_units": {"input_tokens": 24, "output_tokens": 26}}}, "responseTime": 1346}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "zbK4msqqAvAFBD4KCyLAe"}, {"description": "Response should contain generations", "status": "pass", "uid": "hflMq2LIDc0J20snC5dih"}, {"description": "Response should contain text", "status": "pass", "uid": "YmPzTdYTgijB16Rvtq-aY"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.407415458, "name": "Cohere API Health Check", "path": "collections/cohere-api/health-check", "iterationIndex": 0}]}]