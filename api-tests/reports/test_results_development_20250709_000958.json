[{"iterationIndex": 0, "summary": {"totalRequests": 5, "passedRequests": 4, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 1, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 9, "passedTests": 9, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/claude-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "headers": {"Content-Type": "application/json", "x-api-key": "************************************************************************************************************", "anthropic-version": "2023-06-01"}, "data": "{\n  \"model\": \"claude-3-haiku-20240307\",\n  \"max_tokens\": 100,\n  \"messages\": [\n    {\n      \"role\": \"user\", \n      \"content\": \"Hello! This is a health check from HSN Bruno tests. Please respond with a simple confirmation.\"\n    }\n  ]\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tue, 08 Jul 2025 16:10:01 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "anthropic-ratelimit-input-tokens-limit": "50000", "anthropic-ratelimit-input-tokens-remaining": "50000", "anthropic-ratelimit-input-tokens-reset": "2025-07-08T16:10:01Z", "anthropic-ratelimit-output-tokens-limit": "10000", "anthropic-ratelimit-output-tokens-remaining": "10000", "anthropic-ratelimit-output-tokens-reset": "2025-07-08T16:10:01Z", "anthropic-ratelimit-requests-limit": "50", "anthropic-ratelimit-requests-remaining": "49", "anthropic-ratelimit-requests-reset": "2025-07-08T16:10:01Z", "anthropic-ratelimit-tokens-limit": "60000", "anthropic-ratelimit-tokens-remaining": "60000", "anthropic-ratelimit-tokens-reset": "2025-07-08T16:10:01Z", "request-id": "req_011CQuqrFw9WWDQDAi1nrtXc", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "anthropic-organization-id": "0b819686-9153-481c-bcf3-22aaa36b4309", "via": "1.1 google", "cf-cache-status": "DYNAMIC", "x-robots-tag": "none", "server": "cloudflare", "cf-ray": "95c0d9669ddfd3f3-KIX"}, "data": {"id": "msg_018sNvmatcXo1guXXnXgBUXJ", "type": "message", "role": "assistant", "model": "claude-3-haiku-20240307", "content": [{"type": "text", "text": "Hello! This is <PERSON>, an AI assistant created by <PERSON><PERSON><PERSON>. I have received your health check request and I can confirm that I am operational."}], "stop_reason": "end_turn", "stop_sequence": null, "usage": {"input_tokens": 27, "cache_creation_input_tokens": 0, "cache_read_input_tokens": 0, "output_tokens": 34, "service_tier": "standard"}}, "responseTime": 1566}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "ywpXTP5lVorEfcp2Of3RC"}, {"description": "Response should contain content", "status": "pass", "uid": "mlB-RvIdZk-EcV5nZQoa0"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.601491625, "name": "Claude API Health Check", "path": "collections/claude-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/hsn-health/app-health.bru"}, "request": {"method": "GET", "url": "http://localhost:8787/api/health", "headers": {}}, "response": {"status": "error", "statusText": null, "headers": null, "data": null, "responseTime": 0}, "error": "connect ECONNREFUSED ::1:8787", "status": "error", "assertionResults": [], "testResults": [], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.003358375, "name": "HSN Application Health Check", "path": "collections/hsn-health/app-health", "iterationIndex": 0}, {"test": {"filename": "collections/instantdb-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.instantdb.com/admin/query", "headers": {"Content-Type": "application/json", "app-id": "a24fb821-52fb-4b35-b9bf-35d561508a68", "authorization": "Bearer a24fb821-52fb-4b35-b9bf-35d561508a68"}, "data": "{\n  \"query\": {\n    \"health\": {}\n  }\n}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-type": "application/json; charset=utf-8", "content-length": "266", "connection": "keep-alive", "date": "Tue, 08 Jul 2025 16:10:01 GMT", "x-cache": "Error from cloudfront", "via": "1.1 43e1ca23939d600169617c2c9d3732da.cloudfront.net (CloudFront)", "x-amz-cf-pop": "NRT57-P2", "alt-svc": "h3=\":443\"; ma=86400", "x-amz-cf-id": "vESeNtrHr44pmjSJOy0dcX49hlH4ifab37heXQzOpPXAeguh4UKSsA=="}, "data": {"type": "record-not-found", "message": "Record not found: app-admin-token", "hint": {"args": [{"app-id": "a24fb821-52fb-4b35-b9bf-35d561508a68", "token": "a24fb821-52fb-4b35-b9bf-35d561508a68"}], "record-type": "app-admin-token"}, "trace-id": "0b28a2b1365ec68b7516326a949c70a1"}, "responseTime": 665}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be valid API response", "status": "pass", "uid": "eafpBX7CeQooyCQulvY7k"}, {"description": "Response should be JSON", "status": "pass", "uid": "7R_v1kXX4fnXDVpn5zbik"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.674717667, "name": "InstantDB API Health Check", "path": "collections/instantdb-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/openrouter-api/health-check.bru"}, "request": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "headers": {"Content-Type": "application/json", "Authorization": "Bearer 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"}, "data": "{\n  \"model\": \"openai/gpt-4o-mini\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.\"\n    }\n  ],\n  \"max_tokens\": 150,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tue, 08 Jul 2025 16:10:02 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "access-control-allow-origin": "*", "vary": "Accept-Encoding", "permissions-policy": "payment=(self \"https://checkout.stripe.com\" \"https://connect-js.stripe.com\" \"https://js.stripe.com\" \"https://*.js.stripe.com\" \"https://hooks.stripe.com\")", "referrer-policy": "no-referrer, strict-origin-when-cross-origin", "x-content-type-options": "nosniff", "server": "cloudflare", "cf-ray": "95c0d973bda423a4-<PERSON><PERSON>"}, "data": {"id": "gen-**********-JIUN3QwUo6YoY5Kvmdh7", "provider": "OpenAI", "model": "openai/gpt-4o-mini", "object": "chat.completion", "created": **********, "choices": [{"logprobs": null, "finish_reason": "stop", "native_finish_reason": "stop", "index": 0, "message": {"role": "assistant", "content": "Hello! I'm here and working. How can I assist you today?", "refusal": null, "reasoning": null}}], "system_fingerprint": "fp_34a54ae93c", "usage": {"prompt_tokens": 30, "completion_tokens": 14, "total_tokens": 44, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}, "responseTime": 1680}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "ygN3uQBlBsXiczHICs1US"}, {"description": "Response should be JSON", "status": "pass", "uid": "0wEN2HfyztLovensvK1A7"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.692017208, "name": "OpenRouter API Health Check", "path": "collections/openrouter-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/tavily-api/search-test.bru"}, "request": {"method": "POST", "url": "https://api.tavily.com/search", "headers": {"Content-Type": "application/json"}, "data": "{\n  \"api_key\": \"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\",\n  \"query\": \"AI assistant health check test\",\n  \"max_results\": 3,\n  \"include_answer\": true\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tue, 08 Jul 2025 16:10:05 GMT", "content-type": "application/json", "content-length": "1363", "connection": "keep-alive", "server": "u<PERSON><PERSON>", "content-security-policy": "default-src 'none'; script-src 'self'; connect-src 'self'; img-src 'self'; style-src 'self';base-uri 'self';form-action 'self'; require-trusted-types-for 'script'; upgrade-insecure-requests;"}, "data": {"query": "AI assistant health check test", "follow_up_questions": null, "answer": "An AI assistant health check test evaluates the functionality and accuracy of AI-driven health advice and symptom analysis. It assesses the AI's ability to provide personalized medical recommendations and insights. These tests ensure the AI's reliability and effectiveness in health-related queries.", "images": [], "results": [{"url": "https://docus.ai/ai-health-assistant", "title": "AI Health Assistant: Your 24/7 Virtual Medical Advisor - Docus.ai", "content": "The AI model is designed to provide personalized virtual health assistance and recommendations tailored to an individual's medical history, and symptoms.", "score": 0.48016238, "raw_content": null}, {"url": "https://docus.ai/symptom-checker", "title": "Docus AI Symptom Checker: Quick and Accurate Health Insights", "content": "Our AI analyzes your symptoms and instantly provides you with a comprehensive list of potential health conditions and insights. Start Symptom Assessment. Who", "score": 0.36412215, "raw_content": null}, {"url": "https://ubiehealth.com/", "title": "Ubie | Check Symptoms & Find Causes by AI", "content": "<PERSON><PERSON> Symptom Checker - Answer quiz about your symptoms to find out possible causes, types, severity, and treatment for free by AI. Developed by doctors.", "score": 0.14842972, "raw_content": null}], "response_time": 1.12}, "responseTime": 2384}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "ZApvW5FWDu_CH6sIQdD3z"}, {"description": "Response should contain results", "status": "pass", "uid": "79EFKS8ODccC4mdapVz4H"}, {"description": "Response should contain query", "status": "pass", "uid": "neBztP5TRByf_nvb3FY-z"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 2.398193125, "name": "Tavily API Search Test", "path": "collections/tavily-api/search-test", "iterationIndex": 0}]}]