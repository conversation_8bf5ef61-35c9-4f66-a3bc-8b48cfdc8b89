[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 1, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 3, "passedTests": 3, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/cohere-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"model\": \"command-light\",\n  \"prompt\": \"Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.\",\n  \"max_tokens\": 100,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "284", "num_tokens": "55", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-api-warning": "unknown field: parameter 'model' is not a valid field", "x-debug-trace-id": "076ce937fed0b62d91e2a77534fdd708", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "40", "x-trial-endpoint-call-remaining": "39", "date": "Tue, 08 Jul 2025 17:15:18 GMT", "content-length": "533", "x-envoy-upstream-service-time": "346", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "2a890a88-484b-4ee2-859f-696fb8a5b749", "generations": [{"id": "566196d0-8344-4c92-bd2c-0e85902b8725", "text": " Yes, I am here and ready to assist with prompt responses about health and fitness queries, as well as general conversations regarding API testing and collaboration! ", "finish_reason": "COMPLETE"}], "prompt": "Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.", "meta": {"api_version": {"version": "1"}, "billed_units": {"input_tokens": 24, "output_tokens": 31}}}, "responseTime": 1037}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "hSqFGjcds0C40ItigwyGB"}, {"description": "Response should contain generations", "status": "pass", "uid": "HqYA8ytp3PwJxtVMEZuMa"}, {"description": "Response should contain text", "status": "pass", "uid": "eqbWqcqlHZp7KMFUZbZ5E"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.099892334, "name": "Cohere API Health Check", "path": "collections/cohere-api/health-check", "iterationIndex": 0}]}]