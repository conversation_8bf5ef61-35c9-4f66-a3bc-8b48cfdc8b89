[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 1, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 2, "passedTests": 2, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/instantdb-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.instantdb.com/admin/query", "headers": {"Content-Type": "application/json", "app-id": "a24fb821-52fb-4b35-b9bf-35d561508a68", "authorization": "Bearer a24fb821-52fb-4b35-b9bf-35d561508a68"}, "data": "{\n  \"query\": {\n    \"health\": {}\n  }\n}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-type": "application/json; charset=utf-8", "content-length": "266", "connection": "keep-alive", "date": "Tue, 08 Jul 2025 16:04:30 GMT", "x-cache": "Error from cloudfront", "via": "1.1 1e5ca059f48c688576a90d719b9ff148.cloudfront.net (CloudFront)", "x-amz-cf-pop": "NRT57-P2", "alt-svc": "h3=\":443\"; ma=86400", "x-amz-cf-id": "bZTL65SwqenHQu7W4JNlRUovHUJz1cfg_GQ--N0JXrt920FZHF1P2g=="}, "data": {"type": "record-not-found", "message": "Record not found: app-admin-token", "hint": {"args": [{"app-id": "a24fb821-52fb-4b35-b9bf-35d561508a68", "token": "a24fb821-52fb-4b35-b9bf-35d561508a68"}], "record-type": "app-admin-token"}, "trace-id": "63b90ff63be813c05e23b862706fedbd"}, "responseTime": 613}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be valid API response", "status": "pass", "uid": "XBLH7IfZ8FICZCOOv9q8O"}, {"description": "Response should be JSON", "status": "pass", "uid": "yojPCtTEcelx-3HfioUZ5"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.672592916, "name": "InstantDB API Health Check", "path": "collections/instantdb-api/health-check", "iterationIndex": 0}]}]