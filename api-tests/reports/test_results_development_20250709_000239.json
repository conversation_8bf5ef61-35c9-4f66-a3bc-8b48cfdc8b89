[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 1, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 3, "passedTests": 3, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/tavily-api/search-test.bru"}, "request": {"method": "POST", "url": "https://api.tavily.com/search", "headers": {"Content-Type": "application/json"}, "data": "{\n  \"api_key\": \"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\",\n  \"query\": \"AI assistant health check test\",\n  \"max_results\": 3,\n  \"include_answer\": true\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tue, 08 Jul 2025 16:02:47 GMT", "content-type": "application/json", "content-length": "1363", "connection": "keep-alive", "server": "u<PERSON><PERSON>", "content-security-policy": "default-src 'none'; script-src 'self'; connect-src 'self'; img-src 'self'; style-src 'self';base-uri 'self';form-action 'self'; require-trusted-types-for 'script'; upgrade-insecure-requests;"}, "data": {"query": "AI assistant health check test", "follow_up_questions": null, "answer": "An AI assistant health check test evaluates the functionality and accuracy of AI-driven health advice and symptom analysis. It assesses the AI's ability to provide personalized medical recommendations and insights. These tests ensure the AI's reliability and effectiveness in health-related queries.", "images": [], "results": [{"url": "https://docus.ai/ai-health-assistant", "title": "AI Health Assistant: Your 24/7 Virtual Medical Advisor - Docus.ai", "content": "The AI model is designed to provide personalized virtual health assistance and recommendations tailored to an individual's medical history, and symptoms.", "score": 0.48016238, "raw_content": null}, {"url": "https://docus.ai/symptom-checker", "title": "Docus AI Symptom Checker: Quick and Accurate Health Insights", "content": "Our AI analyzes your symptoms and instantly provides you with a comprehensive list of potential health conditions and insights. Start Symptom Assessment. Who", "score": 0.36412215, "raw_content": null}, {"url": "https://ubiehealth.com/", "title": "Ubie | Check Symptoms & Find Causes by AI", "content": "<PERSON><PERSON> Symptom Checker - Answer quiz about your symptoms to find out possible causes, types, severity, and treatment for free by AI. Developed by doctors.", "score": 0.14842972, "raw_content": null}], "response_time": 6.14}, "responseTime": 7231}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "6fUyPwHuT3tyu22L37ldT"}, {"description": "Response should contain results", "status": "pass", "uid": "N5AfZ9Y0MZih0KxBIyNzi"}, {"description": "Response should contain query", "status": "pass", "uid": "d2If6xrUhy2EYiJczFarw"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 7.291462542, "name": "Tavily API Search Test", "path": "collections/tavily-api/search-test", "iterationIndex": 0}]}]