[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 0, "failedRequests": 1, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 3, "passedTests": 0, "failedTests": 3, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/tavily-api/search-test.bru"}, "request": {"method": "POST", "url": "https://api.tavily.com/search", "headers": {"Content-Type": "application/json"}, "data": "{\n  \"api_key\": \"{{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}}\",\n  \"query\": \"AI assistant health check test\",\n  \"max_results\": 3,\n  \"include_answer\": true\n}"}, "response": {"status": 401, "statusText": "Unauthorized", "headers": {"date": "Tu<PERSON>, 08 Jul 2025 16:01:44 GMT", "content-type": "application/json", "content-length": "64", "connection": "keep-alive", "server": "u<PERSON><PERSON>", "content-security-policy": "default-src 'none'; script-src 'self'; connect-src 'self'; img-src 'self'; style-src 'self';base-uri 'self';form-action 'self'; require-trusted-types-for 'script'; upgrade-insecure-requests;"}, "data": {"detail": {"error": "Unauthorized: missing or invalid API key."}}, "responseTime": 976}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "fail", "error": "expected 401 to equal 200", "actual": 401, "expected": 200, "uid": "AP8hvTrCAszHElQkSuHkn"}, {"description": "Response should contain results", "status": "fail", "error": "expected { Object (detail) } to have property 'results'", "actual": {"detail": {"error": "Unauthorized: missing or invalid API key."}}, "uid": "eRYZbgSu-wuNqBZR3DIwp"}, {"description": "Response should contain query", "status": "fail", "error": "expected { Object (detail) } to have property 'query'", "actual": {"detail": {"error": "Unauthorized: missing or invalid API key."}}, "uid": "ym93q7FwDjxGPmBOKYGDE"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.045525583, "name": "Tavily API Search Test", "path": "collections/tavily-api/search-test", "iterationIndex": 0}]}]