[{"iterationIndex": 0, "summary": {"totalRequests": 6, "passedRequests": 5, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 1, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 12, "passedTests": 12, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/claude-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "headers": {"Content-Type": "application/json", "x-api-key": "************************************************************************************************************", "anthropic-version": "2023-06-01"}, "data": "{\n  \"model\": \"claude-3-haiku-20240307\",\n  \"max_tokens\": 100,\n  \"messages\": [\n    {\n      \"role\": \"user\", \n      \"content\": \"Hello! This is a health check from HSN Bruno tests. Please respond with a simple confirmation.\"\n    }\n  ]\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tue, 08 Jul 2025 17:16:31 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "anthropic-ratelimit-input-tokens-limit": "50000", "anthropic-ratelimit-input-tokens-remaining": "50000", "anthropic-ratelimit-input-tokens-reset": "2025-07-08T17:16:30Z", "anthropic-ratelimit-output-tokens-limit": "10000", "anthropic-ratelimit-output-tokens-remaining": "10000", "anthropic-ratelimit-output-tokens-reset": "2025-07-08T17:16:31Z", "anthropic-ratelimit-requests-limit": "50", "anthropic-ratelimit-requests-remaining": "49", "anthropic-ratelimit-requests-reset": "2025-07-08T17:16:30Z", "anthropic-ratelimit-tokens-limit": "60000", "anthropic-ratelimit-tokens-remaining": "60000", "anthropic-ratelimit-tokens-reset": "2025-07-08T17:16:30Z", "request-id": "req_011CQuvvKLEbmBrXPrBFMm9c", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "anthropic-organization-id": "0b819686-9153-481c-bcf3-22aaa36b4309", "via": "1.1 google", "cf-cache-status": "DYNAMIC", "x-robots-tag": "none", "server": "cloudflare", "cf-ray": "95c13acad91bd421-<PERSON><PERSON>"}, "data": {"id": "msg_01XdKbUeyLHxSwJEkcYCM3M8", "type": "message", "role": "assistant", "model": "claude-3-haiku-20240307", "content": [{"type": "text", "text": "Confirmed."}], "stop_reason": "end_turn", "stop_sequence": null, "usage": {"input_tokens": 27, "cache_creation_input_tokens": 0, "cache_read_input_tokens": 0, "output_tokens": 6, "service_tier": "standard"}}, "responseTime": 2350}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "jAZetUJ-_Q6tJsiVCKyBN"}, {"description": "Response should contain content", "status": "pass", "uid": "BSCdh2KxwHGn2ZFmyilPf"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 2.409004125, "name": "Claude API Health Check", "path": "collections/claude-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"prompt\": \"Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.\",\n  \"max_tokens\": 100,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "191", "num_tokens": "44", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-debug-trace-id": "e5ec31d6fd870bb3676f9982de2b081c", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "40", "x-trial-endpoint-call-remaining": "38", "date": "Tue, 08 Jul 2025 17:16:32 GMT", "content-length": "440", "x-envoy-upstream-service-time": "607", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "6aeb4f8e-295d-427a-b655-b350b5d1671d", "generations": [{"id": "31781a77-384d-4df0-a40c-5ce7b11c8af7", "text": " Hello! I'm doing well and ready to respond. How can I assist you today? ", "finish_reason": "COMPLETE"}], "prompt": "Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.", "meta": {"api_version": {"version": "1"}, "billed_units": {"input_tokens": 24, "output_tokens": 20}}}, "responseTime": 975}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "_MEk803_8OfnPhoyG50Xo"}, {"description": "Response should contain generations", "status": "pass", "uid": "29ca07KjsoVDVaPVf0xt1"}, {"description": "Response should contain text", "status": "pass", "uid": "ihPRwxVFk-b8JFZT-w7Xe"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.988468333, "name": "Cohere API Health Check", "path": "collections/cohere-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/hsn-health/app-health.bru"}, "request": {"method": "GET", "url": "http://localhost:8787/api/health", "headers": {}}, "response": {"status": "error", "statusText": null, "headers": null, "data": null, "responseTime": 0}, "error": "connect ECONNREFUSED ::1:8787", "status": "error", "assertionResults": [], "testResults": [], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.006087417, "name": "HSN Application Health Check", "path": "collections/hsn-health/app-health", "iterationIndex": 0}, {"test": {"filename": "collections/instantdb-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.instantdb.com/admin/query", "headers": {"Content-Type": "application/json", "app-id": "a24fb821-52fb-4b35-b9bf-35d561508a68", "authorization": "Bearer a24fb821-52fb-4b35-b9bf-35d561508a68"}, "data": "{\n  \"query\": {\n    \"health\": {}\n  }\n}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-type": "application/json; charset=utf-8", "content-length": "266", "connection": "keep-alive", "date": "Tue, 08 Jul 2025 17:16:32 GMT", "x-cache": "Error from cloudfront", "via": "1.1 4bee516d0163cd392c310c300265b098.cloudfront.net (CloudFront)", "x-amz-cf-pop": "NRT57-P2", "alt-svc": "h3=\":443\"; ma=86400", "x-amz-cf-id": "37W6aoM0GHgxWlGbxcQqr4JAGcxW_git0UAz7rQ-8_7ECOsMkwn21w=="}, "data": {"type": "record-not-found", "message": "Record not found: app-admin-token", "hint": {"args": [{"app-id": "a24fb821-52fb-4b35-b9bf-35d561508a68", "token": "a24fb821-52fb-4b35-b9bf-35d561508a68"}], "record-type": "app-admin-token"}, "trace-id": "59c4d8c227efbea90ea42631371faca3"}, "responseTime": 691}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be valid API response", "status": "pass", "uid": "k7pwZT47SQ7a4ixup8rCA"}, {"description": "Response should be JSON", "status": "pass", "uid": "KGGbodXtp2rsB7iNvZ_Kd"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.70406675, "name": "InstantDB API Health Check", "path": "collections/instantdb-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/openrouter-api/health-check.bru"}, "request": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "headers": {"Content-Type": "application/json", "Authorization": "Bearer 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"}, "data": "{\n  \"model\": \"openai/gpt-4o-mini\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.\"\n    }\n  ],\n  \"max_tokens\": 150,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tue, 08 Jul 2025 17:16:35 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "access-control-allow-origin": "*", "vary": "Accept-Encoding", "permissions-policy": "payment=(self \"https://checkout.stripe.com\" \"https://connect-js.stripe.com\" \"https://js.stripe.com\" \"https://*.js.stripe.com\" \"https://hooks.stripe.com\")", "referrer-policy": "no-referrer, strict-origin-when-cross-origin", "x-content-type-options": "nosniff", "server": "cloudflare", "cf-ray": "95c13ae3e90cb4a1-KIX"}, "data": {"id": "gen-**********-TFPBqzHO0usoRBEVlSJw", "provider": "OpenAI", "model": "openai/gpt-4o-mini", "object": "chat.completion", "created": **********, "choices": [{"logprobs": null, "finish_reason": "stop", "native_finish_reason": "stop", "index": 0, "message": {"role": "assistant", "content": "Hello! I'm here and working. How can I assist you today?", "refusal": null, "reasoning": null}}], "system_fingerprint": "fp_34a54ae93c", "usage": {"prompt_tokens": 30, "completion_tokens": 14, "total_tokens": 44, "prompt_tokens_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}, "responseTime": 3185}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "r-qa1or7flENMTNPWuQSU"}, {"description": "Response should be JSON", "status": "pass", "uid": "ezBqEwirKjGPrr8nljOT5"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 3.198194, "name": "OpenRouter API Health Check", "path": "collections/openrouter-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/tavily-api/search-test.bru"}, "request": {"method": "POST", "url": "https://api.tavily.com/search", "headers": {"Content-Type": "application/json"}, "data": "{\n  \"api_key\": \"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\",\n  \"query\": \"AI assistant health check test\",\n  \"max_results\": 3,\n  \"include_answer\": true\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "Tu<PERSON>, 08 Jul 2025 17:16:42 GMT", "content-type": "application/json", "content-length": "3104", "connection": "keep-alive", "server": "u<PERSON><PERSON>", "content-security-policy": "default-src 'none'; script-src 'self'; connect-src 'self'; img-src 'self'; style-src 'self';base-uri 'self';form-action 'self'; require-trusted-types-for 'script'; upgrade-insecure-requests;"}, "data": {"query": "AI assistant health check test", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://docus.ai/ai-health-assistant", "title": "AI Health Assistant: Your 24/7 Virtual Medical Advisor - Docus.ai", "content": "Diagnosis by Docus AI Health Assistant Health report by AI Doctor <PERSON>us AI Health Assistant is not a real doctor, even though it is trained on a vast amount of medical data. AI Doctor is the chatbot feature of Docus AI Health Assistant, designed to provide health-related information, recommendations, and support. To interact with the AI Doctor, first sign up on the Docus Platform and complete your health profile. Simply enter your health-related questions or concerns in the chat area, and the AI Doctor will analyze your inputs and provide responses tailored to your needs. The Docus Health Assistant ensures that all data, including interactions with the AI Doctor, are protected with advanced encryption and security protocols, fully compliant with HIPAA and GDPR standards.", "score": 0.5993685, "raw_content": null}, {"url": "https://docus.ai/symptom-checker", "title": "Docus AI Symptom Checker: Quick and Accurate Health Insights", "content": "Symptom Checker powered by AI Just 3 simple steps to efficiently understand and manage your health online with Docus AI Symptom Checker: Describe your symptoms Answer targeted questions Receive instant health insights Please Note!This tool is not intended to be a substitute for professional medical advice, diagnosis, or treatment. Why It’s the Best Docus AI Symptom Checker is a powerful and absolutely free online tool crafted using AI technology trained on a vast amount of medical data and validated by top human doctors. Input Your Symptoms Begin by entering your single or multiple symptoms into the checker. Receive Your Results Our AI analyzes your symptoms and instantly provides you with a comprehensive list of potential health conditions and insights. Who It's For Docus Symptom Checker is made for those who want to understand their single or multiple symptoms, explore possible causes, and navigate their health with confidence.", "score": 0.42835352, "raw_content": null}, {"url": "https://ubiehealth.com/", "title": "Ubie | Check Symptoms & Find Causes by AI", "content": "Symptoms How <PERSON><PERSON>'s Medical AI Works By starting the symptom checker, you agree to the Privacy Policy and Terms of Use Start with Symptoms Find other Symptoms ## How <PERSON>bie Can Help You Your symptoms <PERSON><PERSON> had all the correct symptoms and led me in the right direction of whether I should wait or see a doctor. <PERSON><PERSON> helped ease my anxiety around my health. In order to help you find the right medical care, our team is constantly improving our AI in collaboration with experienced doctors and medical institutions around the world. ### <PERSON><PERSON> is Supervised by 50+ Medical Experts Worldwide ### <PERSON><PERSON>'s AI is trusted and continuously trained by clinical practice By starting the symptom checker, you agree to the Privacy Policy and Terms of Use Symptoms How <PERSON><PERSON>'s Medical AI Works", "score": 0.103232384, "raw_content": null}], "response_time": 5.62}, "responseTime": 6777}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "7HaFxmyhadV5FVeZQAO5X"}, {"description": "Response should contain results", "status": "pass", "uid": "B931PlTYC_VokBHxOQ4TG"}, {"description": "Response should contain query", "status": "pass", "uid": "c-qRMkpjgM0QZnpwpXB6l"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 6.794317167, "name": "Tavily API Search Test", "path": "collections/tavily-api/search-test", "iterationIndex": 0}]}]