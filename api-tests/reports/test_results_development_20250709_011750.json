[{"iterationIndex": 0, "summary": {"totalRequests": 2, "passedRequests": 2, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 6, "passedTests": 6, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/cohere-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"prompt\": \"Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.\",\n  \"max_tokens\": 100,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "197", "num_tokens": "48", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-debug-trace-id": "61cf7ec55050274cbc1cafe9d9151b91", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "40", "x-trial-endpoint-call-remaining": "39", "date": "<PERSON><PERSON>, 08 Jul 2025 17:17:53 GMT", "content-length": "446", "x-envoy-upstream-service-time": "728", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "13d56ecc-c3f8-4906-8ca5-ef24bee8e420", "generations": [{"id": "5592a5f8-bc67-4361-923c-0cde56f95dea", "text": " Hello! I'm doing well, and I'm ready to assist you. How can I help you today? ", "finish_reason": "COMPLETE"}], "prompt": "Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.", "meta": {"api_version": {"version": "1"}, "billed_units": {"input_tokens": 24, "output_tokens": 24}}}, "responseTime": 1148}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "9BBOxX639dYl5z6SMRxaM"}, {"description": "Response should contain generations", "status": "pass", "uid": "Z7mdaMB-eZz4qYM2doB-g"}, {"description": "Response should contain text", "status": "pass", "uid": "fd0zU3qYf4irdD8R_yC5y"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.203913958, "name": "Cohere API Health Check", "path": "collections/cohere-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/chat-test.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/chat", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"message\": \"Hello! Tell me about <PERSON><PERSON>'s capabilities in 2 sentences.\",\n  \"max_tokens\": 100,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "2636", "num_tokens": "76", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-debug-trace-id": "eb16b0adae4f3512f2f107aad9eff845", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "10", "x-trial-endpoint-call-remaining": "9", "date": "<PERSON><PERSON>, 08 Jul 2025 17:17:54 GMT", "content-length": "1184", "x-envoy-upstream-service-time": "752", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"response_id": "26ead70f-3604-4870-bbcf-e90ba1aa1546", "text": "Cohere provides advanced natural language processing (NLP) models designed to understand and generate human-like text, enabling businesses to build intelligent applications for tasks like summarization, classification, and conversational AI. Its models are fine-tuned for efficiency and scalability, making them accessible via APIs for seamless integration into diverse workflows.", "generation_id": "d19fa3f8-2d1b-44d6-b61b-ae32b85bb18f", "chat_history": [{"role": "USER", "message": "Hello! Tell me about <PERSON><PERSON>'s capabilities in 2 sentences."}, {"role": "CHATBOT", "message": "Cohere provides advanced natural language processing (NLP) models designed to understand and generate human-like text, enabling businesses to build intelligent applications for tasks like summarization, classification, and conversational AI. Its models are fine-tuned for efficiency and scalability, making them accessible via APIs for seamless integration into diverse workflows."}], "finish_reason": "COMPLETE", "meta": {"api_version": {"version": "1"}, "billed_units": {"input_tokens": 14, "output_tokens": 62}, "tokens": {"input_tokens": 509, "output_tokens": 64}}}, "responseTime": 1044}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "wn8JtN0Mv6pDzOpsnnqWF"}, {"description": "Response should contain text", "status": "pass", "uid": "f3GspSnjgRjTHrso6Jb2v"}, {"description": "Response should contain generation metadata", "status": "pass", "uid": "6xqu-MDRenktGfEwq_ccF"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.057428583, "name": "Cohere API Chat Test", "path": "collections/cohere-api/chat-test", "iterationIndex": 0}]}]