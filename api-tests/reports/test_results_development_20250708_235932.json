[{"iterationIndex": 0, "summary": {"totalRequests": 2, "passedRequests": 1, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 1, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 2, "passedTests": 2, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/claude-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "headers": {"Content-Type": "application/json", "x-api-key": "************************************************************************************************************", "anthropic-version": "2023-06-01"}, "data": "{\n  \"model\": \"claude-3-haiku-20240307\",\n  \"max_tokens\": 100,\n  \"messages\": [\n    {\n      \"role\": \"user\", \n      \"content\": \"Hello! This is a health check from HSN Bruno tests. Please respond with a simple confirmation.\"\n    }\n  ]\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"date": "<PERSON><PERSON>, 08 Jul 2025 15:59:38 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "anthropic-ratelimit-input-tokens-limit": "50000", "anthropic-ratelimit-input-tokens-remaining": "50000", "anthropic-ratelimit-input-tokens-reset": "2025-07-08T15:59:37Z", "anthropic-ratelimit-output-tokens-limit": "10000", "anthropic-ratelimit-output-tokens-remaining": "10000", "anthropic-ratelimit-output-tokens-reset": "2025-07-08T15:59:37Z", "anthropic-ratelimit-requests-limit": "50", "anthropic-ratelimit-requests-remaining": "49", "anthropic-ratelimit-requests-reset": "2025-07-08T15:59:36Z", "anthropic-ratelimit-tokens-limit": "60000", "anthropic-ratelimit-tokens-remaining": "60000", "anthropic-ratelimit-tokens-reset": "2025-07-08T15:59:37Z", "request-id": "req_011CQuq4A6PEuvMteBZVUfH4", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "anthropic-organization-id": "0b819686-9153-481c-bcf3-22aaa36b4309", "via": "1.1 google", "cf-cache-status": "DYNAMIC", "x-robots-tag": "none", "server": "cloudflare", "cf-ray": "95c0ca1fefdc19fe-KIX"}, "data": {"id": "msg_01CqZ2Nw4yiJ2A2uxbx6pxH7", "type": "message", "role": "assistant", "model": "claude-3-haiku-20240307", "content": [{"type": "text", "text": "Confirmed."}], "stop_reason": "end_turn", "stop_sequence": null, "usage": {"input_tokens": 27, "cache_creation_input_tokens": 0, "cache_read_input_tokens": 0, "output_tokens": 6, "service_tier": "standard"}}, "responseTime": 4060}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "8EGeiLKLiTGgdAFikZYHJ"}, {"description": "Response should contain content", "status": "pass", "uid": "VatdXZ8W7yV98bfrgROI8"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 4.115785958, "name": "Claude API Health Check", "path": "collections/claude-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/hsn-health/app-health.bru"}, "request": {"method": "GET", "url": "http://localhost:8787/api/health", "headers": {}}, "response": {"status": "error", "statusText": null, "headers": null, "data": null, "responseTime": 0}, "error": "connect ECONNREFUSED ::1:8787", "status": "error", "assertionResults": [], "testResults": [], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.00354925, "name": "HSN Application Health Check", "path": "collections/hsn-health/app-health", "iterationIndex": 0}]}]