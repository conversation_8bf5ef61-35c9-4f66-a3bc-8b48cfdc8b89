[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 1, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 2, "passedTests": 2, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/instantdb-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.instantdb.com/admin/query", "headers": {"Content-Type": "application/json", "app-id": "a24fb821-52fb-4b35-b9bf-35d561508a68"}, "data": "{\n  \"query\": {\n    \"health\": {}\n  }\n}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-type": "application/json; charset=utf-8", "content-length": "175", "connection": "keep-alive", "date": "Tu<PERSON>, 08 Jul 2025 16:03:55 GMT", "x-cache": "Error from cloudfront", "via": "1.1 06dea94a9acccc89bf073f5b6e5408ea.cloudfront.net (CloudFront)", "x-amz-cf-pop": "NRT57-P2", "alt-svc": "h3=\":443\"; ma=86400", "x-amz-cf-id": "uPAphZGzMEYlorBz4rS1AwUQfwnJn2cASMdP5z5rYHPbe_L6nGUxmg=="}, "data": {"type": "param-missing", "message": "Missing parameter: [\"headers\" \"authorization\"]", "hint": {"in": ["headers", "authorization"]}, "trace-id": "9914ae8e9e2b447d740dcdec07a4fe43"}, "responseTime": 490}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be valid API response", "status": "pass", "uid": "KOh82Xhv8DExFAGsy5UWY"}, {"description": "Response should be JSON", "status": "pass", "uid": "rb-lkDACuj0fH1HJ97FRW"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.548754167, "name": "InstantDB API Health Check", "path": "collections/instantdb-api/health-check", "iterationIndex": 0}]}]