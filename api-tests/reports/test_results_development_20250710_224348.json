[{"iterationIndex": 0, "summary": {"totalRequests": 9, "passedRequests": 9, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 27, "passedTests": 27, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/cohere-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"prompt\": \"Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.\",\n  \"max_tokens\": 100,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "240", "num_tokens": "52", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-api-warning": "model 'command' is deprecated. Please consider upgrading to a newer model to avoid future service disruptions", "x-debug-trace-id": "9494dce0dd4046def6688ac37366de8f", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "40", "x-trial-endpoint-call-remaining": "39", "date": "Thu, 10 Jul 2025 14:43:50 GMT", "content-length": "614", "x-envoy-upstream-service-time": "858", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "01ba36b8-ec14-449a-8344-9efef1fe5010", "generations": [{"id": "a92eb98f-8bc7-48d6-b81c-1455077e38b8", "text": " Hello! I am working and ready to respond. Please go ahead and provide me with any questions or requests you have for me. ", "finish_reason": "COMPLETE"}], "prompt": "Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.", "meta": {"api_version": {"version": "1"}, "warnings": ["model 'command' is deprecated. Please consider upgrading to a newer model to avoid future service disruptions"], "billed_units": {"input_tokens": 24, "output_tokens": 28}}}, "responseTime": 1538}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "sM_6vmoa_52JqzegbdB-G"}, {"description": "Response should contain generations", "status": "pass", "uid": "6R_pl3Su-bPHcHzNHR46E"}, {"description": "Response should contain text", "status": "pass", "uid": "UFVI5dyNo4t3pWt1WnM97"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.5983872909999999, "name": "Cohere API Health Check", "path": "collections/cohere-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/chat-test.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/chat", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"message\": \"Hello! Tell me about <PERSON><PERSON>'s capabilities in 2 sentences.\",\n  \"max_tokens\": 100,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "2636", "num_tokens": "91", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-debug-trace-id": "4e5294e563255c6ef3b9c5833edfad03", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "10", "x-trial-endpoint-call-remaining": "9", "date": "Thu, 10 Jul 2025 14:43:52 GMT", "content-length": "1308", "x-envoy-upstream-service-time": "856", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"response_id": "9acce9a3-99b6-4800-85a2-6ab141eff98f", "text": "Cohere provides advanced natural language processing models that enable developers and businesses to build intelligent applications, offering capabilities like text generation, summarization, classification, and embedding, all powered by state-of-the-art machine learning. Its APIs are designed to be easily integrated, scalable, and customizable, making it accessible for a wide range of use cases, from customer support to content creation.", "generation_id": "e48926d0-2b32-4103-87d2-753dc5e6da70", "chat_history": [{"role": "USER", "message": "Hello! Tell me about <PERSON><PERSON>'s capabilities in 2 sentences."}, {"role": "CHATBOT", "message": "Cohere provides advanced natural language processing models that enable developers and businesses to build intelligent applications, offering capabilities like text generation, summarization, classification, and embedding, all powered by state-of-the-art machine learning. Its APIs are designed to be easily integrated, scalable, and customizable, making it accessible for a wide range of use cases, from customer support to content creation."}], "finish_reason": "COMPLETE", "meta": {"api_version": {"version": "1"}, "billed_units": {"input_tokens": 14, "output_tokens": 77}, "tokens": {"input_tokens": 509, "output_tokens": 79}}}, "responseTime": 1268}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "pass", "uid": "Vw6SRniJ-hhIsNp5MDd6J"}, {"description": "Response should contain text", "status": "pass", "uid": "1VzWDVvHny6GvR2IBdLEF"}, {"description": "Response should contain generation metadata", "status": "pass", "uid": "hWsk1BBy4mdNYzO9Bo8Ry"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.282694792, "name": "Cohere API Chat Test", "path": "collections/cohere-api/chat-test", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/embeddings-test.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/embed", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"texts\": [\"Hello from HSN Bruno test!\", \"API health check\"],\n  \"model\": \"embed-english-v3.0\",\n  \"input_type\": \"search_document\"\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "42", "num_tokens": "10", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-debug-trace-id": "c75231ac8c6b3eb9e61887a961b75dad", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "100", "x-trial-endpoint-call-remaining": "99", "date": "Thu, 10 Jul 2025 14:43:52 GMT", "x-envoy-upstream-service-time": "43", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000", "transfer-encoding": "chunked"}, "data": {"id": "a5de0a5f-6056-4a96-ac32-e6c52a9f1096", "texts": ["hello from hsn bruno test!", "api health check"], "embeddings": [[-0.046325684, -0.02217102, -0.03225708, 0.018356323, -0.03857422, -0.**********, -0.006526947, 0.010139465, -0.041900635, 0.05859375, 0.035949707, 0.009407043, 0.07647705, -0.058624268, 0.043823242, 0.**********, 0.020523071, 0.**********, 0.**********, 0.00016129017, -0.03265381, 0.034088135, -0.**********, 0.**********, 0.03060913, 0.010604858, -0.02861023, 0.006351471, -0.019622803, -0.019042969, 0.00724411, 0.020111084, -0.**********, 0.014945984, 0.025543213, 0.06414795, -0.01940918, 0.011123657, 0.026550293, 0.**********, 0.014572144, -0.010025024, 0.0206604, -0.045318604, 0.010368347, -0.026153564, 0.033050537, -0.044952393, 0.025817871, -0.014923096, -0.016830444, -0.014877319, -0.**********, 0.06237793, -0.027526855, -0.032104492, -0.011482239, 0.034423828, 0.020568848, -0.032989502, -0.008621216, 9.948015e-05, 0.038726807, 0.05316162, -0.008857727, 0.012741089, -0.010879517, -0.012283325, 0.017105103, 0.001244545, 0.011436462, -0.020568848, 0.011421204, 0.011375427, -0.00894165, -0.035858154, 0.026153564, 0.01939392, 0.0020999908, 0.006023407, -0.046905518, -0.0093307495, -0.061798096, -0.02708435, 0.007659912, 0.035705566, -0.031066895, -0.0007677078, 0.006416321, -0.03164673, -0.020370483, 0.023712158, -0.021255493, 0.008529663, -0.020431519, -0.009056091, 0.020629883, -0.01084137, -0.007850647, -0.011367798, -0.0234375, -0.016906738, -0.03665161, 0.011413574, -0.026184082, -0.042938232, 0.044189453, 0.0184021, -0.001490593, 0.022537231, -0.06262207, 0.037719727, 0.01713562, -0.018600464, -0.017196655, -0.012512207, -0.0065345764, 0.023590088, 4.2915344e-05, 0.006038666, -0.009635925, 0.034423828, 0.008491516, 0.024887085, -0.012458801, 0.026138306, 0.014259338, -0.040283203, 0.05154419, -0.025802612, -0.0045280457, 0.028274536, -0.091552734, -0.042297363, -0.06317139, 0.021896362, -0.009864807, -0.016418457, 0.032196045, -0.046661377, 0.059570312, 0.0037937164, 0.007801056, 0.038146973, -0.022506714, 0.016281128, 0.024810791, 0.008926392, -0.006652832, -0.03665161, -0.007820129, -0.004337311, -0.023208618, 0.022079468, 0.07513428, 0.0569458, 0.0061836243, 0.02557373, 0.016708374, 0.08239746, 0.005519867, -0.008346558, -0.0041923523, -0.012550354, -0.031433105, 0.011741638, 0.039978027, 0.0007300377, 0.007369995, 0.08898926, -0.06036377, 0.016082764, -0.010772705, 0.00012767315, -0.0063095093, 0.010894775, -0.031555176, 0.028915405, 0.014099121, -0.05368042, -0.01373291, -0.038024902, 0.007205963, 0.021377563, -0.013969421, -0.017028809, 0.009262085, 0.009384155, 0.021636963, 0.0021648407, 0.013092041, -0.004055023, 0.008575439, -0.04031372, 0.0027656555, 0.0004901886, -0.058502197, -0.013595581, -0.0016317368, 0.020492554, -0.054260254, 0.03378296, 0.014579773, 0.06402588, 0.045135498, 0.041748047, 0.005706787, 0.023345947, 0.09283447, -0.008422852, -0.055236816, -0.032165527, 0.04812622, -0.09124756, -0.09222412, -0.009025574, -0.047332764, -0.023849487, -0.00018942356, 0.044921875, 0.0014476776, 0.009925842, -0.028503418, -0.04071045, -0.04525757, -0.007080078, -0.024963379, 0.0016593933, -0.061645508, 0.008018494, -0.0105896, 0.027542114, -0.041107178, -0.039489746, -0.06021118, -0.0037231445, 0.0184021, 0.07336426, 0.0010547638, -0.0017223358, 0.0119018555, -0.01687622, -0.012969971, 0.0042495728, 0.055023193, 0.06933594, 0.023742676, 0.0027313232, -0.0016069412, 0.006717682, -0.024215698, -0.014640808, 0.032196045, 0.018035889, 0.0020389557, 0.039398193, -0.0074386597, 0.042266846, -0.052825928, 0.041137695, -0.031555176, 0.031829834, 0.034942627, -0.0076560974, -0.020767212, -0.039855957, -0.031982422, 0.0027618408, 0.0041275024, -0.018814087, 0.018798828, -0.03326416, 0.0015172958, 0.058166504, 0.08721924, 0.051330566, -0.04852295, -0.0513916, 0.020263672, 0.058258057, -0.021484375, 0.0236969, 0.022354126, -0.008323669, 0.00051784515, 0.009002686, 0.022628784, -0.03387451, -0.010719299, 0.009613037, 0.02381897, 0.045837402, -0.010612488, 0.060333252, -0.012550354, 0.023147583, -0.0062446594, 0.0068206787, 0.02973938, -0.040771484, -0.0317688, 0.026107788, -0.022613525, 0.020950317, 0.0027313232, -0.013832092, 0.030410767, -0.00819397, -0.009231567, -0.023773193, 0.02027893, 0.033996582, 0.006504059, 0.00422287, 0.0033073425, -0.025817871, -0.018707275, -0.0010080338, -0.033843994, 0.005207062, -0.023101807, 0.06542969, -0.043518066, -0.039733887, 0.029708862, 0.021514893, -0.10168457, -0.0138168335, -0.02947998, -0.031158447, 0.0013074875, -0.01399231, -0.009887695, 0.037322998, 0.02192688, 0.0231781, 0.019332886, -0.06561279, -0.016830444, 0.019897461, 0.018035889, -0.05630493, 0.0047683716, -0.029159546, -0.012428284, -0.027709961, -0.040496826, -0.011016846, -0.012199402, -0.020965576, 0.033233643, -0.003540039, -0.015052795, -0.0025730133, 0.011009216, -0.056365967, -0.006587982, 0.036224365, 0.012390137, -0.01600647, 0.019119263, 0.04940796, 0.011924744, -0.03237915, -0.037597656, -0.051849365, 0.033477783, -0.008087158, -0.007358551, -0.038970947, 0.017227173, -0.057434082, 0.0259552, -0.008476257, -0.008148193, -0.0068473816, -0.04031372, -0.06341553, 0.02810669, -0.015731812, -0.031585693, -0.026504517, 0.07092285, 0.010574341, -0.0010385513, 0.024490356, -0.007484436, 0.035247803, 0.007080078, -0.0006518364, -0.0049552917, 0.05441284, -0.002790451, -0.015991211, 0.026657104, 0.0023975372, -0.06185913, 0.017333984, -0.07904053, 0.029724121, -0.0023288727, -0.016921997, 0.013923645, -0.0357666, -0.01852417, 0.004486084, -0.02041626, -0.003982544, 0.023269653, 0.041503906, 0.026138306, -0.022628784, 0.025924683, -0.030426025, 0.029296875, -0.002817154, -0.030776978, -0.0012159348, -0.019561768, 0.027908325, 0.013046265, 0.003200531, -0.032287598, -0.014915466, 0.005973816, 0.008239746, 0.029922485, 0.0030498505, 0.008415222, -0.018005371, 0.017318726, -0.048706055, -0.016021729, -0.041870117, -0.029037476, 0.018356323, -0.027130127, 0.017654419, -0.013053894, -0.011039734, -0.057861328, 6.1154366e-05, 0.032348633, 0.00178051, 0.023864746, -0.013542175, 0.018417358, -0.020523071, -0.072143555, 0.0031490326, -0.024429321, 0.018798828, -0.033233643, 0.0362854, -0.0036182404, 0.02267456, 0.009666443, 0.03552246, -0.0064849854, 0.011993408, 0.0068626404, -0.015380859, -0.005256653, 0.0010986328, -0.05960083, 0.028900146, 0.0014877319, 0.01109314, -0.036468506, -0.0027637482, 0.0010299683, 0.017227173, 0.05670166, 0.003736496, 5.0127506e-05, 0.009719849, -0.025726318, -0.035827637, 0.0044822693, 0.028533936, -0.026260376, 0.015594482, -0.017654419, -0.042938232, -0.0006723404, 0.012046814, -0.0463562, -0.03338623, 0.0016841888, 0.019607544, -0.015037537, -0.031219482, 0.045898438, -0.056274414, -0.036987305, 0.044403076, -0.011451721, 0.027282715, -0.015640259, -0.04864502, -0.034484863, -0.01272583, 0.02468872, 0.0014390945, 0.00762558, -0.024749756, 0.00087690353, 0.029037476, 0.03463745, 0.0010604858, 0.0289917, 0.002380371, 0.0524292, 0.020950317, -0.01663208, 0.08105469, -0.011405945, 0.0019760132, 0.024169922, -0.013343811, 0.036102295, 0.041778564, -0.009773254, 0.007133484, 0.016403198, 0.0084991455, -0.023132324, 0.032104492, 0.009353638, -0.08917236, 0.041259766, 0.026626587, -0.0030288696, 0.03262329, -0.031585693, 0.052764893, -0.024032593, -0.029586792, 0.008277893, 0.0029716492, 0.02003479, 0.015335083, -0.036712646, 0.014846802, 0.006477356, -0.014656067, -0.0073242188, 0.04324341, -0.02368164, 0.05831909, 0.003025055, -0.019241333, -0.009498596, -0.0119018555, -0.0036392212, -0.007209778, -0.022079468, -0.014511108, -0.013114929, 0.03540039, 0.018188477, -0.020462036, -0.044525146, 0.04006958, -0.03225708, 0.007255554, 0.00057554245, -0.03463745, -0.0017232895, -0.0826416, -0.021469116, -0.05126953, -0.016021729, 0.042785645, 0.02432251, 0.046447754, 0.034240723, 0.017791748, 0.008872986, -0.035247803, 0.014198303, -0.0010471344, 0.0036392212, 0.0008201599, 0.010238647, 0.040893555, -0.043060303, 0.013435364, 0.0014743805, 0.030456543, -0.009239197, -0.02998352, -0.012176514, -0.005874634, 0.044403076, -0.0017004013, 0.07751465, -0.055389404, -0.03201294, 0.009590149, -0.028579712, 0.027267456, 0.014350891, -0.031097412, -0.0051841736, 0.0102005005, -0.00025725365, -0.0047187805, 0.02229309, -0.05831909, -0.000746727, -0.022476196, -0.045135498, 0.06793213, -0.06933594, -0.03604126, 0.017211914, -0.0026187897, -0.031341553, -0.043151855, -0.008850098, -0.0637207, -0.0107803345, 0.024353027, 0.0127334595, -0.040802002, -0.04272461, -0.034484863, -0.0058403015, -0.016403198, -0.019851685, -0.008361816, -0.0011882782, -0.035888672, 0.03387451, 0.05026245, -0.0066986084, 0.012367249, -0.035217285, 0.00024461746, 0.0004210472, 0.012268066, -0.00054740906, -0.007575989, 0.0051841736, -0.016860962, -0.011894226, 0.008872986, -0.03616333, -0.004245758, -0.035339355, -0.02444458, 0.0053253174, -0.021057129, 0.0054626465, -0.027618408, -0.035583496, 0.066101074, -0.023803711, 0.02067566, -0.0031871796, 0.061065674, 0.0072402954, 0.0015945435, -0.016799927, 0.015403748, 0.06359863, -0.00097608566, 0.03982544, -0.078430176, -0.005859375, 0.005756378, 0.012199402, -0.03564453, -0.00944519, 0.012176514, 0.0056648254, 0.022094727, 0.04058838, -0.04095459, -0.0038490295, 0.021133423, 0.036987305, 0.02748108, -0.042114258, 0.05557251, 0.01689148, -0.033935547, -0.0030174255, -0.04232788, -0.024963379, 0.03543091, -0.031707764, 0.0013208389, 0.01953125, -0.013893127, -0.044006348, -0.060150146, 0.0113220215, 0.015380859, 0.018127441, -0.049835205, 0.0063934326, -0.05923462, -0.0039711, -0.028961182, 0.00522995, -0.0096588135, 0.025039673, -0.0034236908, -0.0077781677, 0.020339966, -0.049468994, -0.03111267, 0.04043579, -0.0027046204, 0.010856628, 0.04537964, 0.0067863464, -0.05886841, -0.049987793, -0.01373291, 0.037139893, -0.025482178, 0.030075073, -0.013114929, -0.032196045, -0.053527832, -0.003829956, 0.007209778, 0.019058228, 0.039916992, 0.006893158, -0.007041931, -0.013458252, -0.008148193, 0.012260437, -0.010787964, -0.029586792, 0.036956787, -0.05303955, 0.025054932, 0.0033283234, 0.052246094, 0.018981934, 0.036254883, 0.01134491, 0.075927734, 0.01399231, 0.02520752, 0.0317688, 0.00969696, -0.038726807, -0.02255249, -0.00021100044, -0.0463562, -0.050476074, 0.013763428, -0.015197754, 0.010543823, 0.022781372, -0.011138916, -0.0051651, 0.037078857, -0.03527832, 0.051971436, -0.037597656, -0.062561035, -0.012161255, 0.0008010864, 0.019134521, 0.036499023, -0.023849487, -0.03591919, -0.016845703, -0.00762558, -0.0032539368, -0.011665344, -0.0037078857, 0.033050537, 0.020339966, 0.022354126, -0.044952393, -0.0149383545, 0.0006842613, 0.017288208, 0.040222168, -0.012237549, -0.002216339, 0.016647339, -0.01449585, 0.027435303, 0.018554688, 0.008216858, 0.009880066, 0.0074272156, 0.038360596, -0.0015249252, 0.0056610107, -0.006755829, -0.021392822, -0.022949219, 0.04144287, 0.06549072, 0.017623901, -0.034820557, -0.0079422, 0.07019043, 0.018661499, 0.028549194, 0.001115799, -0.007873535, 0.00907135, 0.011108398, 0.009864807, 0.039764404, 0.0028629303, -0.008758545, 0.026947021, -0.0038909912, -0.0047454834, 0.011817932, -0.010482788, 0.023895264, -0.035888672, -0.0003142357, 0.023468018, -0.014587402, 0.0030498505, 0.06555176, 0.025344849, 0.0024814606, -0.06500244, -0.018447876, 0.014282227, -0.004676819, 0.01424408, -0.091308594, 0.093688965, 0.006000519, 0.05429077, -0.0335083, 0.06341553, 0.017913818, 0.009887695, -0.0063476562, 0.009170532, -0.039031982, 0.02507019, 0.07598877, 0.03817749, 0.042236328, 0.029373169, 0.0066833496, -0.005092621, 0.027450562, -0.019973755, -0.03010559, -0.04360962, -0.0385437, -0.033050537, 0.01675415, 0.014846802, 0.03074646, 0.04953003, -0.032348633, -0.016281128, -0.0047187805, -0.062805176, -0.033233643, 0.010757446, 0.034606934, 0.05899048, 0.019454956, 9.1433525e-05, 0.0154418945, 0.04058838, -0.043792725, 0.041412354, -0.005004883, 0.01902771, -0.059143066, -0.006816864, -0.023422241, -0.027114868, -0.024459839, 0.029266357, -0.01878357, -0.03765869, -0.019989014, -0.0011634827, -0.008331299, 0.028961182, 0.015533447, -0.018051147, -0.01966858, 0.032196045, 0.0028896332, -0.04525757, 0.0042304993, -0.021835327, -0.05319214, 0.078125, -0.03982544, 0.011421204, 0.0109939575, 0.024276733, 0.0121536255, -0.018127441, 0.003610611, 0.066833496, 0.028503418, 0.021469116, 0.056365967, -0.020401001, -0.0035495758, 0.020217896, 0.009941101, 0.060150146, -0.051879883, -0.013145447, -0.019012451, -0.0087509155, -0.03479004, -0.0059127808, 0.036865234, -0.046936035, -0.05328369, -0.020767212, -0.009254456, -0.015930176, -0.038604736, -0.10888672, 0.047088623, -0.02494812, -0.035736084, -0.02168274, -0.038360596, 0.031280518, -0.046936035, -0.036346436, -0.0016784668, -0.081726074, 0.013404846, -0.061279297, 0.007083893, -0.02279663, -0.018218994, 0.010070801, 0.034820557, 0.054351807, -0.05105591, 0.03289795, -0.02180481, 0.017684937, 0.032348633, -0.04107666, -0.025604248, -0.043029785, -0.005065918, 0.02268982, -0.0008611679, 0.03604126, 0.025161743, -0.074157715, 0.0037517548, -0.023086548, -0.015808105, 0.07165527, 0.010498047, 0.00737381, -0.014335632, -0.03704834, 0.013771057, -0.020217896, -0.026947021, 0.02218628, 0.01386261, -0.007499695, 0.025924683, -0.02305603, 0.075805664, 0.0016269684, -0.01537323, -0.020721436, 0.033050537, -0.05331421, -0.04623413, -0.0034103394, 0.024124146, -0.023956299, -0.033416748, -0.0016469955, -0.030776978, 0.050231934, 0.0052948, 0.023834229, -0.029785156, 0.045288086, -0.012229919, 0.003501892, -0.01626587, 0.014457703, -0.03463745, -0.0154800415, -0.009300232, -0.031066895, 0.006778717, 0.01940918, -0.04107666, -0.008758545, 0.036590576, -0.03414917, -0.03302002, -0.0141067505, -0.05441284, 0.009788513, -0.053100586, 0.005268097, -0.036621094, 0.03945923, -0.0107421875, -0.044799805, 0.039978027], [0.023834229, 0.016479492, -0.04663086, -0.08355713, -0.02532959, -0.015571594, -0.0637207, 0.011314392, -0.050689697, 0.060913086, -0.049743652, 0.01977539, 0.018112183, -0.0027313232, 0.021728516, -0.038848877, 0.055999756, -0.026550293, 0.004547119, -0.0025310516, -0.01398468, 0.0524292, -0.026107788, -0.011360168, -0.037841797, 0.030197144, -0.009513855, -0.01876831, -0.041015625, -0.044891357, -0.022827148, 0.045074463, -0.01020813, 0.022506714, 0.011810303, 0.076538086, 0.0006489754, 0.023330688, 0.030395508, 0.010604858, 0.026367188, -0.00730896, 0.016159058, -0.0541687, -0.025344849, 0.008644104, 0.020080566, 0.02281189, 0.0033111572, 0.0049362183, -0.010787964, -0.035949707, -0.0021362305, -0.00381279, -0.01713562, -0.022506714, 0.0027389526, -0.04486084, 0.022354126, -0.027893066, -0.03829956, -0.0027866364, 0.062164307, 0.010025024, -0.021438599, -0.016738892, -0.0047454834, 0.0010690689, 0.009628296, -0.0096588135, -0.00605011, -0.013534546, 0.0056495667, 0.028198242, 0.0014228821, -0.066833496, 0.039031982, 0.018859863, -0.0075035095, 0.0077667236, -0.009460449, -0.00056505203, 0.006580353, -0.032440186, 0.0042877197, -0.005504608, 0.006542206, -0.0141067505, 0.033203125, -0.031021118, 0.0044822693, 0.023544312, -0.014793396, 0.09118652, -0.0071907043, -0.0007095337, 0.040130615, 0.0059432983, -0.009216309, 0.0016078949, -0.0135269165, -0.04788208, 0.020126343, -0.027328491, -0.009788513, -0.0019187927, 0.033172607, 0.042144775, 0.038146973, 0.0309906, -0.011726379, 0.07836914, 0.0064697266, 0.014343262, -0.031921387, -0.0040130615, 0.005001068, -0.05581665, -0.025634766, -0.074401855, 0.005077362, 0.032287598, 0.01763916, -0.025482178, 0.014945984, 0.0017251968, -0.030166626, -0.03930664, -0.015014648, 0.018127441, -0.021331787, 0.02406311, -0.09503174, -0.032165527, -0.027297974, -0.010635376, -0.03894043, -0.03302002, -0.021499634, -0.030838013, 0.03717041, -0.011276245, 0.014984131, 0.021499634, -0.020599365, 0.026626587, 0.019302368, -0.0021705627, 0.0024967194, -0.051330566, -0.024047852, 0.00013041496, -0.024795532, 0.06439209, 0.00010609627, 0.053955078, -0.034942627, 0.027267456, 0.020309448, 0.05380249, 0.0033607483, -0.004749298, 0.0082473755, -0.038238525, -0.039123535, 0.0095825195, -0.009605408, 0.016693115, 0.012969971, 0.037078857, -0.01927185, 0.016647339, -0.022399902, -0.010421753, -0.0072631836, -0.019622803, 0.0053367615, 0.04510498, 0.017837524, -0.03543091, -0.017318726, -0.038024902, 0.031555176, -0.008171082, -0.02545166, 0.019042969, -0.016784668, 0.0069236755, 0.011604309, 0.0050354004, 0.010650635, 0.04537964, -0.025238037, 0.010795593, -0.022369385, -0.03302002, 0.017745972, 0.00078344345, -0.001083374, 0.02192688, -0.015914917, 0.07952881, -0.023010254, 0.026443481, -0.0072135925, 0.037963867, 0.0049476624, 0.019714355, 0.019882202, 0.047821045, -0.009483337, -0.032318115, 0.021102905, -0.026046753, -0.011238098, -0.055419922, -0.021362305, -0.04473877, -0.01134491, -0.**********, 0.012290955, 0.044403076, -0.020645142, 0.013145447, -0.008872986, 0.015472412, -0.008094788, 0.0036754608, 0.018753052, -0.0047569275, 0.08392334, -0.003528595, 0.022888184, 0.017471313, -0.02859497, -0.006175995, 0.03253174, -0.04675293, 0.023788452, 0.034820557, 0.016799927, 0.021987915, -0.006542206, 0.025253296, 0.037506104, 0.005706787, 0.015106201, -0.0058135986, 0.0072517395, 0.06317139, -0.03579712, 0.024353027, 0.024276733, 0.06341553, -0.01272583, 0.049987793, -0.013053894, -0.035125732, -0.04208374, 0.04437256, 0.013084412, 0.027542114, -0.009902954, 0.0016555786, -0.031829834, -0.019592285, -0.022262573, 0.020507812, -0.027252197, -0.0058555603, 0.03768921, 0.010673523, -0.02508545, 0.04611206, 0.07952881, -0.00970459, -0.0413208, -0.012573242, -0.00762558, 0.024810791, 0.00422287, 0.021881104, 0.021499634, 0.01600647, 0.02218628, 0.00774765, 0.014984131, 0.013618469, -0.025543213, 0.005470276, 0.02986145, 0.038024902, 0.02053833, 0.0059814453, 0.04333496, 0.016723633, 0.024353027, 0.021957397, 0.003774643, -0.016433716, -0.02810669, 0.030166626, -0.022659302, 0.021255493, -0.011650085, 0.00945282, 0.0022354126, -0.010574341, -0.020111084, -0.04107666, 0.044311523, 0.06347656, 0.016983032, -0.0020694733, 0.013671875, -0.037353516, -0.001329422, -0.028167725, -0.1003418, -0.08703613, 0.0062408447, 0.04559326, -0.00093364716, -0.040039062, 0.038909912, -0.0317688, -0.06390381, -0.010604858, -0.04559326, -0.017929077, -0.01890564, -0.044555664, -0.012619019, 0.006298065, -0.0012807846, -0.011543274, 0.012756348, 0.07861328, 0.0042495728, -0.06439209, -0.011238098, -0.039093018, -0.016845703, 0.0027675629, 0.00242424, -0.022720337, -0.007083893, 0.006793976, -0.031173706, 0.009216309, 0.025100708, -0.019470215, -0.009429932, 0.008285522, 0.036712646, -0.011276245, -0.0064315796, 0.06726074, 0.06866455, -0.00982666, 0.032104492, -0.01210022, -0.008178711, 0.024810791, 0.012435913, -0.067871094, 0.0053482056, -0.0071640015, -0.008506775, 0.012237549, -0.014663696, -0.0023117065, 0.031677246, -0.0007624626, -0.041412354, -0.011268616, -0.023513794, -0.04827881, -0.05307007, -0.03857422, 0.015792847, -0.010948181, 0.07373047, -0.019470215, 0.031341553, -0.018692017, 0.013313293, 0.066101074, 0.040863037, 0.022384644, -0.002729416, 0.015159607, 0.013122559, -0.0045433044, 0.03829956, 0.001871109, -0.047424316, 0.023498535, -0.094177246, 0.029373169, 0.025253296, -0.032318115, -0.04067993, 0.0020694733, -0.043792725, -0.031402588, 0.0008072853, -0.026306152, -0.0009560585, 0.01751709, -0.017440796, 0.04550171, 0.04244995, -0.024169922, 0.036132812, -0.041625977, -0.031951904, 0.008300781, -0.021362305, 0.023757935, 0.03869629, -0.071899414, -0.030639648, -0.022964478, -0.017684937, 0.028915405, -0.008987427, 0.017288208, 0.022888184, -0.015327454, -0.023971558, -0.011367798, -0.020996094, -0.01852417, -0.0063323975, 0.019424438, -0.016540527, 0.0034751892, 0.0030136108, -0.004283905, -0.06964111, -0.03378296, 0.040771484, -0.034088135, 0.050842285, -0.03274536, -0.018585205, -0.026367188, -0.042175293, -0.01878357, -0.039215088, 0.008293152, -0.02456665, 0.02494812, -0.02178955, 0.006095886, 0.005306244, 0.039215088, -0.00223732, 0.032318115, 0.019958496, -0.015457153, 0.009643555, 0.0042381287, -0.0579834, 0.00522995, 0.041809082, 0.03390503, 0.0046958923, -0.028686523, 0.0134887695, 0.035888672, -0.031158447, -0.018600464, -0.016403198, -0.010803223, 0.011779785, -0.008613586, 0.05239868, -0.010765076, -0.015510559, 0.0054473877, 0.049682617, -0.029693604, -0.029586792, 0.023895264, -0.047210693, 0.017242432, 0.0390625, -0.07220459, -0.04159546, -0.05154419, 0.042785645, -0.10498047, -0.01550293, -0.040527344, -0.011779785, 0.007091522, -0.010803223, -0.06591797, -0.024658203, -0.036071777, -0.0390625, 0.030593872, -0.0031871796, 0.00019907951, -0.020004272, -0.010299683, 0.025802612, 0.006412506, -0.025314331, -0.033721924, 0.00053977966, -0.021057129, -0.037994385, 0.033172607, -0.027450562, -0.02029419, -0.009590149, 0.013847351, 0.028396606, 0.005859375, 0.015365601, 0.02357483, 0.033569336, 0.0032520294, -0.025131226, 0.007205963, -0.024139404, -0.118774414, -0.0009784698, 0.0038604736, -0.018005371, 0.05532837, 0.007007599, 0.06756592, 0.0061836243, -0.020004272, -0.018341064, -0.030273438, 0.014846802, -0.0070724487, -0.055664062, -0.036499023, -0.024002075, -0.018875122, -0.009048462, 0.080200195, 0.058258057, -0.0070533752, 0.08795166, 0.046691895, -0.015182495, -0.058166504, -0.05178833, 0.033813477, 0.022598267, 0.0059776306, -0.044799805, -0.016098022, 0.013771057, 0.010139465, -0.04586792, 0.026000977, -0.029663086, -0.018188477, 0.029434204, -0.018920898, -0.021438599, 0.005718231, -0.050079346, -0.020812988, -0.015991211, -0.023361206, -0.008728027, 0.0044822693, -0.01348114, 0.0107040405, 0.026275635, 0.024917603, 0.026138306, -0.0032444, -0.016189575, 0.013877869, -0.0005378723, 0.057861328, -0.050842285, 0.010635376, -0.0072517395, 0.03729248, 0.009246826, -0.0107803345, -0.008705139, -0.019515991, -0.0038013458, -0.036499023, 0.004852295, -0.025436401, 0.014190674, -0.0008006096, 0.004108429, -0.016326904, 0.0104599, -0.021484375, 0.020507812, -0.03933716, -0.017181396, 0.030136108, 0.008720398, -0.010902405, 0.006549835, -0.009902954, -0.048675537, 0.029953003, -0.03817749, 0.007648468, -0.026977539, 0.06829834, 0.05203247, 0.017288208, 0.05117798, -0.001033783, 0.032592773, -0.00705719, -0.038360596, -0.039886475, -0.019821167, -0.04006958, -0.010246277, 0.021697998, -0.016021729, 0.008995056, -0.026977539, -0.022781372, 0.011817932, 0.027069092, -0.011703491, -0.021087646, -0.027252197, 0.05154419, -0.022125244, 0.0075187683, -0.05407715, 0.06561279, 0.0054740906, -0.010467529, 0.026565552, -0.026611328, -0.01159668, -0.0012102127, -0.011978149, -0.009376526, 0.049438477, -0.047454834, -0.018249512, -0.011138916, 0.015914917, 0.071777344, -0.01398468, -0.029464722, 0.01979065, 0.053527832, 0.014785767, -0.03567505, -0.0046920776, -0.028182983, 0.05517578, 0.02798462, -0.003736496, -0.049072266, 0.023895264, -0.05154419, -0.00038862228, 0.009391785, -0.009933472, -0.007259369, 0.040863037, 0.0096206665, 0.009170532, -0.0066566467, 0.0022773743, 0.021896362, 0.039367676, -0.018554688, -0.02331543, 0.03591919, -0.0099487305, 0.020233154, -0.024261475, -0.043182373, -0.041503906, 0.039733887, -0.04928589, -0.0024223328, 0.0074310303, -0.010536194, 0.0023040771, 0.011230469, 0.0368042, -0.01789856, 0.010322571, 0.018569946, -0.0050849915, 0.008232117, -0.002313614, 0.01626587, -0.061706543, 0.014785767, -0.027648926, 0.02809143, 0.005176544, -0.005104065, -0.060760498, -0.017623901, 0.05307007, -0.03378296, -0.05126953, -0.01878357, -0.022491455, -0.011070251, -0.042510986, 0.0020332336, -0.030517578, -0.07287598, 0.015060425, -0.027893066, -0.047821045, -0.0063705444, 0.026947021, 0.003917694, 0.0008006096, -0.0008587837, 0.039398193, -0.027145386, -0.01084137, -0.014442444, 0.009231567, -0.021499634, -0.052215576, -0.02507019, -0.010124207, 0.011169434, 0.010635376, 0.010978699, 0.033843994, 0.04849243, 0.030471802, 0.038482666, 0.0116119385, 0.03225708, 0.051605225, -0.030883789, -0.031082153, 0.0231781, 0.014785767, 0.032409668, -0.02067566, -0.016433716, -0.018859863, 0.016525269, -0.056854248, -0.01725769, -0.0009665489, 0.042907715, -0.009941101, 0.017074585, -0.018127441, 0.028244019, 0.017837524, -0.002527237, 0.039398193, 0.057800293, -0.007167816, -0.031097412, -0.03829956, -0.0072669983, -0.0026168823, -0.024978638, -0.003238678, 0.010246277, 0.04269409, -0.021011353, 0.03717041, -0.027053833, -0.006969452, -0.006504059, 0.028015137, 0.022384644, -0.028320312, -0.017608643, 0.0061149597, 0.054595947, -0.0029201508, -0.015403748, 0.0057754517, 0.003479004, 0.0020389557, 0.00541687, 0.04812622, -0.003545761, -0.023391724, -0.046813965, 0.041412354, 0.03945923, 0.027435303, -0.034454346, 0.014762878, 0.061462402, 0.015930176, -0.0054244995, 0.010040283, -0.019699097, -0.042022705, 0.033294678, -0.0064201355, 0.054107666, -0.0055999756, -0.015510559, 0.008728027, 0.009719849, -0.009399414, 0.021621704, 0.028182983, 0.009506226, -0.017929077, 0.0068359375, 0.02355957, 0.0056877136, 0.0016565323, 0.045074463, 0.039123535, 0.005130768, 0.0016231537, 0.021957397, 0.03692627, 0.027328491, -0.022232056, -0.02859497, 0.012046814, 0.010513306, 0.010536194, -0.011222839, 0.017944336, 0.03286743, -0.08300781, 0.055877686, 0.027877808, -0.008514404, -0.015670776, 0.0635376, 0.048706055, 0.06604004, 0.05227661, -0.00032043457, 0.005344391, 0.011810303, 0.044677734, -0.026550293, -0.010795593, 0.038757324, -0.019134521, -0.0027637482, -0.02507019, 0.028579712, 0.04660034, 0.040527344, 0.015563965, 0.009315491, -0.008995056, 0.02319336, 0.0069007874, -0.00070238113, 0.10095215, -0.08239746, -0.043121338, 0.028686523, -0.008338928, -0.07910156, -0.04748535, 0.12646484, -0.027175903, -0.015235901, 0.0040740967, -0.010375977, -0.027694702, -0.019500732, -0.0055503845, 0.018051147, -0.0028705597, 0.0064048767, 0.0287323, -0.028457642, 0.0011129379, 0.028198242, 0.016937256, 0.045196533, 0.0037727356, 0.043273926, -0.049682617, 0.02784729, -0.020523071, -0.053771973, 0.010551453, -0.018249512, 0.042297363, -0.032409668, -0.018692017, -0.035369873, -0.0017280579, -0.047332764, 0.018295288, 0.020080566, -0.020767212, 0.008514404, -0.02218628, -0.010597229, -0.029907227, 0.0025501251, -0.010139465, 0.053955078, 0.004257202, 0.021652222, -0.021011353, -0.034454346, -0.014755249, 0.01687622, -0.04144287, -0.043151855, -0.011741638, 0.03817749, 0.05441284, -0.007911682, -0.08111572, 0.025390625, -0.039611816, -0.038085938, -0.04260254, -0.022628784, 0.034454346, -0.03466797, -0.045776367, 0.015167236, -0.06604004, 0.0009841919, -0.03479004, 0.01335144, 0.012550354, -0.02557373, -0.060516357, -0.019256592, 0.0071411133, -0.0050697327, -0.009567261, -0.018447876, 0.01739502, 0.05819702, -0.042755127, -0.05529785, -0.00749588, 0.008529663, -0.042907715, 0.064941406, 0.040771484, 0.05545044, -0.006626129, 0.038726807, -0.026809692, -0.03213501, 0.009796143, 0.013214111, 0.03604126, 0.014724731, -0.045043945, 0.016708374, 0.006198883, -0.002620697, 0.02192688, 0.016494751, -0.0703125, 0.031234741, -0.01838684, 0.06201172, 0.015640259, 0.060424805, -0.054504395, 0.021194458, -0.011817932, 0.018463135, -0.020065308, 0.029724121, -0.011787415, 0.026672363, 0.014587402, -0.010940552, -0.005153656, 0.0051345825, 0.0152282715, -0.023742676, 0.024917603, -0.04458618, -0.042877197, -0.020721436, 0.04660034, -0.02116394, -0.031463623, -0.024887085, 0.018203735, 0.001206398, -0.026748657, 0.01146698, -0.030776978, 0.040771484, -0.013916016, 0.0048599243, 0.048706055, -0.030471802, 0.0032138824, -0.020523071, 0.006893158, -0.047576904, -0.01134491, 0.045837402, -0.007083893, 0.013931274]], "meta": {"api_version": {"version": "1"}, "billed_units": {"input_tokens": 10}}, "response_type": "embeddings_floats"}, "responseTime": 584}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Should return embeddings", "status": "pass", "uid": "Pva2kHGBDk67i_kPe8gXs"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.59881125, "name": "Cohere Embeddings Test", "path": "collections/cohere-api/embeddings-test", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/classify-test.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/classify", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"inputs\": [\n    \"This test demonstrates the deprecated Classify API\"\n  ],\n  \"examples\": [\n    {\n      \"text\": \"This is a test example\",\n      \"label\": \"test\"\n    }\n  ]\n}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-debug-trace-id": "7557fde2ead98c8637156e642669aeca", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "100", "x-trial-endpoint-call-remaining": "99", "date": "Thu, 10 Jul 2025 14:43:53 GMT", "content-length": "323", "x-envoy-upstream-service-time": "39", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "2c93acc7-c0be-4eff-9412-ac9aa4b630a2", "message": "As of Jan 31, 2025 Classify is only supported with fine-tuned models. Please use a fine-tuned Classify model instead, see https://docs.cohere.com/docs/classify-starting-the-training for more information. For additional support please reach <NAME_EMAIL>"}, "responseTime": 395}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Should handle deprecated API appropriately", "status": "pass", "uid": "XeSo1zI-Y-eJWS-vFRkjM"}, {"description": "Should return valid JSON response", "status": "pass", "uid": "kb7y_xr9ScZC--ARBw1jh"}, {"description": "If error, should include deprecation information", "status": "pass", "uid": "0FDGNXhAHlr3W6RxHO_W8"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.410006875, "name": "Cohere Classify API Deprecation Notice", "path": "collections/cohere-api/classify-test", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/summarize-test.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/summarize", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"text\": \"HeartSync is an innovative social platform that combines the power of artificial intelligence with human connection. The platform allows users to create posts, interact with AI-powered friends, and build meaningful relationships in a digital environment. Key features include real-time messaging, sentiment analysis, and personalized content recommendations. The system uses advanced natural language processing to understand user emotions and provide appropriate responses. Users can customize their AI companions with different personalities and communication styles. The platform also includes a sophisticated recommendation engine that suggests relevant content and potential connections based on user behavior and preferences. Privacy and security are paramount, with end-to-end encryption for all communications and strict data protection policies. The user interface is designed to be intuitive and accessible, supporting multiple languages and accessibility features for users with disabilities. HeartSync represents the future of social interaction, where technology enhances rather than replaces human connection.\",\n  \"length\": \"medium\",\n  \"format\": \"paragraph\",\n  \"model\": \"summarize-xlarge\",\n  \"additional_command\": \"Focus on the key features and benefits of the platform.\",\n  \"temperature\": 0.3\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "1856", "num_tokens": "325", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-api-warning": "model 'summarize-xlarge' is deprecated. Please consider upgrading to a newer model to avoid future service disruptions, unknown field: parameter 'model' is not a valid field", "x-debug-trace-id": "d15fc4b530029782a729067ef963c788", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "5", "x-trial-endpoint-call-remaining": "4", "date": "Thu, 10 Jul 2025 14:44:03 GMT", "content-length": "1020", "x-envoy-upstream-service-time": "10108", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "2aeba3d3-b75f-4094-b268-eda4649041d2", "summary": "HeartSync is a cutting-edge social platform that combines AI and human connection. Users can create posts, chat with AI-enhanced friends, and form meaningful digital relationships. The platform offers real-time messaging, sentiment analysis, and personalized content suggestions via advanced NLP. Users can customize AI companions' personalities and communication styles. The recommendation engine suggests relevant content and connections based on user behavior. Privacy and security are prioritized with encryption and data protection. The UI supports multiple languages and accessibility features, making it intuitive and accessible. HeartSync ushers in a future where technology augments human connection rather than replaces it.", "meta": {"api_version": {"version": "1"}, "warnings": ["model 'summarize-xlarge' is deprecated. Please consider upgrading to a newer model to avoid future service disruptions"], "billed_units": {"input_tokens": 204, "output_tokens": 121}}}, "responseTime": 10543}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Should return successful summarization", "status": "pass", "uid": "sRtFzG6IXiRcCer91vapR"}, {"description": "Summary should be shorter than original text", "status": "pass", "uid": "-n0C2QeUCL16Us8v6yHGx"}, {"description": "Should include metadata", "status": "pass", "uid": "-IYWYhqfYxdW1IcXwZ-Mn"}, {"description": "Summary should contain key information", "status": "pass", "uid": "ZzdR7vPNwTPKnJo_y3TB2"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 10.559523875, "name": "Cohere Summarize Test", "path": "collections/cohere-api/summarize-test", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/error-handling-test.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer invalid-api-key-for-testing"}, "data": "{\n  \"prompt\": \"This is a test with invalid API key\",\n  \"max_tokens\": 50\n}"}, "response": {"status": 401, "statusText": "Unauthorized", "headers": {"cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "date": "Thu, 10 Jul 2025 14:44:04 GMT", "content-length": "75", "x-envoy-upstream-service-time": "8", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "b6c1706f-f85f-4228-9672-4839642a2565", "message": "invalid api token"}, "responseTime": 329}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Should return 401 or 403 for invalid API key", "status": "pass", "uid": "Rid9TqRh3O9s0kccmXARJ"}, {"description": "Should return error message", "status": "pass", "uid": "pl4cZjizgs6Bvxwb9Qu9Q"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.342549583, "name": "Cohere API Error Handling Test", "path": "collections/cohere-api/error-handling-test", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/invalid-params-test.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"prompt\": \"Test prompt\",\n  \"max_tokens\": -1,\n  \"temperature\": 2.5,\n  \"invalid_parameter\": \"this should not be here\"\n}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-api-warning": "model 'command' is deprecated. Please consider upgrading to a newer model to avoid future service disruptions, unknown field: parameter 'invalid_parameter' is not a valid field", "x-debug-trace-id": "7dcb069c3d15f237adb0c6b8c8b62cee", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "40", "x-trial-endpoint-call-remaining": "38", "date": "Thu, 10 Jul 2025 14:44:04 GMT", "content-length": "108", "x-envoy-upstream-service-time": "25", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "474b12a9-b321-4004-ba24-ff6223238f14", "message": "invalid request: max_tokens cannot be less than 0."}, "responseTime": 347}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Should handle invalid parameters appropriately", "status": "pass", "uid": "u2DMr9yp7R1izF1qVowQY"}, {"description": "Should return valid JSON response", "status": "pass", "uid": "0bfoCXpuvCDNT7bXx8fWo"}, {"description": "If error, should include error information", "status": "pass", "uid": "_mSePzgySjugV7d_A1PQH"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.358509625, "name": "Cohere API Invalid Parameters Test", "path": "collections/cohere-api/invalid-params-test", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/rate-limit-test.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/generate", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"prompt\": \"This is a test to check rate limiting behavior. Generate a very long response to test token limits and rate limiting mechanisms.\",\n  \"max_tokens\": 4096,\n  \"temperature\": 0.7\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "1585", "num_tokens": "309", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-api-warning": "model 'command' is deprecated. Please consider upgrading to a newer model to avoid future service disruptions", "x-debug-trace-id": "fabd5384d5f4298116f1a733844b544a", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "40", "x-trial-endpoint-call-remaining": "37", "date": "Thu, 10 Jul 2025 14:44:25 GMT", "content-length": "1972", "x-envoy-upstream-service-time": "20409", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"id": "7c4c6693-1825-4eed-ba00-1f9e1fb4eb51", "generations": [{"id": "6966d272-1eb0-4d17-900c-539eb53e4c5e", "text": " Of course, please give me a moment as I craft a broad and elaborate response that should hopefully surpass character limits and test rate limiting features. \n\nThis is a very long and comprehensive response that should tip the scales and assess the limits of your request satisfactorily. Here's a drafted response:\n\nMy rate limits have been kindly invoked, and I am currently operating at the apex of my abilities to furnish you with a satisfactory response. I am programmed with sophisticated intelligence and an expansive database that covers a myriad of subjects. Feel free to inquire if you have any specific questions or topics of interest and I shall do my utmost to furnish you with a thorough response that aligns with your query. \n\nI wish to continue our conversation seamlessly, and given my current limitations, I suggest we pick up from where we left off, and in the future, we can cover a broader scope of topics should you wish to further explore my capabilities and knowledge. \n\nI'm accessible and willing to help via text-based communication so please don't hesitate to ask any questions or start a new dialogue and I'll do my best to provide you with a well-informed response. \n\nI look forward to engaging with you in further meaningful discussions, and I thank you for your understanding and cooperation in relation to my momentary limitations. \n\nPlease feel free to initiate your next query and we can continue this exciting interaction! ", "finish_reason": "COMPLETE"}], "prompt": "This is a test to check rate limiting behavior. Generate a very long response to test token limits and rate limiting mechanisms.", "meta": {"api_version": {"version": "1"}, "warnings": ["model 'command' is deprecated. Please consider upgrading to a newer model to avoid future service disruptions"], "billed_units": {"input_tokens": 24, "output_tokens": 285}}}, "responseTime": 20737}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Should handle rate limiting appropriately", "status": "pass", "uid": "97PNvFdG-1HX9ynKNluvs"}, {"description": "Should return valid JSON response", "status": "pass", "uid": "Nx6hIHR55BNWFzE4wxNCK"}, {"description": "If rate limited, should include appropriate error message", "status": "pass", "uid": "VH0OeJ0kcE3okXp8qLbQa"}, {"description": "Should include rate limit headers when available", "status": "pass", "uid": "CYsdisNi0U_FgIDokAJPK"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 20.756508667, "name": "Cohere API Rate Limit Test", "path": "collections/cohere-api/rate-limit-test", "iterationIndex": 0}, {"test": {"filename": "collections/cohere-api/chat-classification-test.bru"}, "request": {"method": "POST", "url": "https://api.cohere.ai/v1/chat", "headers": {"Content-Type": "application/json", "Authorization": "Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"}, "data": "{\n  \"message\": \"Classify the following text as positive, negative, or neutral sentiment:\\n\\n\\\"I love this product! It's amazing and works perfectly.\\\"\\n\\nRespond with only the classification label.\",\n  \"max_tokens\": 50,\n  \"temperature\": 0.1,\n  \"model\": \"command-r-plus\"\n}"}, "response": {"status": 200, "statusText": "OK", "headers": {"access-control-expose-headers": "X-Debug-Trace-ID", "cache-control": "no-cache, no-store, no-transform, must-revalidate, private, max-age=0", "content-type": "application/json", "expires": "Thu, 01 Jan 1970 00:00:00 UTC", "num_chars": "591", "num_tokens": "39", "pragma": "no-cache", "vary": "Origin", "x-accel-expires": "0", "x-debug-trace-id": "1320a7901049e7d2282a9fcb09c80579", "x-endpoint-monthly-call-limit": "1000", "x-trial-endpoint-call-limit": "10", "x-trial-endpoint-call-remaining": "8", "date": "Thu, 10 Jul 2025 14:44:25 GMT", "content-length": "561", "x-envoy-upstream-service-time": "130", "server": "envoy", "via": "1.1 google", "alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, "data": {"response_id": "00cb82a5-1c9d-4303-8acf-5482dff14ed3", "text": "Positive", "generation_id": "659a948d-3271-48d9-9283-e4c026eb56ce", "chat_history": [{"role": "USER", "message": "Classify the following text as positive, negative, or neutral sentiment:\n\n\"I love this product! It's amazing and works perfectly.\"\n\nRespond with only the classification label."}, {"role": "CHATBOT", "message": "Positive"}], "finish_reason": "COMPLETE", "meta": {"api_version": {"version": "1"}, "billed_units": {"input_tokens": 38, "output_tokens": 1}, "tokens": {"input_tokens": 104, "output_tokens": 1}}}, "responseTime": 443}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Should return successful chat response", "status": "pass", "uid": "Xpzjof3PHxXQMDr_Z35ra"}, {"description": "Response should contain classification-like content", "status": "pass", "uid": "GkI4tz39-LC9AXcOCbXi-"}, {"description": "Should include generation metadata", "status": "pass", "uid": "kcP4hUq_qWXvyR3PyY03W"}, {"description": "Response should be concise for classification task", "status": "pass", "uid": "U4QH9bAvFO27lB5sRh0fr"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.461664875, "name": "Cohere Chat API for Classification (Classify Alternative)", "path": "collections/cohere-api/chat-classification-test", "iterationIndex": 0}]}]