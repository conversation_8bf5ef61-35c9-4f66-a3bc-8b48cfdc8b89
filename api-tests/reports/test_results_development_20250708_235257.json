[{"iterationIndex": 0, "summary": {"totalRequests": 2, "passedRequests": 0, "failedRequests": 1, "skippedRequests": 0, "errorRequests": 1, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 2, "passedTests": 0, "failedTests": 2, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/claude-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "headers": {"Content-Type": "application/json", "x-api-key": "", "anthropic-version": "2023-06-01"}, "data": "{\n  \"model\": \"claude-3-haiku-20240307\",\n  \"max_tokens\": 100,\n  \"messages\": [\n    {\n      \"role\": \"user\", \n      \"content\": \"Hello! This is a health check from HSN Bruno tests. Please respond with a simple confirmation.\"\n    }\n  ]\n}"}, "response": {"status": 401, "statusText": "Unauthorized", "headers": {"date": "<PERSON><PERSON>, 08 Jul 2025 15:52:59 GMT", "content-type": "application/json", "content-length": "97", "connection": "keep-alive", "x-should-retry": "false", "request-id": "req_011CQupZ1We63jSwN26C7E8B", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "via": "1.1 google", "cf-cache-status": "DYNAMIC", "x-robots-tag": "none", "server": "cloudflare", "cf-ray": "95c0c07a8e13fc98-KIX"}, "data": {"type": "error", "error": {"type": "authentication_error", "message": "x-api-key header is required"}}, "responseTime": 498}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200", "status": "fail", "error": "expected 401 to equal 200", "actual": 401, "expected": 200, "uid": "KwT3fo8BHO3I-DpKEthb8"}, {"description": "Response should contain content", "status": "fail", "error": "expected { type: 'error', error: { …(2) } } to have property 'content'", "actual": {"type": "error", "error": {"type": "authentication_error", "message": "x-api-key header is required"}}, "uid": "2996HiinjZGfLt0Kug4W1"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.559305042, "name": "Claude API Health Check", "path": "collections/claude-api/health-check", "iterationIndex": 0}, {"test": {"filename": "collections/hsn-health/app-health.bru"}, "request": {"method": "GET", "url": "http://localhost:8787/api/health", "headers": {}}, "response": {"status": "error", "statusText": null, "headers": null, "data": null, "responseTime": 0}, "error": "connect ECONNREFUSED ::1:8787", "status": "error", "assertionResults": [], "testResults": [], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.002708333, "name": "HSN Application Health Check", "path": "collections/hsn-health/app-health", "iterationIndex": 0}]}]