[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 0, "failedRequests": 1, "skippedRequests": 0, "errorRequests": 0, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 2, "passedTests": 1, "failedTests": 1, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "collections/instantdb-api/health-check.bru"}, "request": {"method": "POST", "url": "https://api.instantdb.com/admin/query", "headers": {"Content-Type": "application/json", "Authorization": "Bearer a24fb821-52fb-4b35-b9bf-35d561508a68"}, "data": "{\n  \"app-id\": \"a24fb821-52fb-4b35-b9bf-35d561508a68\",\n  \"query\": {\n    \"health\": {}\n  }\n}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-type": "application/json; charset=utf-8", "content-length": "161", "connection": "keep-alive", "date": "Tu<PERSON>, 08 Jul 2025 16:02:59 GMT", "x-cache": "Error from cloudfront", "via": "1.1 358f8e46780e3a959fd761293471617e.cloudfront.net (CloudFront)", "x-amz-cf-pop": "NRT57-P2", "alt-svc": "h3=\":443\"; ma=86400", "x-amz-cf-id": "-jzmJ4k_rdqglimzCQvyT5lwut8O7kWMXX7r63DQGoVvrDRbLzXvww=="}, "data": {"type": "param-missing", "message": "Missing parameter: [\"headers\" \"app-id\"]", "hint": {"in": ["headers", "app-id"]}, "trace-id": "e6e09c8f2929242918797ff4cc52122e"}, "responseTime": 824}, "error": null, "status": "pass", "assertionResults": [], "testResults": [{"description": "Status should be 200 or 401 (auth check)", "status": "fail", "error": "expected [ 200, 401, 403 ] to include 400", "actual": [200, 401, 403], "uid": "w9XQ_OvfSPR1jdNeY_-bu"}, {"description": "Response should be JSON", "status": "pass", "uid": "q-ACau93A35G4E4ijieGi"}], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 0.886790458, "name": "InstantDB API Health Check", "path": "collections/instantdb-api/health-check", "iterationIndex": 0}]}]