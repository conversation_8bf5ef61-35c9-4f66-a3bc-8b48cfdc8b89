#!/bin/bash

# HeartSync API Test Runner
# Usage: ./run-tests.sh [environment] [collection]
# Example: ./run-tests.sh development claude-api

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-development}
COLLECTION=${2:-}
VERBOSE=${3:-false}

echo -e "${BLUE}🧪 HeartSync API Test Runner${NC}"
echo -e "${BLUE}================================${NC}"

# Check if environment file exists
ENV_FILE="environments/${ENVIRONMENT}.bru"
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${RED}❌ Environment file not found: $ENV_FILE${NC}"
    echo -e "${YELLOW}Available environments:${NC}"
    ls -1 environments/*.bru | sed 's/environments\//  - /' | sed 's/\.bru$//'
    exit 1
fi

echo -e "${GREEN}✅ Using environment: $ENVIRONMENT${NC}"

# Check if API keys are set
if [ -z "$CLAUDE_API_KEY" ]; then
    echo -e "${YELLOW}⚠️  Warning: CLAUDE_API_KEY not set${NC}"
fi

if [ -z "$TAVILY_API_KEY" ]; then
    echo -e "${YELLOW}⚠️  Warning: TAVILY_API_KEY not set${NC}"
fi

if [ -z "$INSTANTDB_APP_ID" ]; then
    echo -e "${YELLOW}⚠️  Warning: INSTANTDB_APP_ID not set${NC}"
fi

if [ -z "$OPENROUTER_API_KEY" ]; then
    echo -e "${YELLOW}⚠️  Warning: OPENROUTER_API_KEY not set${NC}"
fi

if [ -z "$COHERE_API_KEY" ]; then
    echo -e "${YELLOW}⚠️  Warning: COHERE_API_KEY not set${NC}"
fi

if [ -z "$FINLIGHT_API_KEY" ]; then
    echo -e "${YELLOW}⚠️  Warning: FINLIGHT_API_KEY not set${NC}"
fi

# Build the command
CMD="bru run"

if [ -n "$COLLECTION" ]; then
    if [ -d "collections/$COLLECTION" ]; then
        CMD="$CMD collections/$COLLECTION"
        echo -e "${GREEN}✅ Running collection: $COLLECTION${NC}"
    else
        echo -e "${RED}❌ Collection not found: collections/$COLLECTION${NC}"
        echo -e "${YELLOW}Available collections:${NC}"
        ls -1 collections/ | sed 's/^/  - /'
        exit 1
    fi
else
    CMD="$CMD . -r"
    echo -e "${GREEN}✅ Running all tests${NC}"
fi

# Add environment
CMD="$CMD --env-file $ENV_FILE"

# Add environment variables as overrides
if [ -n "$CLAUDE_API_KEY" ]; then
    CMD="$CMD --env-var CLAUDE_API_KEY=$CLAUDE_API_KEY"
fi

if [ -n "$TAVILY_API_KEY" ]; then
    CMD="$CMD --env-var TAVILY_API_KEY=$TAVILY_API_KEY"
fi

if [ -n "$INSTANTDB_APP_ID" ]; then
    CMD="$CMD --env-var INSTANTDB_APP_ID=$INSTANTDB_APP_ID"
fi

if [ -n "$OPENROUTER_API_KEY" ]; then
    CMD="$CMD --env-var OPENROUTER_API_KEY=$OPENROUTER_API_KEY"
fi

if [ -n "$COHERE_API_KEY" ]; then
    CMD="$CMD --env-var COHERE_API_KEY=$COHERE_API_KEY"
fi

if [ -n "$FINLIGHT_API_KEY" ]; then
    CMD="$CMD --env-var FINLIGHT_API_KEY=$FINLIGHT_API_KEY"
fi

# Add verbose if requested
if [ "$VERBOSE" = "true" ]; then
    CMD="$CMD --verbose"
fi

# Add output file
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
OUTPUT_FILE="reports/test_results_${ENVIRONMENT}_${TIMESTAMP}.json"
CMD="$CMD --output $OUTPUT_FILE"

echo -e "${BLUE}📋 Command: $CMD${NC}"
echo ""

# Create reports directory if it doesn't exist
mkdir -p reports

# Run the tests
echo -e "${BLUE}🚀 Running tests...${NC}"
if eval $CMD; then
    echo -e "${GREEN}✅ Tests completed successfully!${NC}"
    echo -e "${BLUE}📊 Results saved to: $OUTPUT_FILE${NC}"
else
    echo -e "${RED}❌ Tests failed!${NC}"
    echo -e "${BLUE}📊 Results saved to: $OUTPUT_FILE${NC}"
    exit 1
fi
