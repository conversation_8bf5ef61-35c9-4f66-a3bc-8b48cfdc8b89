#!/bin/bash

# Linear API Helper Functions
# Make sure LINEAR_API_TOKEN is set in your environment

TEAM_ID="2eace510-09e3-4a38-8c7b-719026f67947"
LINEAR_API_URL="https://api.linear.app/graphql"

# Helper function to make GraphQL requests
linear_request() {
    local query="$1"
    curl -s -X POST \
        -H "Authorization: $LINEAR_API_TOKEN" \
        -H "Content-Type: application/json" \
        -d "$query" \
        "$LINEAR_API_URL"
}

# Get all issues from the team
linear_get_issues() {
    local query='{
        "query": "query { issues(filter: { team: { id: { eq: \"'$TEAM_ID'\" } } }) { nodes { id title description state { name } assignee { name } priority labels { nodes { name } } createdAt updatedAt } } }"
    }'
    
    echo "📋 Fetching issues from HeartSync team..."
    linear_request "$query" | jq '.data.issues.nodes'
}

# Create a new issue
linear_create_issue() {
    local title="$1"
    local description="$2"
    local priority="${3:-0}"  # Default priority is 0 (no priority)
    
    if [[ -z "$title" ]]; then
        echo "❌ Error: Title is required"
        echo "Usage: linear_create_issue \"Title\" \"Description\" [priority]"
        echo "Priority: 0=None, 1=Urgent, 2=High, 3=Medium, 4=Low"
        return 1
    fi
    
    local query='{
        "query": "mutation { issueCreate(input: { title: \"'$title'\", description: \"'$description'\", teamId: \"'$TEAM_ID'\", priority: '$priority' }) { success issue { id title url } } }"
    }'
    
    echo "🚀 Creating issue: $title"
    local result=$(linear_request "$query")
    local success=$(echo "$result" | jq -r '.data.issueCreate.success')
    
    if [[ "$success" == "true" ]]; then
        local issue_id=$(echo "$result" | jq -r '.data.issueCreate.issue.id')
        local issue_url=$(echo "$result" | jq -r '.data.issueCreate.issue.url')
        echo "✅ Issue created successfully!"
        echo "🔗 URL: $issue_url"
        echo "🆔 ID: $issue_id"
    else
        echo "❌ Failed to create issue"
        echo "$result" | jq '.errors'
    fi
}

# Update an existing issue
linear_update_issue() {
    local issue_id="$1"
    local title="$2"
    local description="$3"
    local state_id="$4"
    
    if [[ -z "$issue_id" ]]; then
        echo "❌ Error: Issue ID is required"
        echo "Usage: linear_update_issue \"issue_id\" \"new_title\" \"new_description\" [state_id]"
        return 1
    fi
    
    # Build input object based on provided parameters
    local input_parts=()
    [[ -n "$title" ]] && input_parts+=("title: \"$title\"")
    [[ -n "$description" ]] && input_parts+=("description: \"$description\"")
    [[ -n "$state_id" ]] && input_parts+=("stateId: \"$state_id\"")
    
    local input=$(IFS=', '; echo "${input_parts[*]}")
    
    local query='{
        "query": "mutation { issueUpdate(id: \"'$issue_id'\", input: { '$input' }) { success issue { id title url } } }"
    }'
    
    echo "🔄 Updating issue: $issue_id"
    local result=$(linear_request "$query")
    local success=$(echo "$result" | jq -r '.data.issueUpdate.success')
    
    if [[ "$success" == "true" ]]; then
        local issue_url=$(echo "$result" | jq -r '.data.issueUpdate.issue.url')
        echo "✅ Issue updated successfully!"
        echo "🔗 URL: $issue_url"
    else
        echo "❌ Failed to update issue"
        echo "$result" | jq '.errors'
    fi
}

# Add a comment to an issue
linear_add_comment() {
    local issue_id="$1"
    local comment="$2"
    
    if [[ -z "$issue_id" ]] || [[ -z "$comment" ]]; then
        echo "❌ Error: Issue ID and comment are required"
        echo "Usage: linear_add_comment \"issue_id\" \"comment text\""
        return 1
    fi
    
    local query='{
        "query": "mutation { commentCreate(input: { issueId: \"'$issue_id'\", body: \"'$comment'\" }) { success comment { id body } } }"
    }'
    
    echo "💬 Adding comment to issue: $issue_id"
    local result=$(linear_request "$query")
    local success=$(echo "$result" | jq -r '.data.commentCreate.success')
    
    if [[ "$success" == "true" ]]; then
        echo "✅ Comment added successfully!"
    else
        echo "❌ Failed to add comment"
        echo "$result" | jq '.errors'
    fi
}

# Get workflow states for the team
linear_get_states() {
    local query='{
        "query": "query { team(id: \"'$TEAM_ID'\") { states { nodes { id name type } } } }"
    }'
    
    echo "🔄 Fetching workflow states..."
    linear_request "$query" | jq '.data.team.states.nodes'
}

# Get specific issue by ID
linear_get_issue() {
    local issue_id="$1"
    
    if [[ -z "$issue_id" ]]; then
        echo "❌ Error: Issue ID is required"
        echo "Usage: linear_get_issue \"issue_id\""
        return 1
    fi
    
    local query='{
        "query": "query { issue(id: \"'$issue_id'\") { id title description state { name } assignee { name } priority labels { nodes { name } } createdAt updatedAt url } }"
    }'
    
    echo "🔍 Fetching issue: $issue_id"
    linear_request "$query" | jq '.data.issue'
}

# Search issues by title
linear_search_issues() {
    local search_term="$1"
    
    if [[ -z "$search_term" ]]; then
        echo "❌ Error: Search term is required"
        echo "Usage: linear_search_issues \"search term\""
        return 1
    fi
    
    local query='{
        "query": "query { issues(filter: { team: { id: { eq: \"'$TEAM_ID'\" } }, title: { contains: \"'$search_term'\" } }) { nodes { id title description state { name } url } } }"
    }'
    
    echo "🔍 Searching for issues containing: $search_term"
    linear_request "$query" | jq '.data.issues.nodes'
}

# Helper function to show usage
linear_help() {
    echo "📚 Linear API Helper Functions:"
    echo ""
    echo "🔍 Query functions:"
    echo "  linear_get_issues                    - Get all issues from HeartSync team"
    echo "  linear_get_issue <issue_id>          - Get specific issue by ID"
    echo "  linear_search_issues <search_term>   - Search issues by title"
    echo "  linear_get_states                    - Get workflow states"
    echo ""
    echo "✏️  Modification functions:"
    echo "  linear_create_issue <title> <description> [priority]"
    echo "  linear_update_issue <issue_id> <title> <description> [state_id]"
    echo "  linear_add_comment <issue_id> <comment>"
    echo ""
    echo "📋 Examples:"
    echo "  linear_create_issue \"Fix bug\" \"Description of the bug\" 2"
    echo "  linear_update_issue \"abc123\" \"Updated title\" \"Updated description\""
    echo "  linear_add_comment \"abc123\" \"This is a comment\""
    echo ""
    echo "🎯 Priority levels: 0=None, 1=Urgent, 2=High, 3=Medium, 4=Low"
}

# Show help by default
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    linear_help
fi
