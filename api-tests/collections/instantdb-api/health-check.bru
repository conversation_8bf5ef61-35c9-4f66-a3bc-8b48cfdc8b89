meta {
  name: InstantDB API Health Check
  type: http
  seq: 1
}

post {
  url: https://api.instantdb.com/admin/query
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  app-id: {{INSTANTDB_APP_ID}}
  authorization: Bearer {{INSTANTDB_APP_ID}}
}

body:json {
  {
    "query": {
      "health": {}
    }
  }
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ InstantDB API Test: PASSED");
    const body = res.getBody();
    console.log(`Response: ${JSON.stringify(body).substring(0, 200)}...`);
  } else {
    console.log(`❌ InstantDB API Test: FAILED (${res.status})`);
    console.log(`Error: ${JSON.stringify(res.getBody())}`);
  }
}

tests {
  test("Status should be valid API response", function() {
    // We expect 200 (success), 400 (bad request), 401 (unauthorized), or 403 (forbidden)
    const status = res.getStatus();
    expect([200, 400, 401, 403]).to.include(status);
  });
  
  test("Response should be JSON", function() {
    const body = res.getBody();
    expect(body).to.be.an('object');
  });
}
