meta {
  name: <PERSON> Health Check
  type: http
  seq: 1
}

post {
  url: https://api.anthropic.com/v1/messages
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  x-api-key: {{CLAUDE_API_KEY}}
  anthropic-version: 2023-06-01
}

body:json {
  {
    "model": "claude-3-haiku-20240307",
    "max_tokens": 100,
    "messages": [
      {
        "role": "user", 
        "content": "Hello! This is a health check from HSN Bruno tests. Please respond with a simple confirmation."
      }
    ]
  }
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Claude API Health Check: PASSED");
    console.log(`Response: ${JSON.stringify(res.getBody().content[0].text)}`);
  } else {
    console.log(`❌ Claude API Health Check: FAILED (${res.status})`);
    console.log(`Error: ${res.getBody()}`);
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain content", function() {
    const body = res.getBody();
    expect(body).to.have.property('content');
    expect(body.content).to.be.an('array');
  });
}
