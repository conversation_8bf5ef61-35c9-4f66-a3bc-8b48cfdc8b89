meta {
  name: Cohere API Invalid Parameters Test
  type: http
  seq: 7
}

post {
  url: https://api.cohere.ai/v1/generate
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX
}

body:json {
  {
    "prompt": "Test prompt",
    "max_tokens": -1,
    "temperature": 2.5,
    "invalid_parameter": "this should not be here"
  }
}

script:post-response {
  const response = res.getBody();
  
  if (res.status === 400) {
    console.log("✅ Invalid Parameters Test: PASSED");
    console.log(`Expected 400 error: ${JSON.stringify(response)}`);
  } else if (res.status === 422) {
    console.log("✅ Invalid Parameters Test: PASSED (422 Unprocessable Entity)");
    console.log(`Expected 422 error: ${JSON.stringify(response)}`);
  } else if (res.status === 200) {
    console.log("⚠️ Invalid Parameters Test: API accepted invalid parameters");
    console.log(`Response: ${JSON.stringify(response)}`);
  } else {
    console.log(`❌ Invalid Parameters Test: UNEXPECTED STATUS (${res.status})`);
    console.log(`Response: ${JSON.stringify(response)}`);
  }
}

tests {
  test("Should handle invalid parameters appropriately", function() {
    const status = res.getStatus();
    // Accept 200 (API might ignore invalid params), 400 (bad request), or 422 (unprocessable entity)
    expect([200, 400, 422]).to.include(status);
  });
  
  test("Should return valid JSON response", function() {
    const body = res.getBody();
    expect(body).to.be.an('object');
  });
  
  test("If error, should include error information", function() {
    const status = res.getStatus();
    if (status === 400 || status === 422) {
      const body = res.getBody();
      const hasErrorField = body.hasOwnProperty('message') || 
                           body.hasOwnProperty('error') || 
                           body.hasOwnProperty('detail');
      expect(hasErrorField).to.be.true;
    }
  });
}
