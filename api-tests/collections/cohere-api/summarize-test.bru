meta {
  name: Cohere Summarize Test
  type: http
  seq: 5
}

post {
  url: https://api.cohere.ai/v1/summarize
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX
}

body:json {
  {
    "text": "HeartSync is an innovative social platform that combines the power of artificial intelligence with human connection. The platform allows users to create posts, interact with AI-powered friends, and build meaningful relationships in a digital environment. Key features include real-time messaging, sentiment analysis, and personalized content recommendations. The system uses advanced natural language processing to understand user emotions and provide appropriate responses. Users can customize their AI companions with different personalities and communication styles. The platform also includes a sophisticated recommendation engine that suggests relevant content and potential connections based on user behavior and preferences. Privacy and security are paramount, with end-to-end encryption for all communications and strict data protection policies. The user interface is designed to be intuitive and accessible, supporting multiple languages and accessibility features for users with disabilities. HeartSync represents the future of social interaction, where technology enhances rather than replaces human connection.",
    "length": "medium",
    "format": "paragraph",
    "model": "summarize-xlarge",
    "additional_command": "Focus on the key features and benefits of the platform.",
    "temperature": 0.3
  }
}

script:post-response {
  const response = res.getBody();
  
  if (res.status === 200) {
    console.log("✅ Cohere Summarize API: SUCCESS");
    console.log(`📝 Summary generated (${response.summary.length} characters)`);
    console.log(`Summary: ${response.summary}`);
    
    if (response.meta) {
      console.log(`🔍 API Version: ${response.meta.api_version || 'N/A'}`);
    }
  } else {
    console.log(`❌ Cohere Summarize API: FAILED (${res.status})`);
    console.log(`Error: ${JSON.stringify(response)}`);
  }
}

tests {
  test("Should return successful summarization", function() {
    expect(res.getStatus()).to.equal(200);
    const body = res.getBody();
    expect(body).to.have.property('summary');
    expect(body.summary).to.be.a('string');
    expect(body.summary.length).to.be.greaterThan(0);
  });
  
  test("Summary should be shorter than original text", function() {
    const body = res.getBody();
    const originalLength = 1247; // Length of the input text
    expect(body.summary.length).to.be.lessThan(originalLength);
  });
  
  test("Should include metadata", function() {
    const body = res.getBody();
    expect(body).to.have.property('id');
    expect(body.id).to.be.a('string');
  });
  
  test("Summary should contain key information", function() {
    const body = res.getBody();
    const summary = body.summary.toLowerCase();
    
    // Check if summary contains key concepts from the original text
    const keyTerms = ['heartsync', 'ai', 'social', 'platform', 'users'];
    const foundTerms = keyTerms.filter(term => summary.includes(term));
    
    expect(foundTerms.length).to.be.greaterThan(0);
  });
}
