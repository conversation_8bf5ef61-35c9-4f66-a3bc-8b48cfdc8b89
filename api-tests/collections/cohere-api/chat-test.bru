meta {
  name: Cohere API Chat Test
  type: http
  seq: 2
}

post {
  url: https://api.cohere.ai/v1/chat
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX
}

body:json {
  {
    "message": "Hello! Tell me about Cohere's capabilities in 2 sentences.",
    "max_tokens": 100,
    "temperature": 0.7
  }
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Cohere Chat API Test: PASSED");
    const body = res.getBody();
    if (body.text) {
      console.log(`Response: ${body.text.substring(0, 200)}...`);
    }
  } else {
    console.log(`❌ Cohere Chat API Test: FAILED (${res.status})`);
    console.log(`Error: ${JSON.stringify(res.getBody())}`);
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain text", function() {
    const body = res.getBody();
    expect(body).to.have.property('text');
    expect(body.text).to.be.a('string');
  });
  
  test("Response should contain generation metadata", function() {
    const body = res.getBody();
    expect(body).to.have.property('generation_id');
    expect(body.generation_id).to.be.a('string');
  });
}
