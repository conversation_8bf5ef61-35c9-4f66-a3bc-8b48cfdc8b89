meta {
  name: Cohere API Rate Limit Test
  type: http
  seq: 8
}

post {
  url: https://api.cohere.ai/v1/generate
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX
}

body:json {
  {
    "prompt": "This is a test to check rate limiting behavior. Generate a very long response to test token limits and rate limiting mechanisms.",
    "max_tokens": 4096,
    "temperature": 0.7
  }
}

script:post-response {
  const response = res.getBody();
  const headers = res.getHeaders();
  
  console.log("🔍 Rate Limit Test Results:");
  console.log(`Status: ${res.status}`);
  
  // Check for rate limit headers
  const rateLimitHeaders = [
    'x-ratelimit-limit',
    'x-ratelimit-remaining', 
    'x-ratelimit-reset',
    'retry-after'
  ];
  
  rateLimitHeaders.forEach(header => {
    if (headers[header]) {
      console.log(`${header}: ${headers[header]}`);
    }
  });
  
  if (res.status === 200) {
    console.log("✅ Rate Limit Test: Request successful");
    if (response.generations) {
      console.log(`Generated tokens: ~${response.generations[0].text.length / 4}`);
    }
  } else if (res.status === 429) {
    console.log("⚠️ Rate Limit Test: Rate limit exceeded (expected behavior)");
    console.log(`Error: ${JSON.stringify(response)}`);
  } else if (res.status === 402) {
    console.log("⚠️ Rate Limit Test: Payment required (quota exceeded)");
    console.log(`Error: ${JSON.stringify(response)}`);
  } else {
    console.log(`❌ Rate Limit Test: Unexpected status (${res.status})`);
    console.log(`Response: ${JSON.stringify(response)}`);
  }
}

tests {
  test("Should handle rate limiting appropriately", function() {
    const status = res.getStatus();
    // Accept various status codes that indicate different types of limits
    expect([200, 402, 429]).to.include(status);
  });
  
  test("Should return valid JSON response", function() {
    const body = res.getBody();
    expect(body).to.be.an('object');
  });
  
  test("If rate limited, should include appropriate error message", function() {
    const status = res.getStatus();
    if (status === 429 || status === 402) {
      const body = res.getBody();
      const hasErrorField = body.hasOwnProperty('message') || 
                           body.hasOwnProperty('error') || 
                           body.hasOwnProperty('detail');
      expect(hasErrorField).to.be.true;
    }
  });
  
  test("Should include rate limit headers when available", function() {
    const headers = res.getHeaders();
    // This test is informational - not all APIs include these headers
    const rateLimitHeaderExists = headers['x-ratelimit-limit'] || 
                                 headers['x-ratelimit-remaining'] || 
                                 headers['retry-after'];
    
    if (rateLimitHeaderExists) {
      console.log("ℹ️ Rate limit headers found");
    } else {
      console.log("ℹ️ No rate limit headers found (this is normal for some APIs)");
    }
  });
}
