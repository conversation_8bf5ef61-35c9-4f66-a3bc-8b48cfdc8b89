meta {
  name: Cohere API Health Check
  type: http
  seq: 1
}

post {
  url: https://api.cohere.ai/v1/generate
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX
}

body:json {
  {
    "prompt": "Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working.",
    "max_tokens": 100,
    "temperature": 0.7
  }
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Cohere API Test: PASSED");
    const body = res.getBody();
    if (body.generations && body.generations.length > 0) {
      console.log(`Response: ${body.generations[0].text.substring(0, 200)}...`);
    }
  } else {
    console.log(`❌ Cohere API Test: FAILED (${res.status})`);
    console.log(`Error: ${JSON.stringify(res.getBody())}`);
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain generations", function() {
    const body = res.getBody();
    expect(body).to.have.property('generations');
    expect(body.generations).to.be.an('array');
    expect(body.generations.length).to.be.greaterThan(0);
  });
  
  test("Response should contain text", function() {
    const body = res.getBody();
    expect(body.generations[0]).to.have.property('text');
    expect(body.generations[0].text).to.be.a('string');
  });
}
