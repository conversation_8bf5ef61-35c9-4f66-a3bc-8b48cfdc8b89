# Cohere API Tests

This collection contains tests for the Cohere API to verify functionality and authentication.

## Setup

### 1. Get a Cohere API Key
1. Visit [Cohere Dashboard](https://dashboard.cohere.ai/)
2. Sign up or log in to your account
3. Navigate to the API Keys section
4. Create a new API key or use an existing one
5. Copy the API key (it should look like: `VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX`)

### 2. Set Environment Variable
```bash
export COHERE_API_KEY=your-actual-cohere-api-key-here
```

### 3. Run Tests
```bash
# Run only Cohere API tests
./scripts/run-tests.sh development cohere-api

# Run all tests including Cohere
./scripts/run-tests.sh development

# Run specific test files
bru run collections/cohere-api/health-check.bru --env-file environments/development.bru
bru run collections/cohere-api/classify-test.bru --env-file environments/development.bru
bru run collections/cohere-api/error-handling-test.bru --env-file environments/development.bru
```

## ⚠️ Important Notice

**Cohere API Changes (January 2025):**
- The default `/v1/classify` endpoint has been **deprecated** as of January 31, 2025
- Classification now requires **fine-tuned Embed models**
- See [Classify Fine-tuning Documentation](https://docs.cohere.com/docs/classify-fine-tuning) for migration
- The `classify-test.bru` file now serves as a deprecation notice and migration guide

## Test Coverage

The Cohere API test suite now includes comprehensive coverage for:

- ✅ **Core APIs**: Generate, Chat, Embeddings
- ⚠️ **Advanced APIs**: ~~Classify~~ (deprecated), Summarize
- ✅ **Error Handling**: Invalid keys, parameters, rate limits
- ✅ **Response Validation**: Structure, content, metadata
- ✅ **Performance**: Token usage, response times
- ✅ **API Lifecycle**: Deprecation handling and migration guidance

## Test Files

### Core Functionality Tests

#### `health-check.bru`
Tests the Cohere `/v1/generate` endpoint for basic text generation:
- **Model**: Uses default model (Command Light)
- **Tests**: Status 200, generations array, text content
- **Purpose**: Verifies API key authentication and basic functionality

#### `chat-test.bru`
Tests the Cohere `/v1/chat` endpoint for conversational AI:
- **Model**: Uses default chat model
- **Tests**: Status 200, text response, generation metadata
- **Purpose**: Verifies chat functionality and response structure

#### `embeddings-test.bru`
Tests the Cohere `/v1/embed` endpoint for text embeddings:
- **Model**: Uses embed-english-v3.0
- **Tests**: Embedding generation, dimension validation
- **Purpose**: Verifies text embedding functionality

### Advanced API Tests

#### `classify-test.bru` ⚠️ **DEPRECATED**
Tests the Cohere `/v1/classify` endpoint (deprecated as of Jan 31, 2025):
- **Status**: ⚠️ API deprecated - requires fine-tuned models now
- **Tests**: Deprecation notice and error handling
- **Purpose**: Documents API deprecation and migration path
- **Migration**: Use [fine-tuned Embed models](https://docs.cohere.com/docs/classify-fine-tuning)

#### `summarize-test.bru`
Tests the Cohere `/v1/summarize` endpoint for text summarization:
- **Features**: Configurable length and format
- **Tests**: Summary generation, content validation
- **Purpose**: Verifies text summarization capabilities

### Error Handling Tests

#### `error-handling-test.bru`
Tests API behavior with invalid authentication:
- **Tests**: Invalid API key handling (401/403 errors)
- **Purpose**: Verifies proper authentication error responses

#### `invalid-params-test.bru`
Tests API behavior with invalid parameters:
- **Tests**: Negative max_tokens, invalid temperature, unknown parameters
- **Purpose**: Verifies parameter validation and error handling

#### `rate-limit-test.bru`
Tests API rate limiting and quota behavior:
- **Tests**: Large token requests, rate limit headers
- **Purpose**: Verifies rate limiting and quota management

#### `chat-classification-test.bru` 🆕
Alternative classification approach using Chat API:
- **Purpose**: Demonstrates classification using Chat API (Classify replacement)
- **Features**: Sentiment analysis via structured prompts
- **Tests**: Classification accuracy, response format validation
- **Migration**: Shows how to replace deprecated Classify API

## Expected Results

### Successful Tests

✅ **Generate API Test:**
```
✅ Cohere API Test: PASSED
Response:  Hello! I'm doing well and ready to respond...
Tests
   ✓ Status should be 200
   ✓ Response should contain generations
   ✓ Response should contain text
```

✅ **Embeddings API Test:**
```
✅ Cohere Embeddings API: SUCCESS
📊 Generated 2 embeddings
🔢 Embedding dimension: 1024
```

⚠️ **Classify API Test (Deprecated):**
```
⚠️ COHERE CLASSIFY API DEPRECATION NOTICE
📅 As of January 31, 2025, the default Classify endpoint has been deprecated.
🔧 To use classification, you must now fine-tune an Embed model.
📖 See: https://docs.cohere.com/docs/classify-fine-tuning
✅ Expected deprecation error received
```

✅ **Summarize API Test:**
```
✅ Cohere Summarize API: SUCCESS
📝 Summary generated (245 characters)
Summary: HeartSync is an AI-powered social platform...
```

✅ **Chat Classification Test (Classify Alternative):**
```
🔄 Chat API Classification Alternative Test
💡 Using Chat API as replacement for deprecated Classify API
✅ Chat Classification Test: SUCCESS
📝 Classification result: positive
✅ Response contains valid classification
```

### Error Handling Tests

❌ **Invalid API Key Test:**
```
✅ Invalid API Key Test: PASSED
Expected 401 error: {"message":"invalid api token"}
```

⚠️ **Rate Limit Test:**
```
⚠️ Rate Limit Test: Rate limit exceeded (expected behavior)
Error: {"message":"rate limit exceeded"}
```

## API Information

- **Base URL**: `https://api.cohere.ai/v1/`
- **Authentication**: Bearer token in Authorization header
- **Rate Limits**: Trial accounts have monthly limits (check API response headers)
- **Documentation**: [Cohere API Docs](https://docs.cohere.ai/docs)

## Notes

- The test uses the default model (Command Light) which is suitable for most use cases
- Trial accounts have usage limits - check the response headers for remaining calls
- The `model` parameter is not required for the `/v1/generate` endpoint
- Response format differs between `/v1/generate` and `/v1/chat` endpoints
