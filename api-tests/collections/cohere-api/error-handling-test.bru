meta {
  name: Cohere API Error Handling Test
  type: http
  seq: 6
}

post {
  url: https://api.cohere.ai/v1/generate
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer invalid-api-key-for-testing
}

body:json {
  {
    "prompt": "This is a test with invalid API key",
    "max_tokens": 50
  }
}

script:post-response {
  const response = res.getBody();
  
  if (res.status === 401) {
    console.log("✅ Invalid API Key Test: PASSED");
    console.log(`Expected 401 error: ${JSON.stringify(response)}`);
  } else if (res.status === 403) {
    console.log("✅ Invalid API Key Test: PASSED (403 Forbidden)");
    console.log(`Expected 403 error: ${JSON.stringify(response)}`);
  } else {
    console.log(`❌ Invalid API Key Test: UNEXPECTED STATUS (${res.status})`);
    console.log(`Response: ${JSON.stringify(response)}`);
  }
}

tests {
  test("Should return 401 or 403 for invalid API key", function() {
    const status = res.getStatus();
    expect([401, 403]).to.include(status);
  });
  
  test("Should return error message", function() {
    const body = res.getBody();
    expect(body).to.be.an('object');
    // Common error response fields
    const hasErrorField = body.hasOwnProperty('message') || 
                         body.hasOwnProperty('error') || 
                         body.hasOwnProperty('detail');
    expect(hasErrorField).to.be.true;
  });
}
