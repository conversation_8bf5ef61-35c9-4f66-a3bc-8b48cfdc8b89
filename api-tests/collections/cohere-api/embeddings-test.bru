meta {
  name: Cohere Embeddings Test
  type: http
  seq: 3
}

post {
  url: https://api.cohere.ai/v1/embed
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX
}

body:json {
  {
    "texts": ["Hello from HSN Bruno test!", "API health check"],
    "model": "embed-english-v3.0",
    "input_type": "search_document"
  }
}

script:post-response {
  const response = res.getBody();
  
  if (res.status === 200) {
    console.log("✅ Cohere Embeddings API: SUCCESS");
    console.log(`📊 Generated ${response.embeddings.length} embeddings`);
    console.log(`🔢 Embedding dimension: ${response.embeddings[0].length}`);
  } else {
    console.log(`❌ Cohere Embeddings API: FAILED (${res.status})`);
    console.log(`Error: ${JSON.stringify(response)}`);
  }
}

tests {
  test("Should return embeddings", function() {
    expect(res.getStatus()).to.equal(200);
    const body = res.getBody();
    expect(body).to.have.property('embeddings');
    expect(body.embeddings).to.be.an('array');
    expect(body.embeddings.length).to.equal(2);
  });
}
