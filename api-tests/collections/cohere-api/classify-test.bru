meta {
  name: Cohere Classify API Deprecation Notice
  type: http
  seq: 4
}

post {
  url: https://api.cohere.ai/v1/classify
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX
}

body:json {
  {
    "inputs": [
      "This test demonstrates the deprecated Classify API"
    ],
    "examples": [
      {
        "text": "This is a test example",
        "label": "test"
      }
    ]
  }
}

script:post-response {
  const response = res.getBody();

  console.log("⚠️ COHERE CLASSIFY API DEPRECATION NOTICE");
  console.log("📅 As of January 31, 2025, the default Classify endpoint has been deprecated.");
  console.log("🔧 To use classification, you must now fine-tune an Embed model.");
  console.log("📖 See: https://docs.cohere.com/docs/classify-fine-tuning");
  console.log("");

  if (res.status === 400 || res.status === 403) {
    console.log("✅ Expected deprecation error received");
    console.log(`Status: ${res.status}`);
    console.log(`Response: ${JSON.stringify(response)}`);
  } else if (res.status === 200) {
    console.log("⚠️ Unexpected success - API may still be working for existing users");
    console.log(`Response: ${JSON.stringify(response)}`);
  } else {
    console.log(`❓ Unexpected status: ${res.status}`);
    console.log(`Response: ${JSON.stringify(response)}`);
  }
}

tests {
  test("Should handle deprecated API appropriately", function() {
    const status = res.getStatus();
    // Accept various status codes for deprecated API
    // 200: Still working for existing users
    // 400: Bad request (deprecated)
    // 403: Forbidden (deprecated)
    expect([200, 400, 403]).to.include(status);
  });

  test("Should return valid JSON response", function() {
    const body = res.getBody();
    expect(body).to.be.an('object');
  });

  test("If error, should include deprecation information", function() {
    const status = res.getStatus();
    if (status === 400 || status === 403) {
      const body = res.getBody();
      // Check for error message indicating deprecation
      const hasErrorField = body.hasOwnProperty('message') ||
                           body.hasOwnProperty('error') ||
                           body.hasOwnProperty('detail');
      expect(hasErrorField).to.be.true;
    }
  });
}
