meta {
  name: Cohere Chat API for Classification (Classify Alternative)
  type: http
  seq: 9
}

post {
  url: https://api.cohere.ai/v1/chat
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX
}

body:json {
  {
    "message": "Classify the following text as positive, negative, or neutral sentiment:\n\n\"I love this product! It's amazing and works perfectly.\"\n\nRespond with only the classification label.",
    "max_tokens": 50,
    "temperature": 0.1,
    "model": "command-r-plus"
  }
}

script:post-response {
  const response = res.getBody();
  
  console.log("🔄 Chat API Classification Alternative Test");
  console.log("💡 Using Chat API as replacement for deprecated Classify API");
  console.log("");
  
  if (res.status === 200) {
    console.log("✅ Chat Classification Test: SUCCESS");
    console.log(`📝 Classification result: ${response.text}`);
    
    // Check if response contains expected classification labels
    const text = response.text.toLowerCase();
    const hasClassification = text.includes('positive') || 
                             text.includes('negative') || 
                             text.includes('neutral');
    
    if (hasClassification) {
      console.log("✅ Response contains valid classification");
    } else {
      console.log("⚠️ Response may not contain expected classification format");
    }
    
  } else {
    console.log(`❌ Chat Classification Test: FAILED (${res.status})`);
    console.log(`Error: ${JSON.stringify(response)}`);
  }
}

tests {
  test("Should return successful chat response", function() {
    expect(res.getStatus()).to.equal(200);
    const body = res.getBody();
    expect(body).to.have.property('text');
    expect(body.text).to.be.a('string');
  });
  
  test("Response should contain classification-like content", function() {
    const body = res.getBody();
    const text = body.text.toLowerCase();
    
    // Check if response contains sentiment classification terms
    const hasClassificationTerms = text.includes('positive') || 
                                  text.includes('negative') || 
                                  text.includes('neutral') ||
                                  text.includes('sentiment');
    
    expect(hasClassificationTerms).to.be.true;
  });
  
  test("Should include generation metadata", function() {
    const body = res.getBody();
    expect(body).to.have.property('generation_id');
    expect(body.generation_id).to.be.a('string');
  });
  
  test("Response should be concise for classification task", function() {
    const body = res.getBody();
    // Classification responses should be relatively short
    expect(body.text.length).to.be.lessThan(200);
  });
}
