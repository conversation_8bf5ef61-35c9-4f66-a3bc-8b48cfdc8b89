meta {
  name: Tavily API Search Test
  type: http
  seq: 1
}

post {
  url: https://api.tavily.com/search
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "api_key": "{{TAVILY_API_KEY}}",
    "query": "AI assistant health check test",
    "max_results": 3,
    "include_answer": true
  }
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ Tavily API Test: PASSED");
    const body = res.getBody();
    console.log(`Query: ${body.query}`);
    console.log(`Results found: ${body.results?.length || 0}`);
    if (body.answer) {
      console.log(`Answer: ${body.answer.substring(0, 100)}...`);
    }
  } else {
    console.log(`❌ Tavily API Test: FAILED (${res.status})`);
    console.log(`Error: ${JSON.stringify(res.getBody())}`);
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain results", function() {
    const body = res.getBody();
    expect(body).to.have.property('results');
    expect(body.results).to.be.an('array');
  });
  
  test("Response should contain query", function() {
    const body = res.getBody();
    expect(body).to.have.property('query');
    expect(body.query).to.equal('AI assistant health check test');
  });
}
