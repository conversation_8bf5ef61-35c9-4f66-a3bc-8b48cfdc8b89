meta {
  name: OpenRouter API Health Check
  type: http
  seq: 1
}

post {
  url: https://openrouter.ai/api/v1/chat/completions
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{OPENROUTER_API_KEY}}
}

body:json {
  {
    "model": "openai/gpt-4o-mini",
    "messages": [
      {
        "role": "user",
        "content": "Hello! This is a health check from HeartSync API tests. Please respond with a simple confirmation that you're working."
      }
    ],
    "max_tokens": 150,
    "temperature": 0.7
  }
}

script:post-response {
  if (res.status === 200) {
    console.log("✅ OpenRouter API Test: PASSED");
    const body = res.getBody();
    console.log(`Response: ${JSON.stringify(body).substring(0, 200)}...`);
  } else {
    console.log(`❌ OpenRouter API Test: FAILED (${res.status})`);
    console.log(`Error: ${JSON.stringify(res.getBody())}`);
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should be JSON", function() {
    const body = res.getBody();
    expect(body).to.be.an('object');
  });
}
