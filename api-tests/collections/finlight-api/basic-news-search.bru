meta {
  name: Finlight Basic News Search
  type: http
  seq: 1
}

post {
  url: https://api.finlight.me/v2/articles
  body: json
  auth: none
}

headers {
  X-API-KEY: {{FINLIGHT_API_KEY}}
  Content-Type: application/json
  User-Agent: HeartSync-v2/1.0
}

body:json {
  {
    "query": "nvidia",
    "language": "en",
    "pageSize": 5
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain news articles", function() {
    const data = res.getBody();
    expect(data).to.have.property('articles');
    expect(data.articles).to.be.an('array');
  });
  
  test("Response time should be reasonable", function() {
    expect(res.getResponseTime()).to.be.below(5000);
  });
}

docs {
  # Finlight API Basic News Search Test

  This test validates the basic news search functionality of the Finlight API.
  
  ## Test Parameters
  - **query**: Search term (default: "nvidia")
  - **language**: Language filter (default: "en")
  - **limit**: Number of results (default: 10)
  
  ## Expected Response
  - Status: 200 OK
  - Contains: Array of news articles
  - Response time: < 5 seconds
}
