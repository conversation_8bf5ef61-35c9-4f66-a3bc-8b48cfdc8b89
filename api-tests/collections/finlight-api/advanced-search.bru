meta {
  name: Finlight Advanced Search
  type: http
  seq: 2
}

post {
  url: https://api.finlight.me/v2/articles
  body: json
  auth: none
}

headers {
  X-API-KEY: {{FINLIGHT_API_KEY}}
  Content-Type: application/json
  User-Agent: HeartSync-v2/1.0
}

body:json {
  {
    "query": "(\"bull market\" AND nvidia) NOT crypto",
    "language": "en",
    "from": "2025-01-01",
    "to": "2025-07-20",
    "pageSize": 10,
    "sources": ["reuters.com", "bloomberg.com"]
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain articles", function() {
    const data = res.getBody();
    expect(data).to.have.property('articles');
    expect(data.articles).to.be.an('array');
  });
  
  test("Articles should have required fields", function() {
    const data = res.getBody();
    if (data.articles && data.articles.length > 0) {
      const article = data.articles[0];
      expect(article).to.have.property('title');
      expect(article).to.have.property('url');
      expect(article).to.have.property('publishedAt');
    }
  });
  
  test("Response time should be reasonable", function() {
    expect(res.getResponseTime()).to.be.below(10000);
  });
}

docs {
  # Finlight API Advanced Search Test

  This test validates advanced search functionality with:
  - Complex boolean queries
  - Date range filtering
  - Source filtering
  
  ## Features Tested
  - Boolean operators (AND, NOT)
  - Date range filtering (from/to)
  - Source filtering
  - Language filtering
  
  ## Expected Response
  - Status: 200 OK
  - Contains: Array of filtered news articles
  - Response time: < 10 seconds
}
