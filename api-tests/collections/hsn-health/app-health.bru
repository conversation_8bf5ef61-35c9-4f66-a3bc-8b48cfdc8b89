meta {
  name: HSN Application Health Check
  type: http
  seq: 1
}

get {
  url: {{BASE_URL}}/api/health
  body: none
  auth: none
}

script:post-response {
  const health = res.getBody();
  
  console.log("🏥 HSN Health Check Results:");
  console.log(`Status: ${health.status}`);
  console.log(`Message: ${health.message}`);
  
  if (health.features) {
    health.features.forEach((feature, index) => {
      console.log(`  ${index + 1}. ${feature}`);
    });
  }
}

tests {
  test("HSN should be healthy", function() {
    expect(res.getStatus()).to.equal(200);
    const body = res.getBody();
    expect(body.status).to.equal("ok");
  });
  
  test("Should have feature list", function() {
    const body = res.getBody();
    expect(body).to.have.property('features');
    expect(body.features).to.be.an('array');
  });
}
