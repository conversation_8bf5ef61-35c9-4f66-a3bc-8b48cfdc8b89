# 🚀 HeartSync v2 部署指南

## 📋 概覽

HeartSync v2 支援多種部署方式，包括本地開發、Cloudflare Workers 和 Railway 雲端部署。

## 🛠️ 環境要求

- **Node.js**: >= 18.0.0
- **npm**: 最新版本
- **API Keys**: Claude API、OpenRouter API、Tavily API

## 🔧 環境配置

### 1. 環境變數設置

創建 `.env` 文件：

```bash
# Claude API (必需)
CLAUDE_API_KEY=sk-ant-api03-your-key-here

# OpenRouter API (可選，用於多模型支援)
OPENROUTER_API_KEY=your-openrouter-key

# Tavily API (可選，用於知識搜尋)
TAVILY_API_KEY=your-tavily-key

# 環境設定
NODE_ENV=development
```

### 2. 依賴安裝

```bash
npm install
```

## 🏃‍♂️ 本地開發

### 啟動開發服務器

```bash
# Cloudflare Workers 開發模式
npm run dev

# Railway 兼容模式
npm run start:railway
```

### 測試工具

```bash
# 快速 Claude API 檢查
npm run check:claude

# 詳細 Claude API 診斷
npm run diagnose:claude

# 聊天風格測試
npm run test:style

# Claude API 功能測試
npm run test:claude

# 代碼品質檢查
npm run check
```

## ☁️ Cloudflare Workers 部署

### 1. 配置 Wrangler

確保已安裝並配置 Wrangler CLI：

```bash
npm install -g wrangler
wrangler login
```

### 2. 部署到 Cloudflare

```bash
# 生產部署
npm run deploy

# 開發環境部署
npm run dev
```

### 3. 環境變數設置

在 Cloudflare Dashboard 中設置：
- `CLAUDE_API_KEY`
- `OPENROUTER_API_KEY`
- `TAVILY_API_KEY`

## 🚂 Railway 部署

### 1. Railway 配置

項目包含完整的 Railway 配置：

- **配置文件**: `railway.toml`
- **健康檢查**: `/api/health`
- **啟動命令**: `npm run start:railway`

### 2. 部署步驟

```bash
# 安裝 Railway CLI
npm install -g @railway/cli

# 登入 Railway
railway login

# 初始化項目
railway init

# 部署
npm run deploy:railway
```

### 3. 環境變數設置

在 Railway Dashboard 中設置：
- `CLAUDE_API_KEY` (secret)
- `OPENROUTER_API_KEY` (secret)
- `TAVILY_API_KEY` (secret)
- `NODE_ENV=production`

### 4. 資源配置

Railway 配置包含：
- **記憶體**: 512Mi
- **CPU**: 0.5 核心
- **健康檢查超時**: 300 秒
- **重啟策略**: ON_FAILURE (最多 10 次)

## 🔍 健康檢查端點

### `/api/health`

返回系統狀態：

```json
{
  "status": "healthy",
  "timestamp": "2025-06-30T12:00:00.000Z",
  "environment": "production",
  "version": "2.1.0"
}
```

### `/api/test-keys`

測試 API Keys 狀態：

```json
{
  "claude": "configured",
  "openrouter": "configured", 
  "tavily": "configured"
}
```

## 🎯 可用路由

- **主頁**: `/`
- **CLI Preview**: `/CLI-preview`
- **聊天界面**: `/chat-alpine`
- **資料庫測試**: `/database-test`
- **知識原型**: `/knowledge-prototype`

## 🛡️ 安全考量

1. **API Keys**: 使用環境變數，不要提交到版本控制
2. **CORS**: 已配置適當的 CORS 政策
3. **錯誤處理**: 包含完整的錯誤處理機制
4. **速率限制**: 建議在生產環境中實施

## 📊 監控和日誌

### 開發環境

- 控制台日誌
- 錯誤追蹤
- 性能監控

### 生產環境

- Railway 內建監控
- Cloudflare Analytics
- 健康檢查日誌

## 🔧 故障排除

### 常見問題

1. **API Key 錯誤**
   ```bash
   npm run check:claude
   ```

2. **依賴問題**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **端口衝突**
   - 本地開發: 8787
   - Railway: 3000

### 診斷工具

```bash
# 完整系統診斷
npm run diagnose:claude

# 代碼品質檢查
npm run check

# 聊天功能測試
npm run test:style
```

## 📝 更新日誌

### v2.1.0 (2025-06-30)

- ✅ 智能合併衝突解決
- ✅ Railway 部署配置完善
- ✅ CSS 索引優化系統
- ✅ 統一 Node.js 版本要求
- ✅ 完整的測試工具套件

---

💡 **提示**: 如需更多幫助，請查看項目的 README.md 或聯繫開發團隊。
