# 🐋 HeartSync v2 Podman Migration Plan

## 📋 Executive Summary

**Goal**: Migrate HeartSync v2 from monolithic deployment to containerized microservices using Podman.

**Current State**: Monolithic Hono.js application with multiple route handlers
**Target State**: Containerized microservices with route-based separation

## 🎯 Container Architecture

### **Recommended Container Structure**

```
┌─────────────────────────────────────────────────────────────┐
│                    Load Balancer / Reverse Proxy           │
│                    (Traefik or nginx)                      │
└─────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
┌───────────────▼───┐ ┌─────────▼─────┐ ┌─────▼─────┐
│   Core API        │ │  CLI Preview  │ │  AI Chat  │
│   Container       │ │   Container   │ │ Container │
│                   │ │               │ │           │
│ • Main index.js   │ │ • cli-preview │ │ • AI APIs │
│ • Health checks   │ │ • Terminal UI │ │ • Streaming│
│ • Static assets   │ │ • WebSocket   │ │ • Context │
└───────────────────┘ └───────────────┘ └───────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
┌───────────────▼───┐ ┌─────────▼─────┐ ┌─────▼─────┐
│   Database        │ │  Knowledge    │ │  Assets   │
│   Container       │ │   Container   │ │ Container │
│                   │ │               │ │           │
│ • InstantDB       │ │ • Tavily API  │ │ • CSS/JS  │
│ • Connection pool │ │ • Knowledge   │ │ • Images  │
│ • Sync handlers   │ │ • Search      │ │ • CDN     │
└───────────────────┘ └───────────────┘ └───────────┘
```

## 🚀 Implementation Phases

### **Phase 1: Analysis & Preparation** (Week 1)

#### **1.1 Current Architecture Analysis**
- [x] Analyze route structure ✅
- [x] Identify shared dependencies ✅
- [x] Map database connections ✅
- [x] Document API endpoints ✅

#### **1.2 Dependency Analysis**
```bash
# Current dependencies that need containerization:
- @anthropic-ai/sdk: AI integration
- @hono/node-server: Web framework  
- @instantdb/react: Database
- @starfederation/datastar: Frontend framework
- dotenv: Environment management
```

#### **1.3 Environment Setup**
- [x] Podman installed (v5.5.2) ✅
- [ ] Podman Desktop (recommended for GUI management)
- [ ] Container registry setup (Docker Hub or GitHub Container Registry)

### **Phase 2: Containerization** (Week 2)

#### **2.1 Base Container Creation**
```dockerfile
# Dockerfile.base
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY src/ ./src/
EXPOSE 8787
CMD ["node", "src/index.js"]
```

#### **2.2 Service-Specific Containers**

**CLI Preview Container:**
```dockerfile
# Dockerfile.cli-preview
FROM heartsync-base
COPY src/routes/cli-preview* ./src/routes/
COPY src/routes/cli-test* ./src/routes/
ENV SERVICE_TYPE=cli-preview
CMD ["node", "src/services/cli-preview-service.js"]
```

**AI Chat Container:**
```dockerfile  
# Dockerfile.ai-chat
FROM heartsync-base
COPY src/routes/knowledge* ./src/routes/
ENV SERVICE_TYPE=ai-chat
ENV ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
CMD ["node", "src/services/ai-chat-service.js"]
```

#### **2.3 Podman Compose Configuration**
```yaml
# podman-compose.yml
version: '3.8'

services:
  core-api:
    build:
      context: .
      dockerfile: Dockerfile.base
    ports:
      - "8787:8787"
    environment:
      - NODE_ENV=production
      - SERVICE_TYPE=core
    depends_on:
      - database
      
  cli-preview:
    build:
      context: .
      dockerfile: Dockerfile.cli-preview
    ports:
      - "8788:8787"
    environment:
      - NODE_ENV=production
      - SERVICE_TYPE=cli-preview
      
  ai-chat:
    build:
      context: .
      dockerfile: Dockerfile.ai-chat
    ports:
      - "8789:8787"
    environment:
      - NODE_ENV=production
      - SERVICE_TYPE=ai-chat
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    secrets:
      - anthropic_api_key
      
  database:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      
  reverse-proxy:
    image: traefik:v2.10
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/etc/traefik/traefik.yml

volumes:
  redis_data:
  
secrets:
  anthropic_api_key:
    file: ./.secrets/anthropic_api_key
```

### **Phase 3: Service Extraction** (Week 3)

#### **3.1 Route Separation Strategy**

**High-Priority Routes for Containerization:**
1. **CLI Preview Service** (`/CLI-preview`)
   - Heavy WebSocket usage
   - Terminal UI rendering
   - Independent scaling needs

2. **AI Chat Service** (`/chat-alpine`, `/knowledge-prototype`)
   - CPU-intensive AI processing
   - External API rate limiting
   - Memory-intensive context management

3. **Database Service** (`/database-test`, InstantDB routes)
   - Connection pooling
   - Real-time sync management
   - Data consistency requirements

#### **3.2 Service Communication**
```javascript
// Inter-service communication setup
const serviceRegistry = {
  'cli-preview': 'http://cli-preview:8787',
  'ai-chat': 'http://ai-chat:8787',
  'database': 'http://database:8787'
};

// Service discovery middleware
app.use('/api/services/*', async (c, next) => {
  const serviceName = c.req.path.split('/')[3];
  const serviceUrl = serviceRegistry[serviceName];
  
  if (!serviceUrl) {
    return c.json({ error: 'Service not found' }, 404);
  }
  
  // Proxy request to appropriate service
  const response = await fetch(`${serviceUrl}${c.req.path}`);
  return response;
});
```

### **Phase 4: Testing & Optimization** (Week 4)

#### **4.1 Container Testing**
```bash
# Test individual containers
podman build -t heartsync-core -f Dockerfile.base .
podman run -p 8787:8787 heartsync-core

# Test service communication
podman-compose up -d
curl http://localhost:8787/health
curl http://localhost:8788/CLI-preview
```

#### **4.2 Performance Optimization**
- Container resource limits
- Multi-stage builds for smaller images
- Layer caching optimization
- Health check implementation

## 🔍 Study Areas & Further Research

### **1. Container Orchestration**
- **Kubernetes**: For production-scale deployment
- **Podman Desktop**: GUI management for development
- **Buildah**: Advanced container building

### **2. Service Mesh** (Future)
- **Istio**: Traffic management and security
- **Linkerd**: Lightweight service mesh
- **Consul Connect**: Service discovery and configuration

### **3. Monitoring & Observability**
- **Prometheus**: Metrics collection
- **Grafana**: Visualization and dashboards  
- **Jaeger**: Distributed tracing
- **Loki**: Log aggregation

### **4. Security Considerations**
- **Rootless containers**: Podman's security advantage
- **Secret management**: Podman secrets or external vault
- **Network policies**: Container communication restrictions
- **Image scanning**: Vulnerability detection

## ⚠️ Potential Challenges & Solutions

### **Challenge 1: State Management**
**Problem**: Shared state between services
**Solution**: 
- Redis for session management
- InstantDB for application state
- Event-driven architecture

### **Challenge 2: Database Connections**
**Problem**: Connection pooling across containers
**Solution**:
- Database connection service
- Connection proxy pattern
- Connection pool per service

### **Challenge 3: Real-time Features**
**Problem**: WebSocket connections across services
**Solution**:
- WebSocket gateway service
- Redis pub/sub for message passing
- Service-to-service WebSocket proxying

### **Challenge 4: AI API Rate Limits**
**Problem**: Shared API quotas across containers
**Solution**:
- Centralized AI service
- Request queuing system
- Rate limiting middleware

## 📊 Migration Risk Assessment

### **Low Risk** ✅
- Static asset serving
- Basic route handlers
- Health checks
- Environment configuration

### **Medium Risk** ⚠️
- Database connection management
- Session state handling
- Inter-service communication
- Load balancing configuration

### **High Risk** ⚠️
- Real-time WebSocket connections
- AI API integration
- InstantDB synchronization
- Performance optimization

## 🎯 Success Metrics

### **Technical Metrics**
- Container startup time < 30 seconds
- Service response time < 200ms
- Memory usage per container < 512MB
- 99.9% uptime for core services

### **Development Metrics**
- Independent service deployment
- Faster development cycles
- Better resource utilization
- Improved fault isolation

## 🔧 Recommended Tools & Resources

### **Development Tools**
- **Podman Desktop**: GUI management
- **Buildah**: Container building
- **Skopeo**: Container image operations
- **Podman Compose**: Multi-container orchestration

### **Monitoring & Debugging**
- **Podman logs**: Container log aggregation
- **Podman stats**: Resource usage monitoring
- **Podman inspect**: Container configuration analysis
- **Podman top**: Process monitoring

### **Learning Resources**
- [Podman Tutorial](https://github.com/containers/podman/blob/main/docs/tutorials/README.md)
- [Container Best Practices](https://developers.redhat.com/blog/2019/04/25/podman-basics-cheat-sheet)
- [Microservices Patterns](https://microservices.io/patterns/)
- [Node.js Containerization Guide](https://nodejs.org/en/docs/guides/nodejs-docker-webapp/)

## 🚀 Next Steps

1. **Immediate Actions**:
   - Create base Dockerfile
   - Set up development environment
   - Implement health checks

2. **Week 1 Goals**:
   - Complete dependency analysis
   - Create container configurations
   - Test basic containerization

3. **Week 2 Goals**:
   - Implement service separation
   - Set up inter-service communication
   - Create Podman Compose configuration

4. **Week 3 Goals**:
   - Migrate high-priority routes
   - Implement monitoring
   - Performance optimization

5. **Week 4 Goals**:
   - Production testing
   - Documentation
   - Deployment automation

---

**🎯 This is a solid, well-planned approach that will significantly improve your application's scalability, maintainability, and deployment flexibility.**
