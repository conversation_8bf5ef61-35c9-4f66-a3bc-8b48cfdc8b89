name = "heartsync-v2"
main = "src/index.js"
compatibility_date = "2025-06-05"
compatibility_flags = ["nodejs_compat"]

# 🔧 HSN-61 Phase 2: 靜態文件服務配置
# 支援 /src/* 路徑的 JavaScript 模組載入和根目錄 HTML 文件
[site]
bucket = "./"
include = ["src/**/*.js", "src/**/*.json", "src/**/*.css", "*.html"]
exclude = ["node_modules/**", ".git/**", "*.md"]

# Development settings
[env.development.vars]
ENVIRONMENT = "development"
COHERE_API_KEY = "VMlpYnFDgEue4IQVaGabtSwCAeJBo2dpXciGtGDX"
# 🔧 HSN-61 Phase 2: 臨時添加 API 密鑰用於測試
CLAUDE_API_KEY = "************************************************************************************************************"
OPENROUTER_API_KEY = "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"
OPENROUTER_REFERER = "https://heartsync.dev"
TAVILY_API_KEY = "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"
FINLIGHT_API_KEY = "sk_a785e99eee3015b5491e966020bf744c1b95c37569c60b09b02190eeae2b601d"
# Turso LibSQL Database Configuration
TURSO_DATABASE_URL = "libsql://heartsync-v2-weiss.aws-ap-south-1.turso.io"
TURSO_AUTH_TOKEN = "***************************************************************************************************************************************************************************************************************************************************************************"
# ⚠️ SECURITY: 生產環境應使用 wrangler secrets
# Use: wrangler secret put CLAUDE_API_KEY --env production
# Use: wrangler secret put OPENROUTER_API_KEY --env production
# Use: wrangler secret put TURSO_DATABASE_URL --env production
# Use: wrangler secret put TURSO_AUTH_TOKEN --env production

# Production settings
[env.production.vars]
ENVIRONMENT = "production"
# ⚠️ SECURITY: API Keys should be set using wrangler secrets
# Use: wrangler secret put CLAUDE_API_KEY --env production
# Use: wrangler secret put OPENROUTER_API_KEY --env production
# Use: wrangler secret put TAVILY_API_KEY --env production
# Use: wrangler secret put FINLIGHT_API_KEY --env production
# Use: wrangler secret put COHERE_API_KEY --env production

# Future database bindings (when needed)
# [[env.production.d1_databases]]
# binding = "DB"
# database_name = "heartsync-production"
# database_id = "your-database-id"

# Future KV storage bindings (when needed)
# [[env.production.kv_namespaces]]
# binding = "SESSIONS"
# id = "sessions_kv_id"
