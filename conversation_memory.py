import os
from mem0 import MemoryClient
from datetime import datetime

# Set up the mem0 client
os.environ["MEM0_API_KEY"] = "m0-Yth3aS1H72Yipwi4ieeEw1UlwqfECVIvGOnYQ4Qj"
client = MemoryClient()

USER_ID = "Weiss@Warp"

def store_conversation_memory(content, context="conversation"):
    """Store a conversation highlight or key information"""
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
        memory_content = f"[{timestamp}] {context.title()}: {content}"
        
        result = client.add(
            messages=[
                {"role": "assistant", "content": memory_content}
            ],
            user_id=USER_ID
        )
        print(f"💾 Stored conversation memory: {content}")
        return result
    except Exception as e:
        print(f"❌ Failed to store conversation memory: {e}")
        return None

def store_current_session():
    """Store key points from our current conversation session"""
    print("🤖 Storing highlights from our current conversation...")
    
    # Store key information about what we accomplished today
    memories_to_store = [
        "Successfully set up mem0 client with mem0ai package installation in virtual environment",
        "Created working mem0_client.py with comprehensive testing functionality",
        "Configured user_id as 'Weiss@Warp' for personal memory storage",
        "Built mem0_utils.py with helper functions for memory management",
        "Discovered mem0 FREE plan limitations: requires user_id, search limited, graph memories need PRO",
        "Successfully added and retrieved memories including Python preferences, Warp terminal usage, and AI assistant preferences",
        "Created conversation_memory.py to store our interaction highlights in mem0"
    ]
    
    stored_count = 0
    for memory in memories_to_store:
        if store_conversation_memory(memory, "session_highlight"):
            stored_count += 1
    
    print(f"✅ Stored {stored_count}/{len(memories_to_store)} conversation memories")
    return stored_count

def get_conversation_history():
    """Retrieve conversation-related memories"""
    try:
        all_memories = client.get_all(user_id=USER_ID)
        
        # Filter for conversation/session memories
        conversation_memories = [
            memory for memory in all_memories 
            if any(keyword in memory.get('memory', '').lower() 
                  for keyword in ['conversation', 'session', 'accomplished', 'created', 'built', 'discovered'])
        ]
        
        print(f"📚 Found {len(conversation_memories)} conversation-related memories")
        return conversation_memories
    except Exception as e:
        print(f"❌ Failed to get conversation history: {e}")
        return []

def show_conversation_summary():
    """Display a summary of our conversation history"""
    print("📋 Conversation History Summary")
    print("=" * 50)
    
    memories = get_conversation_history()
    if memories:
        for i, memory in enumerate(memories, 1):
            content = memory.get('memory', 'No content')
            created = memory.get('created_at', 'Unknown time')
            print(f"{i}. {content}")
            print(f"   📅 {created}")
            print()
    else:
        print("No conversation memories found yet.")

def test_simple_conversation_storage():
    """Test storing a simple conversation memory"""
    print("🧪 Testing simple conversation memory storage...")
    
    # Store a simple conversation memory
    result = store_conversation_memory(
        "Today I learned about mem0 and set up memory storage for conversations", 
        "learning"
    )
    
    # Check all memories
    all_memories = client.get_all(user_id=USER_ID)
    print(f"\n📊 Total memories now: {len(all_memories)}")
    
    # Show the most recent few
    print("\n🔍 Recent memories:")
    for i, memory in enumerate(all_memories[-3:], 1):
        content = memory.get('memory', 'No content')
        categories = memory.get('categories', [])
        print(f"{i}. {content}")
        print(f"   🏷️  Categories: {categories}")
        print()

if __name__ == "__main__":
    test_simple_conversation_storage()
