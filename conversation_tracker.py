import os
from mem0 import MemoryClient
from datetime import datetime

# Set up the mem0 client
os.environ["MEM0_API_KEY"] = "m0-Yth3aS1H72Yipwi4ieeEw1UlwqfECVIvGOnYQ4Qj"
client = MemoryClient()

USER_ID = "Weiss@Warp"

def log_interaction(interaction_type, details):
    """Log a specific interaction or learning moment"""
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d")
        content = f"On {timestamp}, {interaction_type}: {details}"
        
        result = client.add(
            messages=[
                {"role": "user", "content": content}
            ],
            user_id=USER_ID
        )
        print(f"📝 Logged: {interaction_type} - {details}")
        return result
    except Exception as e:
        print(f"❌ Failed to log interaction: {e}")
        return None

def record_todays_session():
    """Record what we accomplished in today's session"""
    print("📋 Recording today's AI assistance session...")
    
    interactions = [
        ("setup_task", "Set up mem0 memory system with Python virtual environment"),
        ("problem_solving", "Debugged mem0 installation issues and found correct package name 'mem0ai'"),
        ("configuration", "Configured mem0 client with API key and user_id '<PERSON>@Warp'"),
        ("file_creation", "Created mem0_client.py, mem0_utils.py, and conversation tracking utilities"),
        ("discovery", "Learned mem0 FREE plan requires user_id for operations and has search limitations"),
        ("success", "Successfully stored and retrieved personal preference memories in mem0"),
        ("conversation_experiment", "Explored using mem0 to remember our AI assistant conversations")
    ]
    
    success_count = 0
    for interaction_type, details in interactions:
        if log_interaction(interaction_type, details):
            success_count += 1
    
    print(f"✅ Recorded {success_count}/{len(interactions)} interactions")
    
    # Also log this meta-conversation about memory
    log_interaction("meta_conversation", "Discussed using mem0 to remember our conversations and created tracking system")
    
    return success_count

def show_interaction_log():
    """Show all logged interactions"""
    try:
        memories = client.get_all(user_id=USER_ID)
        print(f"\n🗂️  Total memories stored: {len(memories)}")
        print("="*60)
        
        for i, memory in enumerate(memories, 1):
            content = memory.get('memory', '')
            categories = memory.get('categories', [])
            created = memory.get('created_at', '')
            
            print(f"{i}. {content}")
            print(f"   📅 Created: {created}")
            print(f"   🏷️  Categories: {', '.join(categories)}")
            print()
            
    except Exception as e:
        print(f"❌ Failed to retrieve interaction log: {e}")

if __name__ == "__main__":
    record_todays_session()
    print("\n" + "="*60)
    show_interaction_log()
