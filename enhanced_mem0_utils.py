#!/usr/bin/env python3
"""
Enhanced Mem0 Utilities with Persistent Agent Configuration
Uses App ID and Agent ID from .agent_config.json for all operations
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Optional
from agent_config_utils import AgentConfig

class EnhancedMem0Client:
    def __init__(self):
        self.config = AgentConfig()
        self.api_key = os.getenv("MEM0_API_KEY")
        
        if not self.api_key:
            raise ValueError("MEM0_API_KEY environment variable not set")
        
        try:
            from mem0 import MemoryClient
            self.client = MemoryClient(api_key=self.api_key)
        except ImportError:
            raise ImportError("mem0 package not installed. Run: pip install mem0ai")
    
    def add_memory(self, text: str, metadata: Optional[Dict] = None) -> str:
        """Add a memory with persistent agent metadata"""
        enhanced_metadata = self.config.get_mem0_metadata()
        if metadata:
            enhanced_metadata.update(metadata)
        
        result = self.client.add(
            messages=[{"role": "user", "content": text}],
            user_id=self.config.user_id,
            agent_id=self.config.agent_id,
            app_id=self.config.app_id,
            metadata=enhanced_metadata
        )
        return result
    
    def search_memories(self, query: str, limit: int = 10) -> List[Dict]:
        """Search memories with agent context"""
        return self.client.search(
            query=query,
            user_id=self.config.user_id,
            agent_id=self.config.agent_id,
            app_id=self.config.app_id,
            limit=limit
        )
    
    def get_all_memories(self) -> List[Dict]:
        """Get all memories for the current user"""
        return self.client.get_all(
            user_id=self.config.user_id,
            agent_id=self.config.agent_id,
            app_id=self.config.app_id
        )
    
    def get_memory_stats(self) -> Dict:
        """Get memory statistics"""
        memories = self.get_all_memories()
        
        # Count by app_id and agent_id
        app_counts = {}
        agent_counts = {}
        total_count = len(memories)
        
        for memory in memories:
            metadata = memory.get('metadata', {})
            app_id = metadata.get('app_id', 'unknown')
            agent_id = metadata.get('agent_id', 'unknown')
            
            app_counts[app_id] = app_counts.get(app_id, 0) + 1
            agent_counts[agent_id] = agent_counts.get(agent_id, 0) + 1
        
        return {
            "total_memories": total_count,
            "current_app_id": self.config.app_id,
            "current_agent_id": self.config.agent_id,
            "memories_by_app": app_counts,
            "memories_by_agent": agent_counts,
            "current_app_memories": app_counts.get(self.config.app_id, 0),
            "current_agent_memories": agent_counts.get(self.config.agent_id, 0)
        }
    
    def display_stats(self):
        """Display memory statistics"""
        stats = self.get_memory_stats()
        
        print("📊 Memory Statistics")
        print(f"├── Total Memories: {stats['total_memories']}")
        print(f"├── Current App ID: {stats['current_app_id']}")
        print(f"├── Current Agent ID: {stats['current_agent_id']}")
        print(f"├── Memories for Current App: {stats['current_app_memories']}")
        print(f"└── Memories for Current Agent: {stats['current_agent_memories']}")
        
        if len(stats['memories_by_app']) > 1:
            print("\n📱 Memories by App:")
            for app_id, count in stats['memories_by_app'].items():
                marker = "🟢" if app_id == stats['current_app_id'] else "🔵"
                short_id = app_id[:8] + "..." if len(app_id) > 8 else app_id
                print(f"   {marker} {short_id}: {count}")
    
    def track_conversation(self, messages: List[str], context: str = ""):
        """Track a conversation with enhanced metadata"""
        conversation_text = f"Context: {context}\n\n" + "\n".join(messages)
        
        metadata = {
            "type": "conversation",
            "message_count": len(messages),
            "context": context
        }
        
        return self.add_memory(conversation_text, metadata)

def main():
    import sys
    
    try:
        client = EnhancedMem0Client()
        
        if len(sys.argv) > 1:
            command = sys.argv[1]
            
            if command == "stats":
                client.display_stats()
            elif command == "config":
                client.config.display_config()
            elif command == "add" and len(sys.argv) > 2:
                text = " ".join(sys.argv[2:])
                result = client.add_memory(text)
                print(f"✅ Memory added: {result}")
            elif command == "search" and len(sys.argv) > 2:
                query = " ".join(sys.argv[2:])
                results = client.search_memories(query)
                print(f"🔍 Found {len(results)} memories for '{query}':")
                for i, memory in enumerate(results):
                    print(f"   {i+1}. {memory['text'][:100]}...")
            else:
                print("Usage:")
                print("  python enhanced_mem0_utils.py stats     - Show memory statistics")
                print("  python enhanced_mem0_utils.py config    - Show agent configuration")
                print("  python enhanced_mem0_utils.py add <text> - Add a memory")
                print("  python enhanced_mem0_utils.py search <query> - Search memories")
        else:
            client.display_stats()
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
