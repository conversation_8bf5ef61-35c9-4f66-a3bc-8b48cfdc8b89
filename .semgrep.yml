# 🛡️ HeartSync Network (HSN) 專用 Semgrep 安全規則
# 針對 Hono.js + Alpine.js + InstantDB 技術棧的安全檢查

rules:
  # 🔐 API 密鑰洩露檢測
  - id: hsn-api-key-exposure
    pattern-either:
      - pattern: |
          const $KEY = "$VALUE"
      - pattern: |
          let $KEY = "$VALUE"
      - pattern: |
          var $KEY = "$VALUE"
    metavariable-regex:
      metavariable: $KEY
      regex: (?i)(api[_-]?key|secret|token|password|auth)
    metavariable-regex:
      metavariable: $VALUE
      regex: (sk-ant-api|or-|tvly-|[a-zA-Z0-9]{20,})
    message: "Potential API key or secret hardcoded in source code"
    severity: ERROR
    languages: [javascript, typescript]

  # 🌐 Hono.js 路由安全檢查
  - id: hsn-hono-cors-missing
    pattern: |
      app.get($PATH, ...)
    metavariable-regex:
      metavariable: $PATH
      regex: /api/.*
    message: "API route may need CORS configuration for security"
    severity: WARNING
    languages: [javascript, typescript]

  # 🔒 Alpine.js XSS 防護檢查
  - id: hsn-alpine-xss-risk
    pattern-either:
      - pattern: |
          x-html="$CONTENT"
      - pattern: |
          innerHTML = $CONTENT
    message: "Potential XSS vulnerability with dynamic HTML content"
    severity: ERROR
    languages: [javascript, typescript]

  # 📊 InstantDB 配置安全檢查
  - id: hsn-instantdb-config-exposure
    pattern-either:
      - pattern: |
          init({ appId: "$APP_ID" })
      - pattern: |
          const config = { appId: "$APP_ID" }
    metavariable-regex:
      metavariable: $APP_ID
      regex: [a-f0-9-]{36}
    message: "InstantDB App ID should be stored in environment variables"
    severity: WARNING
    languages: [javascript, typescript]

  # 🚫 Console.log 在生產環境
  - id: hsn-console-log-production
    pattern-either:
      - pattern: console.log(...)
      - pattern: console.debug(...)
      - pattern: console.info(...)
    message: "Console statements should be removed in production code"
    severity: INFO
    languages: [javascript, typescript]

  # 🔓 不安全的 eval 使用
  - id: hsn-unsafe-eval
    pattern-either:
      - pattern: eval(...)
      - pattern: Function(...)
      - pattern: setTimeout($STR, ...)
      - pattern: setInterval($STR, ...)
    message: "Avoid using eval() or Function() constructor for security"
    severity: ERROR
    languages: [javascript, typescript]

  # 🌍 環境變數洩露
  - id: hsn-env-var-exposure
    pattern-either:
      - pattern: |
          process.env.$VAR
      - pattern: |
          import.meta.env.$VAR
    metavariable-regex:
      metavariable: $VAR
      regex: (?i)(key|secret|token|password|auth)
    message: "Environment variable containing sensitive data accessed directly"
    severity: WARNING
    languages: [javascript, typescript]

  # 🔗 不安全的 URL 重定向
  - id: hsn-unsafe-redirect
    pattern-either:
      - pattern: |
          window.location = $URL
      - pattern: |
          location.href = $URL
      - pattern: |
          c.redirect($URL)
    message: "Potential open redirect vulnerability"
    severity: ERROR
    languages: [javascript, typescript]

  # 📝 SQL 注入風險 (如果使用資料庫)
  - id: hsn-sql-injection-risk
    pattern-either:
      - pattern: |
          query($SQL + ...)
      - pattern: |
          execute($SQL + ...)
      - pattern: |
          raw($SQL + ...)
    message: "Potential SQL injection vulnerability - use parameterized queries"
    severity: ERROR
    languages: [javascript, typescript]

  # 🔐 弱密碼驗證
  - id: hsn-weak-password-validation
    pattern-either:
      - pattern: |
          password.length < 8
      - pattern: |
          pwd.length < 6
    message: "Password validation may be too weak"
    severity: WARNING
    languages: [javascript, typescript]

  # 🍪 不安全的 Cookie 設定
  - id: hsn-insecure-cookie
    pattern-either:
      - pattern: |
          setCookie($NAME, $VALUE)
      - pattern: |
          document.cookie = $COOKIE
    message: "Cookie should include secure, httpOnly, and sameSite attributes"
    severity: WARNING
    languages: [javascript, typescript]

  # 🔄 CSRF 保護檢查
  - id: hsn-missing-csrf-protection
    pattern: |
      app.post($PATH, ...)
    metavariable-regex:
      metavariable: $PATH
      regex: /api/.*
    message: "POST endpoint may need CSRF protection"
    severity: INFO
    languages: [javascript, typescript]

  # 📡 不安全的 HTTP 請求
  - id: hsn-insecure-http-request
    pattern-either:
      - pattern: |
          fetch("http://...")
      - pattern: |
          axios.get("http://...")
      - pattern: |
          request("http://...")
    message: "Use HTTPS instead of HTTP for external requests"
    severity: WARNING
    languages: [javascript, typescript]

  # 🎯 HSN 特定：Claude API 安全使用
  - id: hsn-claude-api-security
    pattern-either:
      - pattern: |
          anthropic.messages.create({ model: $MODEL, messages: $MESSAGES })
    message: "Ensure Claude API requests include proper input validation and rate limiting"
    severity: INFO
    languages: [javascript, typescript]

  # 🔍 HSN 特定：Tavily API 安全使用
  - id: hsn-tavily-api-security
    pattern-either:
      - pattern: |
          tavily.search({ query: $QUERY })
    message: "Ensure Tavily API queries are properly sanitized"
    severity: INFO
    languages: [javascript, typescript]
