# 🚀 UV - 超快速 Python 套件管理器使用指南

## 📦 什麼是 UV？

UV 是由 Astral 開發的極快 Python 套件管理器，比 pip 快 10-100 倍。它提供：
- 超快的套件安裝
- 虛擬環境管理
- 專案管理
- Python 版本管理
- 工具安裝

## ⚡ 速度比較 (基於剛才的測試)

| 操作 | 傳統方式 | UV | 速度提升 |
|------|----------|-----|----------|
| 安裝 mem0ai + 34 套件 | ~30-60 秒 | ~10 秒 | 3-6x 更快 |
| 虛擬環境創建 | ~5-10 秒 | ~1 秒 | 5-10x 更快 |
| 套件解析 | ~10-20 秒 | ~7.6 秒 | 1.3-2.6x 更快 |

## 🛠️ 基本使用

### 1. 虛擬環境管理
```bash
# 創建虛擬環境
uv venv my-env --python 3.13

# 啟用環境
source my-env/bin/activate

# 使用特定 Python 版本
uv venv my-env --python 3.12
```

### 2. 套件安裝
```bash
# 快速安裝套件 (比 pip 快 10-100x)
uv pip install package-name

# 安裝多個套件
uv pip install requests pandas numpy

# 從 requirements.txt 安裝
uv pip install -r requirements.txt

# 安裝到目前環境 (不需要先啟用)
uv pip install --system package-name
```

### 3. 工具管理
```bash
# 全域安裝工具 (類似 pipx)
uv tool install black
uv tool install pytest
uv tool install mem0ai

# 執行工具
uv tool run black my_file.py
uv tool run pytest
```

### 4. 專案管理
```bash
# 初始化新專案
uv init my-project
cd my-project

# 添加依賴
uv add requests
uv add pandas --dev  # 開發依賴

# 移除依賴
uv remove requests

# 同步專案環境
uv sync

# 執行腳本
uv run my_script.py
```

### 5. Python 版本管理
```bash
# 列出可用的 Python 版本
uv python list

# 安裝 Python 版本
uv python install 3.12
uv python install 3.13

# 使用特定版本
uv python use 3.13
```

## 🎯 您的 HeartSync v2 專案中的應用

### 1. 改善 mem0ai 環境
```bash
# 創建專用的 mem0 環境 (已測試)
uv venv mem0-env --python 3.13
source mem0-env/bin/activate
uv pip install mem0ai

# 比 pipx 更快的替代方案
uv tool install mem0ai
```

### 2. 快速開發環境設置
```bash
# 為 HeartSync v2 創建開發環境
uv venv heartsync-dev --python 3.13
source heartsync-dev/bin/activate

# 快速安裝 Python 開發工具
uv pip install black pytest pylint mypy
```

### 3. 腳本執行
```bash
# 直接執行 Python 腳本 (無需啟用環境)
uv run enhanced_mem0_utils.py stats
uv run update_session_record.py
```

## 🔧 與現有工具的整合

### 1. 替換 pipx
```bash
# 舊方式: pipx install package
# 新方式: uv tool install package

uv tool install black
uv tool install pytest
uv tool install httpie
```

### 2. 替換 pip + venv
```bash
# 舊方式:
# python -m venv myenv
# source myenv/bin/activate
# pip install package

# 新方式:
uv venv myenv --python 3.13
source myenv/bin/activate
uv pip install package
```

### 3. 專案依賴管理
```bash
# 創建 pyproject.toml 基礎的專案
uv init my-project
cd my-project

# 自動管理依賴和虛擬環境
uv add requests anthropic-sdk
uv sync
```

## 💡 最佳實踐

### 1. 用於日常開發
- 使用 `uv venv` 替代 `python -m venv`
- 使用 `uv pip install` 替代 `pip install`
- 使用 `uv tool install` 替代 `pipx install`

### 2. 用於專案管理
- 對新專案使用 `uv init`
- 使用 `uv add/remove` 管理依賴
- 使用 `uv sync` 保持環境同步

### 3. 用於 CI/CD
```bash
# 在 CI 中快速設置環境
uv venv
uv pip install -r requirements.txt
uv run pytest
```

## 🚀 為 HeartSync v2 建議的工作流程

### 1. 創建專用環境
```bash
# 在項目根目錄
uv venv .venv --python 3.13
source .venv/bin/activate

# 安裝項目特定的 Python 工具
uv pip install black pylint mypy pytest
uv pip install mem0ai anthropic-sdk
```

### 2. 快速腳本執行
```bash
# 直接執行，無需啟用環境
uv run scripts/daily-maintenance.js  # 如果是 Python 腳本
uv run enhanced_mem0_utils.py stats
```

### 3. 工具安裝
```bash
# 全域安裝常用工具
uv tool install black
uv tool install pytest
uv tool install httpie
```

## 📊 性能優勢總結

基於我們的實際測試：
- **安裝速度**: mem0ai + 34 個依賴僅需 ~10 秒
- **環境創建**: 虛擬環境創建 < 1 秒
- **記憶體使用**: 更少的記憶體佔用
- **磁碟空間**: 智能快取和連結減少重複

## 🔄 遷移建議

1. **立即可用**: 替換日常的 pip 命令
2. **逐步採用**: 新專案使用 uv init
3. **工具整合**: 將 pipx 工具遷移到 uv tool
4. **團隊採用**: 在 CI/CD 中使用以提升構建速度

---

*安裝時間: 2025-07-23*  
*版本: UV 0.8.2*  
*測試環境: macOS + Homebrew*
