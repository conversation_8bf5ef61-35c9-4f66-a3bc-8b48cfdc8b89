{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off"}, "style": {"useConst": "error", "useTemplate": "error"}, "correctness": {"noUnusedVariables": "warn", "useValidForDirection": "error"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "tab", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always"}}, "files": {"includes": ["src/**/*.js", "src/**/*.ts", "scripts/**/*.js", "*.js", "*.ts"], "ignoreUnknown": false}}