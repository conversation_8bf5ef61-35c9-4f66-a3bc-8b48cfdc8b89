{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "jsxImportSource": "react", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "declaration": false, "noUnusedLocals": false, "noUnusedParameters": false}}