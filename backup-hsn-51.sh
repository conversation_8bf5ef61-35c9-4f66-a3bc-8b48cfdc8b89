#!/bin/bash

# HSN-51 快速修補方案 - 本地備份腳本
# 執行日期: $(date)
# 目的: 在實施 HSN-51 修補前進行本地備份

echo "🚨 HSN-51 快速修補方案 - 本地備份腳本"
echo "================================================"

# 創建備份目錄
BACKUP_DIR="./backups/hsn-51-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📁 備份目錄: $BACKUP_DIR"

# 備份主要文件
echo "📋 備份文件列表:"

# 1. CLI-preview-B 原始文件
if [ -f "src/routes/cli-preview-b.js" ]; then
    cp "src/routes/cli-preview-b.js" "$BACKUP_DIR/cli-preview-b-original.js"
    echo "✅ cli-preview-b.js (原始版本)"
else
    echo "❌ cli-preview-b.js 不存在"
fi

# 2. 相關的 CLI preview 文件
if [ -f "src/routes/cli-preview.js" ]; then
    cp "src/routes/cli-preview.js" "$BACKUP_DIR/cli-preview-original.js"
    echo "✅ cli-preview.js (參考版本)"
fi

if [ -f "src/routes/cli-preview-a.js" ]; then
    cp "src/routes/cli-preview-a.js" "$BACKUP_DIR/cli-preview-a-original.js"
    echo "✅ cli-preview-a.js (參考版本)"
fi

# 3. 測試文件
if [ -f "test-hsn-51-patch.html" ]; then
    cp "test-hsn-51-patch.html" "$BACKUP_DIR/"
    echo "✅ test-hsn-51-patch.html (測試文件)"
fi

# 4. 相關文檔
if [ -f "docs/HSN-CLI-PREVIEW-FIXES.md" ]; then
    cp "docs/HSN-CLI-PREVIEW-FIXES.md" "$BACKUP_DIR/"
    echo "✅ HSN-CLI-PREVIEW-FIXES.md (修正文檔)"
fi

# 5. 創建修補說明文件
cat > "$BACKUP_DIR/HSN-51-PATCH-INFO.md" << 'EOF'
# HSN-51 快速修補方案備份

## 修補概要
- **目標**: CLI-preview-B 重複訊息問題快速修補
- **方法**: AI SystemPrompt 增強 + 狀態代碼系統
- **備份時間**: $(date)

## 修補內容

### 1. SystemPrompt 增強
- 告知 AI 關於重複訊息問題
- 指示 AI 預設忽略重複問題
- 要求在回覆末尾添加狀態代碼

### 2. 狀態代碼系統
- [OK] = 正常回應，無重複檢測
- [DUP] = 檢測到可能的重複訊息  
- [ERR] = 系統處理異常
- [API] = API 調用異常
- [NET] = 網路連線問題
- [SIM] = 模擬回應模式

### 3. 檢測機制
- 訊息雜湊生成器
- 狀態代碼提取器
- 除錯日誌記錄
- UI 狀態顯示

## 測試方法
1. 開啟 CLI-preview-B
2. 啟用除錯模式 (/debug)
3. 發送測試訊息
4. 觀察狀態代碼
5. 檢查重複檢測功能

## 回滾方法
如需回滾，請使用備份的原始文件：
```bash
cp backups/hsn-51-*/cli-preview-b-original.js src/routes/cli-preview-b.js
```

## 相關文件
- cli-preview-b-original.js (修補前原始文件)
- test-hsn-51-patch.html (測試頁面)
- HSN-CLI-PREVIEW-FIXES.md (相關修正文檔)
EOF

# 6. 創建文件清單
echo "📝 創建文件清單..."
ls -la "$BACKUP_DIR" > "$BACKUP_DIR/file-list.txt"

# 7. 計算文件大小
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)

echo ""
echo "✅ 備份完成!"
echo "📁 備份位置: $BACKUP_DIR"
echo "📊 備份大小: $BACKUP_SIZE"
echo ""
echo "🔧 下一步:"
echo "1. 檢查備份文件完整性"
echo "2. 測試 CLI-preview-B 修補版本"
echo "3. 驗證狀態代碼系統功能"
echo "4. 觀察重複訊息檢測效果"
echo ""
echo "⚠️  如需回滾，請使用備份的原始文件"
echo "================================================"
