# 🚀 HeartSync 備份快速參考

## 📋 必記命令

### ⚡ 快速狀態檢查
```bash
npm run status:quick                  # 超快速狀態概覽
npm run status                        # 詳細健康檢查
```

### 🔍 變更前檢查
```bash
npm run risk:check                    # 風險評估
npm run risk:check src/index.js       # 檢查特定文件
```

### 💾 創建備份
```bash
npm run backup                        # 標準備份
npm run backup:list                   # 查看備份列表
```

### 🔄 Git 歷史恢復
```bash
npm run recovery:git                  # 從 Git 恢復文件
npm run recovery:list                 # 查看所有恢復
npm run recovery:files <timestamp>    # 查看特定恢復的文件
```

### 🛡️ 安全操作
```bash
npm run safe:cleanup                  # 安全清理 (檢查+備份+清理)
npm run safe:deploy                   # 安全部署 (檢查+備份+部署)
```

## ⚡ 快速決策指南

### 🚨 立即備份情況
- 修改 `src/index.js`
- 更新 `package.json`
- 刪除多個文件
- 重構代碼結構

### 🔧 備份策略選擇

| 風險等級 | 備份策略 | 命令 |
|---------|---------|------|
| 🔴 極高 | 多重備份 | `git branch` + `npm run backup` + `npm run recovery:git` |
| 🟠 高風險 | 增強備份 | `npm run backup` + `git commit` |
| 🟡 中風險 | 標準備份 | `npm run backup` |
| 🟢 低風險 | Git 提交 | `git commit` |

## 📞 緊急恢復

### 🆘 如果出錯了
```bash
# 1. 停止操作，查看備份
npm run recovery:list

# 2. 恢復最近備份
npm run recovery:restore <timestamp> <filename>

# 3. 或恢復整個類別
npm run recovery:restore <timestamp> documentation-reports

# 4. Git 回滾 (最後手段)
git reset --hard HEAD~1
```

### 📱 常用恢復命令
```bash
# 恢復特定文件
npm run recovery:restore 2025-07-05T12-46-06 HONO_DEPENDENCY_FIX_REPORT.md

# 恢復所有文檔
npm run recovery:restore 2025-07-05T12-46-06 documentation-reports

# 查看可恢復文件
npm run recovery:files 2025-07-05T12-46-06
```

## 📊 每日檢查流程
```bash
# 1. 快速狀態檢查 (5 秒)
npm run status:quick

# 2. 如有問題，詳細檢查
npm run status

# 3. 重大變更前評估
npm run risk:check

# 4. 創建備份
npm run backup
```

## 🎯 最佳實踐提醒

### ✅ 做這些
- 每日開始工作前 `npm run status:quick`
- 重大變更前先 `npm run risk:check`
- 使用 `npm run safe:*` 命令
- 定期檢查 `npm run backup:list`
- 測試恢復功能

### ❌ 避免這些
- 跳過風險評估
- 忘記備份就刪除文件
- 不測試恢復功能
- 忽略風險警告

## 🔗 相關文檔
- [完整最佳實踐指南](./DEVELOPMENT_BEST_PRACTICES.md)
- [詳細備份指南](./BACKUP_RECOVERY_GUIDE.md)

---
*保持這張卡片在手邊，安全開發！*
