# 使用官方 Node.js 映像作為基礎映像
FROM node:20-alpine

# 設置工作目錄
WORKDIR /app

# 複製 package.json 和 package-lock.json
COPY package*.json ./

# 安裝依賴
RUN npm ci --only=production

# 複製源代碼
COPY . .

# 建立非特權用戶
RUN addgroup -g 1001 -S nodejs
RUN adduser -S heartsync -u 1001

# 更改檔案所有權
RUN chown -R heartsync:nodejs /app
USER heartsync

# 暴露端口
EXPOSE 3000

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 啟動應用程式
CMD ["npm", "start"]
