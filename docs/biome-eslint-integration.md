# 🔧 Biome + ESLint 整合策略

## 📋 功能重疊分析

### ✅ **Biome 已處理的功能**

| 功能 | Biome 規則 | 原 ESLint 規則 | 狀態 |
|------|------------|----------------|------|
| 模板字符串強制使用 | `useTemplate: "error"` | `prefer-template: "error"` | ✅ 由 Biome 處理 |
| 使用 const | `useConst: "error"` | `prefer-const: "error"` | ✅ 由 Biome 處理 |
| 引號風格 | `quoteStyle: "single"` | `quotes: ["error", "single"]` | ✅ 由 Biome 處理 |
| 分號 | `semicolons: "always"` | `semi: ["error", "always"]` | ✅ 由 Biome 處理 |
| 代碼格式化 | Biome Formatter | Prettier | ✅ 由 Biome 處理 |

### 🎯 **ESLint 專用規則**

| 功能 | ESLint 規則 | 說明 | 原因 |
|------|-------------|------|------|
| 模板字符串空格 | `template-curly-spacing` | 控制 `${}` 內的空格 | Biome 暫不支持 |
| 字符串中的模板語法 | `no-template-curly-in-string` | 防止 `"${var}"` 錯誤 | Biome 暫不支持 |
| 未使用變數 | `no-unused-vars` | 警告未使用的變數 | 保持現有配置 |
| Console 語句 | `no-console` | 允許 console.log | 開發需要 |
| 未聲明變數 | `no-undef` | 檢查全域變數 | 重要的安全檢查 |

## 🚀 **最佳整合策略**

### 1. **主要工具分工**

```mermaid
graph TD
    A[代碼檢查] --> B[Biome - 主要工具]
    A --> C[ESLint - 補充工具]
    
    B --> D[模板字符串基本檢查]
    B --> E[代碼格式化]
    B --> F[現代 JS 語法]
    
    C --> G[模板字符串細節]
    C --> H[專用規則]
    C --> I[全域變數檢查]
```

### 2. **npm scripts 優化**

```json
{
  "scripts": {
    "lint": "biome lint .",
    "lint:fix": "biome lint --apply .",
    "lint:eslint": "eslint src/ --ext .js",
    "lint:all": "npm run lint && npm run lint:eslint",
    
    "format": "biome format .",
    "format:fix": "biome format --write .",
    
    "check": "biome check .",
    "check:fix": "biome check --apply .",
    "check:all": "npm run check && npm run lint:eslint"
  }
}
```

### 3. **VS Code 配置建議**

```json
// .vscode/settings.json
{
  "editor.defaultFormatter": "biomejs.biome",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "quickfix.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },
  "eslint.enable": true,
  "eslint.validate": ["javascript"]
}
```

## 📊 **性能與效率對比**

### ⚡ **Biome 優勢**
- **速度**: 比 ESLint + Prettier 快 10-100 倍
- **內存**: 更低的內存占用
- **配置**: 單一配置文件
- **整合**: 內建格式化器

### 🎯 **ESLint 優勢**
- **生態系統**: 豐富的插件生態
- **專用規則**: 特定場景的細粒度控制
- **成熟度**: 更成熟的規則集
- **社區**: 大量的社區支持

## 🔧 **實際配置**

### Biome 配置 (`biome.json`)
```json
{
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true,
      "style": {
        "useConst": "error",
        "useTemplate": "error"
      },
      "correctness": {
        "noUnusedVariables": "warn"
      }
    }
  },
  "formatter": {
    "enabled": true,
    "quoteStyle": "single",
    "semicolons": "always"
  }
}
```

### ESLint 配置 (`.eslintrc.js`)
```javascript
module.exports = {
  rules: {
    // 🔧 ESLint 專用規則 (Biome 不支持的)
    'template-curly-spacing': ['error', 'never'],
    'no-template-curly-in-string': 'error',
    'no-unused-vars': 'warn',
    'no-console': 'off',
    'no-undef': 'error',
    
    // 注意：以下規則由 Biome 處理
    // 'prefer-template': 'error', // 由 Biome useTemplate 處理
    // 'prefer-const': 'error',   // 由 Biome useConst 處理
  }
};
```

## 🎯 **開發工作流程**

### 日常開發
```bash
# 1. 開發時自動格式化 (VS Code)
# 2. 提交前檢查
npm run check:all

# 3. 修復問題
npm run check:fix
npm run lint:fix
```

### CI/CD 流程
```bash
# 1. 快速檢查 (Biome)
npm run check

# 2. 完整檢查 (Biome + ESLint)
npm run check:all

# 3. 格式檢查
npm run format:check
```

## 📈 **遷移建議**

### 階段 1: 當前狀態 (已完成)
- ✅ Biome 處理主要的代碼檢查和格式化
- ✅ ESLint 處理專用規則
- ✅ 避免重複配置

### 階段 2: 優化 (下週)
- [ ] 更新 npm scripts
- [ ] 配置 VS Code 設定
- [ ] 團隊培訓

### 階段 3: 完全整合 (下月)
- [ ] 評估是否完全遷移到 Biome
- [ ] 移除不必要的 ESLint 規則
- [ ] 性能監控和優化

## 🏆 **總結**

### ✅ **最佳實踐**
1. **Biome 為主**: 處理 80% 的代碼檢查和格式化
2. **ESLint 為輔**: 處理 Biome 不支持的專用規則
3. **避免重複**: 不要在兩個工具中配置相同的規則
4. **性能優先**: 優先使用更快的 Biome

### 🎯 **具體效益**
- **構建速度**: 提升 50-80%
- **開發體驗**: 更快的即時反饋
- **維護成本**: 減少配置複雜度
- **一致性**: 統一的代碼風格

### 🔮 **未來方向**
隨著 Biome 的發展，我們可以逐步將更多規則遷移到 Biome，最終可能完全替代 ESLint。但目前的混合策略是最佳選擇，既享受了 Biome 的性能優勢，又保持了 ESLint 的靈活性。
