# 🚀 HeartSync v2 多運行時支援

HeartSync v2 現在支援多個 JavaScript 運行時環境，提供更好的開發體驗和部署選擇。

## 🎯 支援的運行時

### 1. 📦 Node.js (主要支援)
- **版本**: 18.x, 20.x, 22.x
- **包管理器**: npm
- **狀態**: ✅ 完全支援
- **用途**: 生產部署、開發環境

```bash
# 安裝依賴
npm install

# 開發模式
npm run dev

# 生產模式
npm run start
```

### 2. 🚀 Bun (高性能支援)
- **版本**: latest, canary
- **包管理器**: bun
- **狀態**: ✅ 完全支援
- **用途**: 快速開發、性能測試

```bash
# 安裝依賴
bun install

# 開發模式
bun run dev:bun

# 生產模式
bun run start:bun
```

### 3. 🦕 Deno (實驗性支援)
- **版本**: v1.x, v2.x
- **包管理器**: 內建
- **狀態**: 🧪 實驗性
- **用途**: 未來遷移、安全性測試

```bash
# 開發模式
deno task dev

# 生產模式
deno task start

# 代碼檢查
deno task check
```

## 🔧 CI/CD 支援

### GitHub Actions 工作流程

我們的 CI/CD 流程會在所有支援的運行時上測試代碼：

1. **Node.js 測試** - 在 3 個版本上測試
2. **Bun 測試** - 在 latest 和 canary 版本上測試
3. **Deno 測試** - 實驗性，不會阻止部署

### 測試矩陣

```yaml
strategy:
  matrix:
    # Node.js
    node-version: [18.x, 20.x, 22.x]
    
    # Bun
    bun-version: [latest, canary]
    
    # Deno (實驗性)
    deno-version: [v1.x, v2.x]
```

## 📊 性能比較

| 運行時 | 啟動時間 | 記憶體使用 | 包安裝速度 | 生產就緒 |
|--------|----------|------------|------------|----------|
| Node.js | 中等 | 中等 | 中等 | ✅ 是 |
| Bun | 快 | 低 | 非常快 | ✅ 是 |
| Deno | 快 | 低 | 快 | 🧪 實驗性 |

## 🎯 何時使用哪個運行時

### Node.js 適合：
- 生產部署
- 穩定性要求高的環境
- 需要完整生態系統支援

### Bun 適合：
- 開發環境
- 需要快速啟動和安裝
- 性能測試和優化

### Deno 適合：
- 安全性測試
- 未來技術探索
- TypeScript 原生支援需求

## 🔄 遷移指南

### 從 Node.js 到 Bun
1. 確保 `package.json` 兼容
2. 使用 `bun install` 安裝依賴
3. 測試所有功能
4. 更新部署腳本

### 準備 Deno 支援
1. 檢查 `deno.json` 配置
2. 更新導入語法（使用 URL 導入）
3. 處理權限要求
4. 測試兼容性

## 🛠️ 開發建議

### 多運行時兼容性
- 使用標準 Web APIs
- 避免運行時特定功能
- 測試所有目標運行時
- 保持依賴最小化

### 最佳實踐
- 在 CI/CD 中測試所有運行時
- 使用 `package.json` 作為主要配置
- 保持 `deno.json` 同步更新
- 監控性能差異

## 🚀 未來計劃

### 短期目標
- ✅ 完善 Bun 支援
- 🔄 優化 CI/CD 流程
- 📊 性能基準測試

### 長期目標
- 🦕 完整 Deno 支援
- 🔧 運行時自動選擇
- 📈 性能監控儀表板

---

**更新時間**: 2025-01-18  
**支援狀態**: Node.js ✅ | Bun ✅ | Deno 🧪
