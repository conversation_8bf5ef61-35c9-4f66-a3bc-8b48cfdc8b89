# 🚀 HSN CLI Preview - Railway 部署配置
# 測試部署階段配置文件

[build]
builder = "nixpacks"
buildCommand = "npm install"

[deploy]
healthcheckPath = "/api/health"
healthcheckTimeout = 300
startCommand = "npm run start:railway"
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

# 環境變數 (在 Railway Dashboard 中設置)
[env]
NODE_ENV = { default = "production" }
PORT = { default = "3000" }
ENVIRONMENT = { default = "production" }

# 這些需要在 Railway Dashboard 中設置為環境變數
# CLAUDE_API_KEY (secret)
# OPENROUTER_API_KEY (secret)
# TAVILY_API_KEY (secret)

# 網路配置
[networking]
serviceDomain = "hsn-cli-preview"

# 資源配置 (測試階段使用基本配置)
[resources]
memory = "512Mi"
cpu = "0.5"

# 監控配置
[monitoring]
enabled = true
